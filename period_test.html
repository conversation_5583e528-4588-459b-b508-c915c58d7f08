<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间段功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        .period-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .period-btn {
            padding: 10px 20px;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .period-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .period-btn:hover {
            background: #e9ecef;
        }
        .period-btn.active:hover {
            background: #0056b3;
        }
        .test-results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        .result-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .result-label {
            font-weight: bold;
            color: #333;
        }
        .result-value {
            color: #666;
            margin-left: 10px;
        }
        .loading {
            text-align: center;
            color: #666;
            padding: 20px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-title">🔍 时间段功能测试</div>
        <p>测试不同时间段参数是否正确传递和处理</p>
        
        <div class="period-buttons">
            <button class="period-btn" onclick="testPeriod('1d')">1天</button>
            <button class="period-btn active" onclick="testPeriod('1w')">1周</button>
            <button class="period-btn" onclick="testPeriod('1mo')">1个月</button>
            <button class="period-btn" onclick="testPeriod('3mo')">3个月</button>
            <button class="period-btn" onclick="testPeriod('6mo')">6个月</button>
            <button class="period-btn" onclick="testPeriod('1y')">1年</button>
        </div>
        
        <div class="test-results" id="testResults">
            <div class="loading">点击上方按钮测试不同时间段...</div>
        </div>
    </div>

    <script>
        let currentPeriod = '1w';

        function testPeriod(period) {
            currentPeriod = period;
            
            // 更新按钮状态
            document.querySelectorAll('.period-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 显示加载状态
            document.getElementById('testResults').innerHTML = '<div class="loading">🔄 正在测试 ' + getPeriodText(period) + ' 数据...</div>';
            
            // 测试API调用
            testStockData('AAPL', period);
        }

        async function testStockData(symbol, period) {
            try {
                const url = `http://localhost:8080/api/stock/${symbol}?period=${period}`;
                console.log('测试URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success) {
                    displayTestResults(data.stock_data, period);
                } else {
                    showError('API返回错误: ' + (data.error || '未知错误'));
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
                console.error('测试失败:', error);
            }
        }

        function displayTestResults(stockData, period) {
            const periodText = getPeriodText(period);
            
            let resultsHTML = `
                <div class="success">✅ ${periodText} 数据获取成功</div>
                
                <div class="result-item">
                    <span class="result-label">股票代码:</span>
                    <span class="result-value">${stockData.symbol}</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">公司名称:</span>
                    <span class="result-value">${stockData.name}</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">请求时间段:</span>
                    <span class="result-value">${period} (${periodText})</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">返回时间段:</span>
                    <span class="result-value">${stockData.period || '未设置'}</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">当前价格:</span>
                    <span class="result-value">$${stockData.current_price}</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">日涨跌:</span>
                    <span class="result-value" style="color: ${(stockData.daily_change_percent || 0) >= 0 ? '#28a745' : '#dc3545'};">
                        ${(stockData.daily_change || 0).toFixed(2)} (${((stockData.daily_change_percent || 0) >= 0 ? '+' : '')}${(stockData.daily_change_percent || 0).toFixed(2)}%)
                    </span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">${periodText}涨跌:</span>
                    <span class="result-value" style="color: ${(stockData.period_change_percent || 0) >= 0 ? '#28a745' : '#dc3545'};">
                        ${(stockData.period_change || 0).toFixed(2)} (${((stockData.period_change_percent || 0) >= 0 ? '+' : '')}${(stockData.period_change_percent || 0).toFixed(2)}%)
                    </span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">${periodText}最高:</span>
                    <span class="result-value">$${stockData.period_high || '未设置'}</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">${periodText}最低:</span>
                    <span class="result-value">$${stockData.period_low || '未设置'}</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">历史数据点数:</span>
                    <span class="result-value">${stockData.historical_data ? stockData.historical_data.length : 0} 个交易日</span>
                </div>
                
                <div class="result-item">
                    <span class="result-label">数据时间范围:</span>
                    <span class="result-value">
                        ${stockData.historical_data && stockData.historical_data.length > 0 ? 
                          stockData.historical_data[0].date + ' 至 ' + stockData.historical_data[stockData.historical_data.length - 1].date : 
                          '无数据'}
                    </span>
                </div>
            `;
            
            // 验证数据一致性
            if (stockData.period === period) {
                resultsHTML += '<div class="success">✅ 时间段参数传递正确</div>';
            } else {
                resultsHTML += '<div class="error">❌ 时间段参数不匹配: 请求' + period + '，返回' + (stockData.period || '未设置') + '</div>';
            }
            
            // 验证期间变化数据
            if (stockData.period_change !== undefined && stockData.period_change_percent !== undefined) {
                resultsHTML += '<div class="success">✅ 期间变化数据完整</div>';
            } else {
                resultsHTML += '<div class="error">❌ 期间变化数据缺失</div>';
            }
            
            // 验证历史数据
            const expectedDataPoints = getExpectedDataPoints(period);
            const actualDataPoints = stockData.historical_data ? stockData.historical_data.length : 0;
            
            if (actualDataPoints > 0) {
                resultsHTML += `<div class="success">✅ 历史数据获取成功 (${actualDataPoints} 个数据点)</div>`;
                if (actualDataPoints >= expectedDataPoints * 0.7) {
                    resultsHTML += '<div class="success">✅ 数据点数量合理</div>';
                } else {
                    resultsHTML += `<div class="error">⚠️ 数据点数量偏少 (期望约${expectedDataPoints}个，实际${actualDataPoints}个)</div>`;
                }
            } else {
                resultsHTML += '<div class="error">❌ 历史数据缺失</div>';
            }
            
            document.getElementById('testResults').innerHTML = resultsHTML;
        }

        function showError(message) {
            document.getElementById('testResults').innerHTML = `<div class="error">❌ ${message}</div>`;
        }

        function getPeriodText(period) {
            const periodMap = {
                '1d': '1天',
                '1w': '1周',
                '1mo': '1个月',
                '3mo': '3个月',
                '6mo': '6个月',
                '1y': '1年'
            };
            return periodMap[period] || period;
        }

        function getExpectedDataPoints(period) {
            const expectedMap = {
                '1d': 1,      // 1天约1个数据点
                '1w': 5,      // 1周约5个交易日
                '1mo': 22,    // 1个月约22个交易日
                '3mo': 65,    // 3个月约65个交易日
                '6mo': 130,   // 6个月约130个交易日
                '1y': 252     // 1年约252个交易日
            };
            return expectedMap[period] || 30;
        }

        // 页面加载后自动测试1周数据
        window.addEventListener('load', function() {
            setTimeout(() => {
                testPeriod('1w');
            }, 1000);
        });
    </script>
</body>
</html>
