<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组合图表功能演示</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .chart-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .chart-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .chart-checkboxes {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        .chart-checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: background 0.3s ease;
        }
        .chart-checkbox:hover {
            background: #e9ecef;
        }
        .chart-checkbox input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
            cursor: pointer;
        }
        .checkbox-label {
            font-size: 0.95em;
            font-weight: 500;
            color: #333;
            cursor: pointer;
        }
        .chart-actions {
            display: flex;
            gap: 10px;
        }
        .chart-btn {
            padding: 6px 12px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.85em;
            transition: background 0.3s ease;
        }
        .chart-btn:hover {
            background: #5a6268;
        }
        .combined-chart-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .chart-canvas {
            height: 500px;
            width: 100%;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .stock-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .stock-btn {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }
        .stock-btn:hover {
            background: #218838;
        }
        .stock-btn.active {
            background: #dc3545;
        }
        .feature-highlight {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        .feature-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }
        .info-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .info-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔄 组合图表功能演示</h1>
        <p>价格、K线和成交量在一个图表中同时显示</p>
    </div>

    <div class="demo-section">
        <div class="section-title">🎯 功能特色</div>
        <div class="feature-highlight">
            <div class="feature-title">🔄 一图多显</div>
            <div>将价格走势、K线数据和成交量信息整合在一个图表中，便于综合分析和对比观察。</div>
        </div>
        <div class="feature-highlight">
            <div class="feature-title">📊 双Y轴设计</div>
            <div>左侧Y轴显示价格信息，右侧Y轴显示成交量，避免数据范围差异影响观察效果。</div>
        </div>
        <div class="feature-highlight">
            <div class="feature-title">🎨 智能着色</div>
            <div>根据涨跌情况自动着色，绿色表示上涨，红色表示下跌，视觉效果清晰直观。</div>
        </div>
        <div class="info-box">
            <div class="info-title">💡 使用提示</div>
            <div>
                • 可以单独选择显示价格线、K线柱或成交量<br>
                • 建议选择"价格线 + 成交量"组合，观察效果最佳<br>
                • K线柱包含开盘、收盘、最高、最低价信息
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="section-title">🎯 股票选择</div>
        <div class="stock-selector">
            <button class="stock-btn active" onclick="loadStockChart('AAPL')">AAPL - Apple</button>
            <button class="stock-btn" onclick="loadStockChart('MSFT')">MSFT - Microsoft</button>
            <button class="stock-btn" onclick="loadStockChart('GOOGL')">GOOGL - Google</button>
            <button class="stock-btn" onclick="loadStockChart('TSLA')">TSLA - Tesla</button>
            <button class="stock-btn" onclick="loadStockChart('AMZN')">AMZN - Amazon</button>
            <button class="stock-btn" onclick="loadStockChart('META')">META - Meta</button>
        </div>
    </div>

    <div class="demo-section">
        <div class="section-title">🔄 组合图表控制</div>
        <div class="chart-controls">
            <div class="chart-title">📊 数据显示选项</div>
            <div class="chart-checkboxes">
                <label class="chart-checkbox">
                    <input type="checkbox" id="showPrice" checked onchange="updateCombinedChart()">
                    <span class="checkbox-label">📈 价格线</span>
                </label>
                <label class="chart-checkbox">
                    <input type="checkbox" id="showCandlestick" onchange="updateCombinedChart()">
                    <span class="checkbox-label">🕯️ K线柱</span>
                </label>
                <label class="chart-checkbox">
                    <input type="checkbox" id="showVolume" checked onchange="updateCombinedChart()">
                    <span class="checkbox-label">📊 成交量</span>
                </label>
            </div>
            <div class="chart-actions">
                <button class="chart-btn" onclick="selectAll()">全选</button>
                <button class="chart-btn" onclick="clearAll()">清空</button>
                <button class="chart-btn" onclick="resetDefault()">推荐组合</button>
            </div>
        </div>
        
        <div class="combined-chart-container">
            <canvas id="combinedChart" class="chart-canvas"></canvas>
        </div>
    </div>

    <script>
        let currentChart = null;
        let currentStockData = null;
        let currentSymbol = 'AAPL';

        // 页面加载完成后自动加载AAPL数据
        window.addEventListener('load', function() {
            loadStockChart('AAPL');
        });

        // 加载股票图表
        async function loadStockChart(symbol) {
            currentSymbol = symbol;
            
            // 更新按钮状态
            document.querySelectorAll('.stock-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            try {
                const response = await fetch(`http://localhost:8080/api/stock/${symbol}`);
                const data = await response.json();
                
                if (response.ok) {
                    currentStockData = data.stock_data;
                    updateCombinedChart();
                } else {
                    console.error('获取股票数据失败:', data.error);
                }
            } catch (error) {
                console.error('网络错误:', error);
            }
        }

        // 更新组合图表
        function updateCombinedChart() {
            if (!currentStockData) return;
            
            const showPrice = document.getElementById('showPrice').checked;
            const showCandlestick = document.getElementById('showCandlestick').checked;
            const showVolume = document.getElementById('showVolume').checked;
            
            createCombinedChart(showPrice, showCandlestick, showVolume);
        }

        // 全选
        function selectAll() {
            document.getElementById('showPrice').checked = true;
            document.getElementById('showCandlestick').checked = true;
            document.getElementById('showVolume').checked = true;
            updateCombinedChart();
        }

        // 清空
        function clearAll() {
            document.getElementById('showPrice').checked = false;
            document.getElementById('showCandlestick').checked = false;
            document.getElementById('showVolume').checked = false;
            updateCombinedChart();
        }

        // 推荐组合
        function resetDefault() {
            document.getElementById('showPrice').checked = true;
            document.getElementById('showCandlestick').checked = false;
            document.getElementById('showVolume').checked = true;
            updateCombinedChart();
        }

        // 创建组合图表
        function createCombinedChart(showPrice, showCandlestick, showVolume) {
            const chartCanvas = document.getElementById('combinedChart');
            if (!chartCanvas || !currentStockData) return;
            
            try {
                const ctx = chartCanvas.getContext('2d');
                
                // 销毁现有图表
                if (currentChart) {
                    currentChart.destroy();
                    currentChart = null;
                }
                
                const historicalData = currentStockData.historical_data || [];
                if (historicalData.length === 0) return;
                
                const labels = historicalData.map(item => item.date);
                const datasets = [];
                
                // 价格线数据集
                if (showPrice) {
                    datasets.push({
                        label: '收盘价',
                        data: historicalData.map(item => item.close),
                        type: 'line',
                        borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: 'transparent',
                        borderWidth: 3,
                        pointRadius: 3,
                        pointHoverRadius: 6,
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y'
                    });
                }
                
                // K线柱状图数据集
                if (showCandlestick) {
                    // 收盘价柱状图
                    datasets.push({
                        label: 'K线收盘',
                        data: historicalData.map(item => item.close),
                        type: 'bar',
                        backgroundColor: historicalData.map(item => 
                            item.close >= item.open ? 'rgba(40, 167, 69, 0.6)' : 'rgba(220, 53, 69, 0.6)'
                        ),
                        borderColor: historicalData.map(item => 
                            item.close >= item.open ? '#28a745' : '#dc3545'
                        ),
                        borderWidth: 1,
                        barThickness: 8,
                        yAxisID: 'y'
                    });
                    
                    // 最高价线
                    datasets.push({
                        label: '最高价',
                        data: historicalData.map(item => item.high),
                        type: 'line',
                        borderColor: '#17a2b8',
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        pointRadius: 1,
                        fill: false,
                        yAxisID: 'y'
                    });
                    
                    // 最低价线
                    datasets.push({
                        label: '最低价',
                        data: historicalData.map(item => item.low),
                        type: 'line',
                        borderColor: '#ffc107',
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        pointRadius: 1,
                        fill: false,
                        yAxisID: 'y'
                    });
                }
                
                // 成交量数据集
                if (showVolume) {
                    datasets.push({
                        label: '成交量',
                        data: historicalData.map(item => item.volume),
                        type: 'bar',
                        backgroundColor: historicalData.map(item => 
                            item.close >= item.open ? 'rgba(40, 167, 69, 0.3)' : 'rgba(220, 53, 69, 0.3)'
                        ),
                        borderColor: historicalData.map(item => 
                            item.close >= item.open ? '#28a745' : '#dc3545'
                        ),
                        borderWidth: 1,
                        barThickness: 6,
                        yAxisID: 'y1'
                    });
                }
                
                currentChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `${currentStockData.name} (${currentStockData.symbol}) - 组合图表分析`,
                                font: { size: 18, weight: 'bold' }
                            },
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    padding: 20
                                }
                            }
                        },
                        scales: {
                            x: {
                                type: 'time',
                                time: {
                                    unit: 'day',
                                    displayFormats: {
                                        day: 'MM/dd'
                                    }
                                },
                                title: {
                                    display: true,
                                    text: '日期',
                                    font: { size: 14, weight: 'bold' }
                                }
                            },
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: {
                                    display: true,
                                    text: '价格 ($)',
                                    font: { size: 14, weight: 'bold' }
                                },
                                beginAtZero: false
                            },
                            y1: {
                                type: 'linear',
                                display: showVolume,
                                position: 'right',
                                title: {
                                    display: true,
                                    text: '成交量',
                                    font: { size: 14, weight: 'bold' }
                                },
                                beginAtZero: true,
                                grid: {
                                    drawOnChartArea: false
                                },
                                ticks: {
                                    callback: function(value) {
                                        if (value >= 1000000000) return (value/1000000000).toFixed(1) + 'B';
                                        if (value >= 1000000) return (value/1000000).toFixed(1) + 'M';
                                        if (value >= 1000) return (value/1000).toFixed(1) + 'K';
                                        return value.toString();
                                    }
                                }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        }
                    }
                });
            } catch (error) {
                console.error('创建组合图表时出错:', error);
            }
        }
    </script>
</body>
</html>
