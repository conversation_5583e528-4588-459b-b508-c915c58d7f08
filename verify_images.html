<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证景点图片</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .image-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .image-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .image-container {
            width: 100%;
            height: 150px;
            position: relative;
            background: #f0f0f0;
        }
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .image-info {
            padding: 15px;
        }
        .image-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .image-url {
            font-size: 0.8em;
            color: #666;
            word-break: break-all;
            margin-bottom: 10px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .loading { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🖼️ 验证景点图片链接</h1>
    
    <div id="imageTests"></div>

    <script>
        // 当前使用的图片链接
        const imageUrls = [
            {name: "故宫", url: "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2c/Forbidden_City_Beijing_Shenwumen_Gate.JPG/400px-Forbidden_City_Beijing_Shenwumen_Gate.JPG"},
            {name: "长城", url: "https://upload.wikimedia.org/wikipedia/commons/thumb/1/10/20090529_Great_Wall_8185.jpg/400px-20090529_Great_Wall_8185.jpg"},
            {name: "天坛", url: "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a2/Temple_of_Heaven%2C_Beijing%2C_China.jpg/400px-Temple_of_Heaven%2C_Beijing%2C_China.jpg"},
            {name: "西湖", url: "https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Hangzhou_Xihu.jpg/400px-Hangzhou_Xihu.jpg"},
            {name: "兵马俑", url: "https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Terracotta_Army_Pit_1_-_14-11-2014.jpg/400px-Terracotta_Army_Pit_1_-_14-11-2014.jpg"}
        ];

        function createImageTest() {
            const container = document.getElementById('imageTests');
            
            imageUrls.forEach(item => {
                const card = document.createElement('div');
                card.className = 'image-card';
                
                card.innerHTML = `
                    <div class="image-container">
                        <img src="${item.url}" alt="${item.name}" 
                             onload="updateStatus(this, 'success')" 
                             onerror="updateStatus(this, 'error')">
                    </div>
                    <div class="image-info">
                        <div class="image-name">${item.name}</div>
                        <div class="image-url">${item.url}</div>
                        <div class="status loading">⏳ 加载中...</div>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }

        function updateStatus(img, status) {
            const statusDiv = img.closest('.image-card').querySelector('.status');
            
            if (status === 'success') {
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 加载成功';
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 加载失败';
            }
        }

        // 创建测试
        createImageTest();

        // 统计结果
        setTimeout(() => {
            const allImages = document.querySelectorAll('img');
            let loaded = 0, failed = 0;
            
            allImages.forEach(img => {
                if (img.complete) {
                    if (img.naturalWidth > 0) {
                        loaded++;
                    } else {
                        failed++;
                    }
                }
            });
            
            console.log(`图片加载统计: 成功 ${loaded} 张, 失败 ${failed} 张`);
            
            // 显示统计结果
            const summary = document.createElement('div');
            summary.style.cssText = 'background: white; padding: 20px; border-radius: 10px; margin-top: 20px; text-align: center; font-weight: bold;';
            summary.innerHTML = `📊 加载统计: 成功 ${loaded} 张, 失败 ${failed} 张`;
            document.body.appendChild(summary);
        }, 5000);
    </script>
</body>
</html>
