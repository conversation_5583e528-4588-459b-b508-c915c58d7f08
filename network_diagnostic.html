<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络连接诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .diagnostic-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-name {
            font-weight: 500;
        }
        .test-status {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 500;
        }
        .status-testing {
            background: #fff3cd;
            color: #856404;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .status-pending {
            background: #e2e3e5;
            color: #383d41;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .log-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
        }
        .log-entry {
            margin: 5px 0;
            padding: 3px 0;
        }
        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .solution-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        .solution-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>🔧 网络连接诊断工具</h1>
    <p>如果您遇到"网络错误，请稍后重试"的问题，请使用此工具进行诊断</p>

    <div class="diagnostic-section">
        <div class="section-title">🎯 快速诊断</div>
        <button onclick="runAllTests()" id="runAllBtn">🔄 运行所有测试</button>
        <button onclick="clearLogs()">🗑️ 清空日志</button>
        
        <div style="margin-top: 20px;">
            <div class="test-item">
                <div class="test-name">服务器连接测试</div>
                <div class="test-status status-pending" id="serverStatus">等待测试</div>
            </div>
            <div class="test-item">
                <div class="test-name">API端点测试</div>
                <div class="test-status status-pending" id="apiStatus">等待测试</div>
            </div>
            <div class="test-item">
                <div class="test-name">股票数据获取测试</div>
                <div class="test-status status-pending" id="stockStatus">等待测试</div>
            </div>
            <div class="test-item">
                <div class="test-name">CORS跨域测试</div>
                <div class="test-status status-pending" id="corsStatus">等待测试</div>
            </div>
            <div class="test-item">
                <div class="test-name">网络延迟测试</div>
                <div class="test-status status-pending" id="latencyStatus">等待测试</div>
            </div>
        </div>
    </div>

    <div class="diagnostic-section">
        <div class="section-title">📋 诊断日志</div>
        <div class="log-section" id="logContainer">
            <div class="log-entry log-info">点击"运行所有测试"开始诊断...</div>
        </div>
    </div>

    <div class="diagnostic-section">
        <div class="section-title">💡 常见问题解决方案</div>
        
        <div class="solution-box">
            <div class="solution-title">问题1: 服务器未启动</div>
            <div>
                <strong>症状:</strong> 无法连接到服务器<br>
                <strong>解决:</strong> 确保在终端中运行了 <code>python run_web.py</code><br>
                <strong>验证:</strong> 浏览器访问 <a href="http://localhost:8080" target="_blank">http://localhost:8080</a>
            </div>
        </div>

        <div class="solution-box">
            <div class="solution-title">问题2: 端口被占用</div>
            <div>
                <strong>症状:</strong> 服务器启动失败，提示端口被占用<br>
                <strong>解决:</strong> 
                <ul>
                    <li>杀死占用端口的进程: <code>lsof -ti:8080 | xargs kill -9</code></li>
                    <li>或者修改端口: 在 <code>run_web.py</code> 中改为其他端口</li>
                </ul>
            </div>
        </div>

        <div class="solution-box">
            <div class="solution-title">问题3: 防火墙阻止</div>
            <div>
                <strong>症状:</strong> 服务器运行但无法访问<br>
                <strong>解决:</strong> 
                <ul>
                    <li>检查系统防火墙设置</li>
                    <li>允许Python应用访问网络</li>
                    <li>尝试使用 127.0.0.1:8080 而不是 localhost:8080</li>
                </ul>
            </div>
        </div>

        <div class="solution-box">
            <div class="solution-title">问题4: 浏览器缓存问题</div>
            <div>
                <strong>症状:</strong> 页面显示旧内容或加载失败<br>
                <strong>解决:</strong> 
                <ul>
                    <li>硬刷新页面: Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac)</li>
                    <li>清除浏览器缓存</li>
                    <li>尝试无痕/隐私模式</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let testRunning = false;

        // 日志记录函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新测试状态
        function updateStatus(testId, status, message) {
            const element = document.getElementById(testId);
            element.className = `test-status status-${status}`;
            element.textContent = message;
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = 
                '<div class="log-entry log-info">日志已清空，准备新的测试...</div>';
        }

        // 运行所有测试
        async function runAllTests() {
            if (testRunning) return;
            
            testRunning = true;
            document.getElementById('runAllBtn').disabled = true;
            
            log('开始网络诊断测试...', 'info');
            
            // 重置所有状态
            ['serverStatus', 'apiStatus', 'stockStatus', 'corsStatus', 'latencyStatus'].forEach(id => {
                updateStatus(id, 'pending', '等待测试');
            });

            try {
                await testServerConnection();
                await testApiEndpoint();
                await testStockData();
                await testCORS();
                await testLatency();
                
                log('所有测试完成！', 'success');
            } catch (error) {
                log(`测试过程中出现错误: ${error.message}`, 'error');
            } finally {
                testRunning = false;
                document.getElementById('runAllBtn').disabled = false;
            }
        }

        // 测试服务器连接
        async function testServerConnection() {
            updateStatus('serverStatus', 'testing', '测试中...');
            log('测试服务器连接...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    updateStatus('serverStatus', 'success', '连接成功');
                    log('✅ 服务器连接正常', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('serverStatus', 'error', '连接失败');
                log(`❌ 服务器连接失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // 测试API端点
        async function testApiEndpoint() {
            updateStatus('apiStatus', 'testing', '测试中...');
            log('测试API端点...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/api/stocks/popular', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('apiStatus', 'success', 'API正常');
                    log(`✅ API端点正常，返回数据: ${JSON.stringify(data).substring(0, 100)}...`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('apiStatus', 'error', 'API失败');
                log(`❌ API端点测试失败: ${error.message}`, 'error');
            }
        }

        // 测试股票数据
        async function testStockData() {
            updateStatus('stockStatus', 'testing', '测试中...');
            log('测试股票数据获取...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/api/stock/AAPL', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.stock_data && data.stock_data.symbol) {
                        updateStatus('stockStatus', 'success', '数据正常');
                        log(`✅ 股票数据获取成功: ${data.stock_data.symbol} - $${data.stock_data.current_price}`, 'success');
                    } else {
                        throw new Error('数据格式不正确');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('stockStatus', 'error', '数据失败');
                log(`❌ 股票数据获取失败: ${error.message}`, 'error');
            }
        }

        // 测试CORS
        async function testCORS() {
            updateStatus('corsStatus', 'testing', '测试中...');
            log('测试CORS跨域设置...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/api/stock/MSFT', {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    updateStatus('corsStatus', 'success', 'CORS正常');
                    log('✅ CORS跨域设置正常', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('corsStatus', 'error', 'CORS失败');
                log(`❌ CORS测试失败: ${error.message}`, 'error');
            }
        }

        // 测试网络延迟
        async function testLatency() {
            updateStatus('latencyStatus', 'testing', '测试中...');
            log('测试网络延迟...', 'info');
            
            try {
                const startTime = performance.now();
                const response = await fetch('http://localhost:8080/api/stocks/trending', {
                    method: 'GET'
                });
                const endTime = performance.now();
                const latency = Math.round(endTime - startTime);
                
                if (response.ok) {
                    if (latency < 1000) {
                        updateStatus('latencyStatus', 'success', `${latency}ms`);
                        log(`✅ 网络延迟正常: ${latency}ms`, 'success');
                    } else {
                        updateStatus('latencyStatus', 'warning', `${latency}ms (慢)`);
                        log(`⚠️ 网络延迟较高: ${latency}ms`, 'warning');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('latencyStatus', 'error', '延迟测试失败');
                log(`❌ 延迟测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后显示当前URL信息
        window.addEventListener('load', function() {
            log(`当前页面URL: ${window.location.href}`, 'info');
            log(`目标服务器: http://localhost:8080`, 'info');
            log('准备就绪，点击"运行所有测试"开始诊断', 'info');
        });
    </script>
</body>
</html>
