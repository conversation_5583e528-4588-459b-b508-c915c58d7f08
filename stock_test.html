<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美股分析平台测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .stock-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .stock-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }
        .stock-symbol {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .stock-name {
            color: #666;
            margin-bottom: 10px;
        }
        .stock-price {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .price-up { color: #28a745; }
        .price-down { color: #dc3545; }
        .price-neutral { color: #6c757d; }
        .news-item {
            border-left: 3px solid #007bff;
            padding: 10px 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }
        .news-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .news-summary {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        .news-meta {
            font-size: 0.8em;
            color: #999;
        }
        .sentiment-positive { color: #28a745; }
        .sentiment-negative { color: #dc3545; }
        .sentiment-neutral { color: #6c757d; }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            color: #dc3545;
            padding: 10px;
            background: #f8d7da;
            border-radius: 5px;
        }
        .success {
            color: #155724;
            padding: 10px;
            background: #d4edda;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .metric {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 10px;
            background: #e9ecef;
            border-radius: 5px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>📈 美股分析平台功能测试</h1>
    
    <div class="test-section">
        <div class="test-title">🔍 API测试控制台</div>
        <button onclick="testSingleStock()">测试单个股票 (AAPL)</button>
        <button onclick="testMultipleStocks()">测试多个股票</button>
        <button onclick="testTrendingStocks()">测试热门股票</button>
        <button onclick="testStockSearch()">测试股票搜索</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="test-section">
        <div class="test-title">📊 单个股票详细分析</div>
        <div id="singleStockResult" class="loading">点击"测试单个股票"按钮开始测试</div>
    </div>

    <div class="test-section">
        <div class="test-title">📈 多股票对比</div>
        <div id="multipleStocksResult" class="loading">点击"测试多个股票"按钮开始测试</div>
    </div>

    <div class="test-section">
        <div class="test-title">🔥 热门股票趋势</div>
        <div id="trendingStocksResult" class="loading">点击"测试热门股票"按钮开始测试</div>
    </div>

    <div class="test-section">
        <div class="test-title">🔍 股票搜索功能</div>
        <div id="searchResult" class="loading">点击"测试股票搜索"按钮开始测试</div>
    </div>

    <script>
        // 测试单个股票
        async function testSingleStock() {
            const container = document.getElementById('singleStockResult');
            container.innerHTML = '<div class="loading">🔄 正在获取AAPL股票数据...</div>';
            
            try {
                const response = await fetch('/api/stock/AAPL');
                const data = await response.json();
                
                if (response.ok) {
                    displaySingleStockData(data, container);
                } else {
                    container.innerHTML = `<div class="error">❌ 错误: ${data.error}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        // 显示单个股票数据
        function displaySingleStockData(data, container) {
            const stock = data.stock_data;
            const recommendation = data.investment_recommendation;
            const sentiment = data.sentiment_analysis;
            const technical = data.technical_analysis;
            
            const priceChangeClass = stock.price_change >= 0 ? 'price-up' : 'price-down';
            const priceChangeSign = stock.price_change >= 0 ? '+' : '';
            
            let html = `
                <div class="success">✅ 数据获取成功</div>
                <div class="stock-card">
                    <div class="stock-symbol">${stock.symbol}</div>
                    <div class="stock-name">${stock.name}</div>
                    <div class="stock-price ${priceChangeClass}">
                        $${stock.current_price} 
                        (${priceChangeSign}${stock.price_change} / ${priceChangeSign}${stock.price_change_percent}%)
                    </div>
                    
                    <div style="margin: 15px 0;">
                        <div class="metric">行业: ${stock.sector}</div>
                        <div class="metric">市值: ${formatNumber(stock.market_cap)}</div>
                        <div class="metric">P/E: ${stock.pe_ratio || 'N/A'}</div>
                        <div class="metric">Beta: ${stock.beta || 'N/A'}</div>
                        <div class="metric">成交量: ${formatNumber(stock.volume)}</div>
                    </div>
                    
                    <div style="margin: 15px 0;">
                        <strong>投资建议:</strong> 
                        <span style="color: ${recommendation.color}">${recommendation.recommendation}</span>
                        (评分: ${recommendation.score}/100)
                    </div>
                    
                    <div style="margin: 15px 0;">
                        <strong>市场情绪:</strong> 
                        <span class="sentiment-${sentiment.overall_sentiment}">
                            ${sentiment.overall_sentiment === 'positive' ? '积极' : 
                              sentiment.overall_sentiment === 'negative' ? '消极' : '中性'}
                        </span>
                        (${sentiment.sentiment_score}/100)
                    </div>
                    
                    <div style="margin: 15px 0;">
                        <strong>技术分析:</strong> ${technical.signal_text}
                    </div>
                </div>
                
                <h4>📰 相关新闻 (${data.news.length}条)</h4>
            `;
            
            data.news.forEach(news => {
                html += `
                    <div class="news-item">
                        <div class="news-title">${news.title}</div>
                        <div class="news-summary">${news.summary}</div>
                        <div class="news-meta">
                            ${news.source} • ${formatTime(news.published_time)} • 
                            <span class="sentiment-${news.sentiment}">
                                ${news.sentiment === 'positive' ? '积极' : 
                                  news.sentiment === 'negative' ? '消极' : '中性'}
                            </span>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 测试多个股票
        async function testMultipleStocks() {
            const container = document.getElementById('multipleStocksResult');
            container.innerHTML = '<div class="loading">🔄 正在获取多个股票数据...</div>';
            
            const symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA'];
            const promises = symbols.map(symbol => 
                fetch(`/api/stock/${symbol}`).then(res => res.json())
            );
            
            try {
                const results = await Promise.all(promises);
                displayMultipleStocks(results, container);
            } catch (error) {
                container.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        // 显示多个股票
        function displayMultipleStocks(results, container) {
            let html = '<div class="success">✅ 多股票数据获取成功</div><div class="stock-grid">';
            
            results.forEach(data => {
                if (data.stock_data) {
                    const stock = data.stock_data;
                    const recommendation = data.investment_recommendation;
                    const priceChangeClass = stock.price_change >= 0 ? 'price-up' : 'price-down';
                    const priceChangeSign = stock.price_change >= 0 ? '+' : '';
                    
                    html += `
                        <div class="stock-card">
                            <div class="stock-symbol">${stock.symbol}</div>
                            <div class="stock-name">${stock.name}</div>
                            <div class="stock-price ${priceChangeClass}">
                                $${stock.current_price}
                            </div>
                            <div class="${priceChangeClass}">
                                ${priceChangeSign}${stock.price_change} (${priceChangeSign}${stock.price_change_percent}%)
                            </div>
                            <div style="margin-top: 10px;">
                                <strong>建议:</strong> 
                                <span style="color: ${recommendation.color}">${recommendation.recommendation}</span>
                            </div>
                            <div class="metric">新闻: ${data.news.length}条</div>
                        </div>
                    `;
                }
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        // 测试热门股票
        async function testTrendingStocks() {
            const container = document.getElementById('trendingStocksResult');
            container.innerHTML = '<div class="loading">🔄 正在获取热门股票...</div>';
            
            try {
                const response = await fetch('/api/stocks/trending');
                const data = await response.json();
                
                if (response.ok) {
                    displayTrendingStocks(data.trending_stocks, container);
                } else {
                    container.innerHTML = `<div class="error">❌ 错误: ${data.error}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        // 显示热门股票
        function displayTrendingStocks(stocks, container) {
            let html = '<div class="success">✅ 热门股票数据获取成功</div><div class="stock-grid">';
            
            stocks.forEach((stock, index) => {
                const priceChangeClass = stock.price_change >= 0 ? 'price-up' : 'price-down';
                const priceChangeSign = stock.price_change >= 0 ? '+' : '';
                
                html += `
                    <div class="stock-card">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div class="stock-symbol">${stock.symbol}</div>
                            <div style="background: #007bff; color: white; padding: 2px 8px; border-radius: 10px; font-size: 0.8em;">
                                #${index + 1}
                            </div>
                        </div>
                        <div class="stock-name">${stock.name}</div>
                        <div class="stock-price ${priceChangeClass}">
                            $${stock.current_price}
                        </div>
                        <div class="${priceChangeClass}">
                            ${priceChangeSign}${stock.price_change} (${priceChangeSign}${stock.price_change_percent}%)
                        </div>
                        <div class="metric">趋势分数: ${stock.trend_score}</div>
                        <div class="metric">成交量: ${formatNumber(stock.volume)}</div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        // 测试股票搜索
        async function testStockSearch() {
            const container = document.getElementById('searchResult');
            container.innerHTML = '<div class="loading">🔄 正在测试搜索功能...</div>';
            
            const queries = ['APPLE', 'TESLA', 'MICROSOFT'];
            let html = '<div class="success">✅ 搜索功能测试</div>';
            
            for (const query of queries) {
                try {
                    const response = await fetch(`/api/stocks/search/${query}`);
                    const data = await response.json();
                    
                    html += `
                        <h4>搜索 "${query}" 的结果:</h4>
                        <div>找到 ${data.total_found} 个结果</div>
                    `;
                    
                    if (data.results && data.results.length > 0) {
                        html += '<div class="stock-grid">';
                        data.results.forEach(stock => {
                            const priceChangeClass = stock.price_change >= 0 ? 'price-up' : 'price-down';
                            html += `
                                <div class="stock-card">
                                    <div class="stock-symbol">${stock.symbol}</div>
                                    <div class="stock-name">${stock.name}</div>
                                    <div class="stock-price ${priceChangeClass}">$${stock.current_price}</div>
                                    <div class="metric">匹配类型: ${stock.match_type}</div>
                                </div>
                            `;
                        });
                        html += '</div>';
                    }
                } catch (error) {
                    html += `<div class="error">搜索 "${query}" 失败: ${error.message}</div>`;
                }
            }
            
            container.innerHTML = html;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('singleStockResult').innerHTML = '<div class="loading">点击"测试单个股票"按钮开始测试</div>';
            document.getElementById('multipleStocksResult').innerHTML = '<div class="loading">点击"测试多个股票"按钮开始测试</div>';
            document.getElementById('trendingStocksResult').innerHTML = '<div class="loading">点击"测试热门股票"按钮开始测试</div>';
            document.getElementById('searchResult').innerHTML = '<div class="loading">点击"测试股票搜索"按钮开始测试</div>';
        }

        // 格式化数字
        function formatNumber(num) {
            if (!num) return 'N/A';
            if (num >= 1000000000) {
                return (num / 1000000000).toFixed(1) + 'B';
            } else if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // 格式化时间
        function formatTime(timeString) {
            const date = new Date(timeString);
            const now = new Date();
            const diffHours = Math.floor((now - date) / (1000 * 60 * 60));
            
            if (diffHours < 1) {
                return '刚刚';
            } else if (diffHours < 24) {
                return `${diffHours}小时前`;
            } else {
                return `${Math.floor(diffHours / 24)}天前`;
            }
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            console.log('美股分析平台测试页面已加载');
        });
    </script>
</body>
</html>
