<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实景点图片测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .image-test-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        .image-test-card:hover {
            transform: translateY(-5px);
        }
        .image-container {
            width: 100%;
            height: 200px;
            position: relative;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: opacity 0.3s ease;
        }
        .image-container .icon-placeholder {
            font-size: 3em;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .image-container.loading {
            background: linear-gradient(135deg, #ddd 0%, #f0f0f0 100%);
        }
        .image-container.loaded .icon-placeholder {
            display: none;
        }
        .image-container.error {
            background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
        }
        .card-info {
            padding: 20px;
        }
        .card-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        .card-description {
            color: #6c757d;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .image-url {
            font-size: 0.8em;
            color: #007bff;
            word-break: break-all;
            margin-bottom: 10px;
        }
        .status-indicator {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 500;
        }
        .status-loading {
            background: #fff3cd;
            color: #856404;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .stats {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1>📸 真实景点图片测试</h1>
    <p>测试不同图片源的加载效果，包括Unsplash、Picsum等</p>
    
    <div class="controls">
        <button onclick="testRealImages()">🔄 测试真实图片</button>
        <button onclick="clearTest()">🗑️ 清空测试</button>
    </div>
    
    <div id="statsContainer"></div>
    <div id="testContainer" class="test-grid"></div>

    <script>
        const testImages = [
            {
                name: "大足石刻",
                description: "世界文化遗产，石雕艺术",
                icon: "🗿",
                urls: [
                    "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop",
                    "https://picsum.photos/400/300?random=1",
                    "https://source.unsplash.com/400x300/?temple,stone"
                ]
            },
            {
                name: "武隆天生三桥",
                description: "自然奇观，喀斯特地貌",
                icon: "🌉",
                urls: [
                    "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop",
                    "https://picsum.photos/400/300?random=2",
                    "https://source.unsplash.com/400x300/?bridge,nature"
                ]
            },
            {
                name: "承德避暑山庄",
                description: "清朝皇家园林，夏季避暑胜地",
                icon: "🏯",
                urls: [
                    "https://images.unsplash.com/photo-1508804185872-d7badad00f7d?w=400&h=300&fit=crop",
                    "https://picsum.photos/400/300?random=3",
                    "https://source.unsplash.com/400x300/?palace,garden"
                ]
            },
            {
                name: "苏州园林",
                description: "江南古典园林，诗情画意",
                icon: "🏮",
                urls: [
                    "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop",
                    "https://picsum.photos/400/300?random=4",
                    "https://source.unsplash.com/400x300/?garden,chinese"
                ]
            },
            {
                name: "杭州西湖",
                description: "人间天堂，湖光山色",
                icon: "🌸",
                urls: [
                    "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop",
                    "https://picsum.photos/400/300?random=5",
                    "https://source.unsplash.com/400x300/?lake,china"
                ]
            },
            {
                name: "都江堰",
                description: "古代水利工程，世界遗产",
                icon: "🌊",
                urls: [
                    "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop",
                    "https://picsum.photos/400/300?random=6",
                    "https://source.unsplash.com/400x300/?water,ancient"
                ]
            }
        ];

        function testRealImages() {
            const container = document.getElementById('testContainer');
            container.innerHTML = '';
            
            testImages.forEach((item, index) => {
                createImageTestCard(item, index);
            });
            
            updateStats();
        }

        function createImageTestCard(item, index) {
            const container = document.getElementById('testContainer');
            
            const card = document.createElement('div');
            card.className = 'image-test-card';
            card.id = `card-${index}`;
            
            card.innerHTML = `
                <div class="image-container loading" id="container-${index}">
                    <img id="img-${index}" src="" alt="${item.name}" style="display: none;">
                    <div class="icon-placeholder" id="icon-${index}">${item.icon}</div>
                </div>
                <div class="card-info">
                    <div class="card-title">${item.name}</div>
                    <div class="card-description">${item.description}</div>
                    <div class="image-url" id="url-${index}">准备加载...</div>
                    <div class="status-indicator status-loading" id="status-${index}">⏳ 加载中...</div>
                </div>
            `;
            
            container.appendChild(card);
            
            // 开始加载图片
            setTimeout(() => {
                loadImageWithFallback(index, item.urls, 0);
            }, index * 200); // 错开加载时间
        }

        function loadImageWithFallback(cardIndex, urls, urlIndex) {
            if (urlIndex >= urls.length) {
                // 所有URL都失败
                updateCardStatus(cardIndex, 'error', '❌ 加载失败', '所有图片源都无法访问');
                return;
            }
            
            const url = urls[urlIndex];
            const img = document.getElementById(`img-${cardIndex}`);
            const container = document.getElementById(`container-${cardIndex}`);
            const icon = document.getElementById(`icon-${cardIndex}`);
            
            updateCardStatus(cardIndex, 'loading', `⏳ 尝试源 ${urlIndex + 1}`, url);
            
            const tempImg = new Image();
            
            tempImg.onload = function() {
                // 成功加载
                img.src = url;
                img.style.display = 'block';
                icon.style.display = 'none';
                
                container.classList.remove('loading');
                container.classList.add('loaded');
                
                updateCardStatus(cardIndex, 'success', '✅ 加载成功', url);
                
                // 淡入效果
                img.style.opacity = '0';
                setTimeout(() => {
                    img.style.opacity = '1';
                }, 50);
            };
            
            tempImg.onerror = function() {
                // 加载失败，尝试下一个URL
                console.log(`图片加载失败: ${url}`);
                setTimeout(() => {
                    loadImageWithFallback(cardIndex, urls, urlIndex + 1);
                }, 500);
            };
            
            // 设置超时
            setTimeout(() => {
                if (container.classList.contains('loading')) {
                    tempImg.onerror();
                }
            }, 3000);
            
            tempImg.src = url;
        }

        function updateCardStatus(cardIndex, type, message, url) {
            const statusElement = document.getElementById(`status-${cardIndex}`);
            const urlElement = document.getElementById(`url-${cardIndex}`);
            
            statusElement.textContent = message;
            statusElement.className = `status-indicator status-${type}`;
            
            if (url) {
                urlElement.textContent = url;
            }
        }

        function updateStats() {
            setTimeout(() => {
                const total = testImages.length;
                let loaded = 0, failed = 0, loading = 0;
                
                for (let i = 0; i < total; i++) {
                    const container = document.getElementById(`container-${i}`);
                    if (container.classList.contains('loaded')) {
                        loaded++;
                    } else if (container.classList.contains('error')) {
                        failed++;
                    } else {
                        loading++;
                    }
                }
                
                const statsContainer = document.getElementById('statsContainer');
                statsContainer.innerHTML = `
                    <div class="stats">
                        <h3>📊 加载统计</h3>
                        <p><strong>总数:</strong> ${total} | 
                           <strong>成功:</strong> ${loaded} | 
                           <strong>失败:</strong> ${failed} | 
                           <strong>加载中:</strong> ${loading}</p>
                        <p><strong>成功率:</strong> ${total > 0 ? ((loaded / total) * 100).toFixed(1) : 0}%</p>
                    </div>
                `;
                
                if (loading > 0) {
                    setTimeout(updateStats, 1000);
                }
            }, 1000);
        }

        function clearTest() {
            document.getElementById('testContainer').innerHTML = '';
            document.getElementById('statsContainer').innerHTML = '';
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            setTimeout(testRealImages, 1000);
        });
    </script>
</body>
</html>
