#!/usr/bin/env python3
"""
智能天气穿衣助手 Web版启动脚本
"""

import os
import sys
from app import app

def main():
    print("🌤️  智能天气穿衣助手 Web版")
    print("=" * 50)
    print("🚀 正在启动Web服务器...")
    print("📱 服务器启动后，请在浏览器中访问:")
    print("   http://localhost:8080")
    print("   或")
    print("   http://127.0.0.1:8080")
    print("=" * 50)
    print("💡 提示: 按 Ctrl+C 停止服务器")
    print()
    
    try:
        # 启动Flask应用
        app.run(
            debug=True,
            host='0.0.0.0',
            port=8080,
            use_reloader=False  # 避免重复启动
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止，感谢使用！")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请检查:")
        print("   1. 端口5000是否被占用")
        print("   2. 依赖是否正确安装")
        print("   3. API密钥是否配置正确")

if __name__ == "__main__":
    main()
