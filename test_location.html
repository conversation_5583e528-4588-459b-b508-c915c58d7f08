<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地理位置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <h1>🧪 地理位置功能测试</h1>
    
    <div class="test-section">
        <h2>📍 浏览器地理位置测试</h2>
        <button onclick="testGeolocation()">测试浏览器定位</button>
        <div id="geoResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="test-section">
        <h2>🌐 API测试</h2>
        <button onclick="testLocationAPI()">测试位置API (东京坐标)</button>
        <button onclick="testLocationAPI2()">测试位置API (北京坐标)</button>
        <div id="apiResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="test-section">
        <h2>🏙️ 城市匹配测试</h2>
        <button onclick="testCityMatching()">测试城市匹配算法</button>
        <div id="matchResult" class="result" style="display:none;"></div>
    </div>

    <script>
        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.style.display = 'block';
        }

        function testGeolocation() {
            if (!navigator.geolocation) {
                showResult('geoResult', '❌ 浏览器不支持地理位置', true);
                return;
            }

            showResult('geoResult', '🔍 正在获取位置...', false);

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const lat = position.coords.latitude;
                    const lon = position.coords.longitude;
                    const accuracy = position.coords.accuracy;
                    
                    showResult('geoResult', `
                        ✅ 定位成功！<br>
                        📍 纬度: ${lat}<br>
                        📍 经度: ${lon}<br>
                        🎯 精度: ${accuracy}米
                    `, false);
                },
                function(error) {
                    let errorMsg = '❌ 定位失败: ';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMsg += '用户拒绝了定位请求';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMsg += '位置信息不可用';
                            break;
                        case error.TIMEOUT:
                            errorMsg += '定位请求超时';
                            break;
                        default:
                            errorMsg += '未知错误';
                            break;
                    }
                    showResult('geoResult', errorMsg, true);
                },
                {
                    timeout: 10000,
                    enableHighAccuracy: true
                }
            );
        }

        async function testLocationAPI() {
            // 东京坐标
            const lat = 35.6762;
            const lon = 139.6503;
            
            showResult('apiResult', '🔍 正在测试东京坐标...', false);
            
            try {
                const response = await fetch(`http://localhost:8080/api/location/${lat}/${lon}`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('apiResult', `
                        ✅ API调用成功！<br>
                        🏙️ 城市: ${data.city}<br>
                        🌍 原始城市: ${data.original_city}<br>
                        🇯🇵 国家: ${data.country}<br>
                        📍 坐标: ${data.lat}, ${data.lon}
                    `, false);
                } else {
                    showResult('apiResult', `❌ API错误: ${data.error}`, true);
                }
            } catch (error) {
                showResult('apiResult', `❌ 网络错误: ${error.message}`, true);
            }
        }

        async function testLocationAPI2() {
            // 北京坐标
            const lat = 39.9042;
            const lon = 116.4074;
            
            showResult('apiResult', '🔍 正在测试北京坐标...', false);
            
            try {
                const response = await fetch(`http://localhost:8080/api/location/${lat}/${lon}`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('apiResult', `
                        ✅ API调用成功！<br>
                        🏙️ 城市: ${data.city}<br>
                        🌍 原始城市: ${data.original_city}<br>
                        🇨🇳 国家: ${data.country}<br>
                        📍 坐标: ${data.lat}, ${data.lon}
                    `, false);
                } else {
                    showResult('apiResult', `❌ API错误: ${data.error}`, true);
                }
            } catch (error) {
                showResult('apiResult', `❌ 网络错误: ${error.message}`, true);
            }
        }

        function testCityMatching() {
            // 模拟城市匹配测试
            const testCities = [
                'Tokyo', 'Beijing', 'Shanghai', 'New York', 'London', 
                'Paris', 'Sydney', 'Dubai', 'Moscow', 'Singapore'
            ];
            
            let results = '🧪 城市匹配测试结果:<br><br>';
            
            testCities.forEach(city => {
                // 这里应该调用实际的匹配函数，但为了演示，我们模拟结果
                results += `✅ ${city} → 匹配成功<br>`;
            });
            
            results += '<br>💡 实际匹配需要在主页面中测试';
            
            showResult('matchResult', results, false);
        }
    </script>
</body>
</html>
