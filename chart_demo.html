<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票图表功能演示</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .chart-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .chart-container {
            height: 400px;
            position: relative;
        }
        .chart-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .chart-tab {
            padding: 10px 20px;
            background: #e9ecef;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        .chart-tab.active {
            background: #007bff;
            color: white;
        }
        .chart-tab:hover {
            background: #007bff;
            color: white;
        }
        .stock-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .stock-btn {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }
        .stock-btn:hover {
            background: #218838;
        }
        .stock-btn.active {
            background: #dc3545;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 1.1em;
        }
        .error {
            text-align: center;
            padding: 20px;
            color: #dc3545;
            background: #f8d7da;
            border-radius: 8px;
        }
        .chart-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        .info-item {
            text-align: center;
        }
        .info-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        .info-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        .price-up { color: #28a745; }
        .price-down { color: #dc3545; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 股票图表功能演示</h1>
        <p>展示不同类型的股票价格走势图表</p>
    </div>

    <div class="chart-section">
        <div class="chart-title">🎯 股票选择</div>
        <div class="stock-selector">
            <button class="stock-btn active" onclick="loadStockChart('AAPL')">AAPL - Apple</button>
            <button class="stock-btn" onclick="loadStockChart('MSFT')">MSFT - Microsoft</button>
            <button class="stock-btn" onclick="loadStockChart('GOOGL')">GOOGL - Google</button>
            <button class="stock-btn" onclick="loadStockChart('TSLA')">TSLA - Tesla</button>
            <button class="stock-btn" onclick="loadStockChart('AMZN')">AMZN - Amazon</button>
            <button class="stock-btn" onclick="loadStockChart('META')">META - Meta</button>
        </div>
    </div>

    <div class="chart-section">
        <div class="chart-title">📊 股票基本信息</div>
        <div id="stockInfo" class="chart-info">
            <div class="loading">选择股票查看基本信息</div>
        </div>
    </div>

    <div class="chart-section">
        <div class="chart-title">📈 价格走势图表</div>
        <div class="chart-tabs">
            <button class="chart-tab active" onclick="switchChartType('line')">📈 价格走势</button>
            <button class="chart-tab" onclick="switchChartType('candlestick')">🕯️ K线图</button>
            <button class="chart-tab" onclick="switchChartType('volume')">📊 成交量</button>
            <button class="chart-tab" onclick="switchChartType('combined')">🔄 组合图</button>
        </div>
        <div class="chart-container">
            <canvas id="mainChart"></canvas>
        </div>
    </div>

    <script>
        let currentChart = null;
        let currentStockData = null;
        let currentChartType = 'line';
        let currentSymbol = 'AAPL';

        // 页面加载完成后自动加载AAPL数据
        window.addEventListener('load', function() {
            loadStockChart('AAPL');
        });

        // 加载股票图表
        async function loadStockChart(symbol) {
            currentSymbol = symbol;
            
            // 更新按钮状态
            document.querySelectorAll('.stock-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 显示加载状态
            document.getElementById('stockInfo').innerHTML = '<div class="loading">🔄 正在加载股票数据...</div>';
            
            try {
                const response = await fetch(`http://localhost:8080/api/stock/${symbol}`);
                const data = await response.json();
                
                if (response.ok) {
                    currentStockData = data.stock_data;
                    displayStockInfo(data.stock_data);
                    createChart(currentChartType);
                } else {
                    showError(data.error || '获取股票数据失败');
                }
            } catch (error) {
                showError('网络错误，请稍后重试');
                console.error('Error:', error);
            }
        }

        // 显示股票基本信息
        function displayStockInfo(stockData) {
            const priceChangeClass = stockData.price_change >= 0 ? 'price-up' : 'price-down';
            const priceChangeSign = stockData.price_change >= 0 ? '+' : '';
            
            const infoHtml = `
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">股票代码</div>
                        <div class="info-value">${stockData.symbol}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">当前价格</div>
                        <div class="info-value ${priceChangeClass}">$${stockData.current_price}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">涨跌</div>
                        <div class="info-value ${priceChangeClass}">
                            ${priceChangeSign}${stockData.price_change} (${priceChangeSign}${stockData.price_change_percent}%)
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">成交量</div>
                        <div class="info-value">${formatNumber(stockData.volume)}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">52周高点</div>
                        <div class="info-value">$${stockData.week_52_high}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">52周低点</div>
                        <div class="info-value">$${stockData.week_52_low}</div>
                    </div>
                </div>
            `;
            
            document.getElementById('stockInfo').innerHTML = infoHtml;
        }

        // 切换图表类型
        function switchChartType(chartType) {
            currentChartType = chartType;
            
            // 更新标签状态
            document.querySelectorAll('.chart-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 重新绘制图表
            if (currentStockData) {
                createChart(chartType);
            }
        }

        // 创建图表
        function createChart(chartType) {
            const ctx = document.getElementById('mainChart').getContext('2d');
            
            // 销毁现有图表
            if (currentChart) {
                currentChart.destroy();
            }
            
            const historicalData = currentStockData.historical_data || [];
            
            switch(chartType) {
                case 'line':
                    createLineChart(ctx, historicalData);
                    break;
                case 'candlestick':
                    createCandlestickChart(ctx, historicalData);
                    break;
                case 'volume':
                    createVolumeChart(ctx, historicalData);
                    break;
                case 'combined':
                    createCombinedChart(ctx, historicalData);
                    break;
            }
        }

        // 创建价格走势图
        function createLineChart(ctx, historicalData) {
            const labels = historicalData.map(item => item.date);
            const prices = historicalData.map(item => item.close);
            
            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `${currentStockData.symbol} 收盘价`,
                        data: prices,
                        borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: currentStockData.price_change >= 0 ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.1,
                        pointRadius: 4,
                        pointHoverRadius: 8
                    }]
                },
                options: getChartOptions('价格走势图', '价格 ($)')
            });
        }

        // 创建成交量图
        function createVolumeChart(ctx, historicalData) {
            const labels = historicalData.map(item => item.date);
            const volumes = historicalData.map(item => item.volume);
            
            currentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '成交量',
                        data: volumes,
                        backgroundColor: historicalData.map(item => 
                            item.close >= item.open ? 'rgba(40, 167, 69, 0.7)' : 'rgba(220, 53, 69, 0.7)'
                        ),
                        borderColor: historicalData.map(item => 
                            item.close >= item.open ? '#28a745' : '#dc3545'
                        ),
                        borderWidth: 1
                    }]
                },
                options: getChartOptions('成交量图', '成交量', true)
            });
        }

        // 创建K线图（简化版）
        function createCandlestickChart(ctx, historicalData) {
            const labels = historicalData.map(item => item.date);
            
            currentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '股价区间',
                        data: historicalData.map(item => ({
                            x: item.date,
                            y: [item.low, item.high]
                        })),
                        backgroundColor: historicalData.map(item => 
                            item.close >= item.open ? '#28a745' : '#dc3545'
                        ),
                        borderColor: historicalData.map(item => 
                            item.close >= item.open ? '#28a745' : '#dc3545'
                        ),
                        borderWidth: 2,
                        barThickness: 10
                    }]
                },
                options: getChartOptions('K线图', '价格 ($)')
            });
        }

        // 创建组合图
        function createCombinedChart(ctx, historicalData) {
            const labels = historicalData.map(item => item.date);
            const prices = historicalData.map(item => item.close);
            const volumes = historicalData.map(item => item.volume);
            
            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '收盘价',
                        data: prices,
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1,
                        yAxisID: 'y'
                    }, {
                        label: '成交量',
                        data: volumes,
                        type: 'bar',
                        backgroundColor: 'rgba(255, 193, 7, 0.3)',
                        borderColor: '#ffc107',
                        borderWidth: 1,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentStockData.name} (${currentStockData.symbol}) - 价格与成交量组合图`,
                            font: { size: 16, weight: 'bold' }
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day',
                                displayFormats: { day: 'MM/dd' }
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: '价格 ($)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: '成交量' },
                            grid: { drawOnChartArea: false },
                            ticks: {
                                callback: function(value) {
                                    return formatNumber(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        // 获取图表通用配置
        function getChartOptions(title, yAxisLabel, isVolume = false) {
            return {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: `${currentStockData.name} (${currentStockData.symbol}) - ${title}`,
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day',
                            displayFormats: { day: 'MM/dd' }
                        },
                        title: { display: true, text: '日期' }
                    },
                    y: {
                        title: { display: true, text: yAxisLabel },
                        beginAtZero: isVolume,
                        ticks: isVolume ? {
                            callback: function(value) {
                                return formatNumber(value);
                            }
                        } : {}
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            };
        }

        // 显示错误
        function showError(message) {
            document.getElementById('stockInfo').innerHTML = `
                <div class="error">❌ ${message}</div>
            `;
        }

        // 格式化数字
        function formatNumber(num) {
            if (!num) return 'N/A';
            if (num >= 1000000000) {
                return (num / 1000000000).toFixed(1) + 'B';
            } else if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }
    </script>
</body>
</html>
