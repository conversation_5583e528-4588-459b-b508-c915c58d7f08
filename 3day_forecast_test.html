<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3天天气预测测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .forecast-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 20px 0;
        }
        .day-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-top: 4px solid #007bff;
        }
        .day-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }
        .day-name {
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .day-date {
            color: #6c757d;
            font-size: 1em;
        }
        .weather-section {
            text-align: center;
            margin-bottom: 20px;
        }
        .weather-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }
        .weather-condition {
            font-size: 1.2em;
            font-weight: 500;
            color: #495057;
            margin-bottom: 10px;
        }
        .temperature-range {
            font-size: 1.3em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 15px;
        }
        .weather-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }
        .detail-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }
        .detail-label {
            font-size: 0.8em;
            color: #6c757d;
            margin-bottom: 5px;
        }
        .detail-value {
            font-weight: bold;
            color: #495057;
        }
        .clothing-section {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .clothing-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }
        .clothing-item {
            margin: 5px 0;
            padding: 5px 10px;
            background: rgba(255,255,255,0.7);
            border-radius: 5px;
            font-size: 0.9em;
        }
        .weather-tips {
            background: #fff3cd;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
        }
        .tips-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 8px;
        }
        .tip-item {
            margin: 3px 0;
            font-size: 0.9em;
            color: #856404;
        }
        .comfort-indicator {
            text-align: center;
            padding: 10px;
            border-radius: 10px;
            font-weight: 500;
        }
        .comfort-excellent {
            background: #d4edda;
            color: #155724;
        }
        .comfort-good {
            background: #cce5ff;
            color: #004085;
        }
        .comfort-fair {
            background: #fff3cd;
            color: #856404;
        }
        .comfort-poor {
            background: #f8d7da;
            color: #721c24;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-size: 1.1em;
        }
    </style>
</head>
<body>
    <h1>📅 3天天气预测与穿衣建议</h1>
    <p>测试未来3天的天气预测和每日穿衣建议功能</p>
    
    <div class="controls">
        <button onclick="test3DayForecast()">🔄 测试3天预测</button>
        <button onclick="clearTest()">🗑️ 清空测试</button>
    </div>
    
    <div id="forecastContainer" class="forecast-grid"></div>

    <script>
        async function test3DayForecast() {
            const container = document.getElementById('forecastContainer');
            container.innerHTML = '<div class="loading">🔄 正在加载3天天气预测...</div>';
            
            try {
                const response = await fetch('http://localhost:8080/api/weather/Beijing');
                const data = await response.json();
                
                if (data.daily_clothing_advice && data.daily_clothing_advice.length > 0) {
                    displayForecast(data.daily_clothing_advice);
                } else {
                    container.innerHTML = '<div class="loading">❌ 没有获取到3天预测数据</div>';
                }
                
            } catch (error) {
                container.innerHTML = `<div class="loading">❌ 加载失败: ${error.message}</div>`;
            }
        }
        
        function displayForecast(forecastData) {
            const container = document.getElementById('forecastContainer');
            let html = '';
            
            forecastData.forEach((dayData, index) => {
                const comfortClass = `comfort-${dayData.comfort_level.level}`;
                
                html += `
                    <div class="day-card">
                        <div class="day-header">
                            <div class="day-name">${dayData.day_name}</div>
                            <div class="day-date">${dayData.month_day}</div>
                        </div>
                        
                        <div class="weather-section">
                            <div class="weather-icon">${dayData.weather.icon}</div>
                            <div class="weather-condition">${dayData.weather.condition}</div>
                            <div class="temperature-range">${dayData.temperature_range}</div>
                        </div>
                        
                        <div class="weather-details">
                            <div class="detail-item">
                                <div class="detail-label">湿度</div>
                                <div class="detail-value">${dayData.weather.humidity}%</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">降水概率</div>
                                <div class="detail-value">${dayData.weather.precipitation_chance}%</div>
                            </div>
                        </div>
                        
                        <div class="clothing-section">
                            <div class="clothing-title">👗 穿衣建议</div>
                            <div class="clothing-item"><strong>上装:</strong> ${dayData.clothing_advice.top}</div>
                            <div class="clothing-item"><strong>下装:</strong> ${dayData.clothing_advice.bottom}</div>
                            <div class="clothing-item"><strong>鞋子:</strong> ${dayData.clothing_advice.shoes}</div>
                            <div class="clothing-item"><strong>配饰:</strong> ${dayData.clothing_advice.accessories}</div>
                        </div>
                        
                        ${dayData.weather_tips && dayData.weather_tips.length > 0 ? `
                        <div class="weather-tips">
                            <div class="tips-title">💡 贴心提醒</div>
                            ${dayData.weather_tips.map(tip => `<div class="tip-item">${tip}</div>`).join('')}
                        </div>
                        ` : ''}
                        
                        <div class="comfort-indicator ${comfortClass}">
                            ${dayData.comfort_level.icon} ${dayData.comfort_level.text}
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
            // 显示统计信息
            console.log('=== 3天天气预测统计 ===');
            forecastData.forEach((day, index) => {
                console.log(`第${index + 1}天 (${day.day_name}):`);
                console.log(`  天气: ${day.weather.condition} ${day.temperature_range}`);
                console.log(`  穿衣: ${day.clothing_advice.top} + ${day.clothing_advice.bottom}`);
                console.log(`  舒适度: ${day.comfort_level.text}`);
            });
        }
        
        function clearTest() {
            document.getElementById('forecastContainer').innerHTML = '';
        }
        
        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            setTimeout(test3DayForecast, 1000);
        });
    </script>
</body>
</html>
