<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>景点图片匹配测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 20px 0;
        }
        .scenic-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .scenic-card:hover {
            transform: translateY(-5px);
        }
        .image-section {
            position: relative;
            height: 200px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }
        .scenic-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: opacity 0.3s ease;
        }
        .image-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3em;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .image-section.loaded .image-placeholder {
            display: none;
        }
        .image-section.error .scenic-image {
            display: none;
        }
        .card-content {
            padding: 20px;
        }
        .scenic-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        .scenic-description {
            color: #6c757d;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        .image-keywords {
            background: #e3f2fd;
            color: #1976d2;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            margin-bottom: 10px;
            word-break: break-all;
        }
        .load-status {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 500;
            text-align: center;
        }
        .status-loading {
            background: #fff3cd;
            color: #856404;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .stats {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .city-section {
            margin: 30px 0;
        }
        .city-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>🖼️ 景点图片精确匹配测试</h1>
    <p>测试每个景点的图片是否与景点名称和特色精确匹配</p>
    
    <div class="controls">
        <button onclick="testImageMatching()">🔄 测试图片匹配</button>
        <button onclick="clearTest()">🗑️ 清空测试</button>
    </div>
    
    <div id="statsContainer"></div>
    <div id="testContainer"></div>

    <script>
        const testCities = ['Chongqing', 'Chengdu', 'Beijing', 'Shanghai'];
        
        async function testImageMatching() {
            const container = document.getElementById('testContainer');
            const statsContainer = document.getElementById('statsContainer');
            
            container.innerHTML = '<div style="text-align: center; padding: 40px; color: #6c757d;">🔄 正在加载景点数据...</div>';
            statsContainer.innerHTML = '';
            
            try {
                const promises = testCities.map(city => 
                    fetch(`http://localhost:8080/api/weather/${city}`)
                        .then(response => response.json())
                        .then(data => ({ 
                            city, 
                            cityName: getCityName(city),
                            attractions: data.travel_recommendations?.nearby || [] 
                        }))
                );
                
                const results = await Promise.all(promises);
                displayScenicCards(results);
                
            } catch (error) {
                container.innerHTML = `<div style="text-align: center; padding: 40px; color: #dc3545;">❌ 加载失败: ${error.message}</div>`;
            }
        }
        
        function getCityName(city) {
            const names = {
                'Chongqing': '重庆',
                'Chengdu': '成都',
                'Beijing': '北京',
                'Shanghai': '上海'
            };
            return names[city] || city;
        }
        
        function displayScenicCards(cities) {
            const container = document.getElementById('testContainer');
            let html = '';
            
            cities.forEach(cityData => {
                html += `
                    <div class="city-section">
                        <div class="city-title">${cityData.cityName} 周边游</div>
                        <div class="test-grid">
                `;
                
                cityData.attractions.forEach((attraction, index) => {
                    const cardId = `${cityData.city}-${index}`;
                    const keywords = extractKeywords(attraction.image);
                    
                    html += `
                        <div class="scenic-card">
                            <div class="image-section" id="section-${cardId}">
                                <img class="scenic-image" id="img-${cardId}" src="" alt="${attraction.name}" style="display: none;">
                                <div class="image-placeholder" id="placeholder-${cardId}">${attraction.icon}</div>
                            </div>
                            <div class="card-content">
                                <div class="scenic-name">${attraction.name}</div>
                                <div class="scenic-description">${attraction.description}</div>
                                <div class="image-keywords">关键词: ${keywords}</div>
                                <div class="load-status status-loading" id="status-${cardId}">⏳ 准备加载...</div>
                            </div>
                        </div>
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
            // 开始加载图片
            let loadIndex = 0;
            cities.forEach(cityData => {
                cityData.attractions.forEach((attraction, index) => {
                    const cardId = `${cityData.city}-${index}`;
                    setTimeout(() => {
                        loadScenicImage(cardId, attraction);
                    }, loadIndex * 300);
                    loadIndex++;
                });
            });
            
            // 延迟显示统计
            setTimeout(() => {
                updateStats(cities);
            }, loadIndex * 300 + 2000);
        }
        
        function extractKeywords(imageUrl) {
            const match = imageUrl.match(/\?(.+)$/);
            if (match) {
                return match[1].replace(/,/g, ', ');
            }
            return '无关键词';
        }
        
        function loadScenicImage(cardId, attraction) {
            const img = document.getElementById(`img-${cardId}`);
            const section = document.getElementById(`section-${cardId}`);
            const placeholder = document.getElementById(`placeholder-${cardId}`);
            const status = document.getElementById(`status-${cardId}`);
            
            status.textContent = '🔄 正在加载...';
            status.className = 'load-status status-loading';
            
            const tempImg = new Image();
            
            tempImg.onload = function() {
                img.src = attraction.image;
                img.style.display = 'block';
                section.classList.add('loaded');
                
                status.textContent = '✅ 加载成功';
                status.className = 'load-status status-success';
                
                // 淡入效果
                img.style.opacity = '0';
                setTimeout(() => {
                    img.style.opacity = '1';
                }, 50);
            };
            
            tempImg.onerror = function() {
                section.classList.add('error');
                status.textContent = '❌ 加载失败';
                status.className = 'load-status status-error';
            };
            
            // 设置超时
            setTimeout(() => {
                if (!section.classList.contains('loaded') && !section.classList.contains('error')) {
                    tempImg.onerror();
                }
            }, 5000);
            
            tempImg.src = attraction.image;
        }
        
        function updateStats(cities) {
            const totalAttractions = cities.reduce((sum, city) => sum + city.attractions.length, 0);
            let loaded = 0, failed = 0;
            
            cities.forEach(cityData => {
                cityData.attractions.forEach((attraction, index) => {
                    const cardId = `${cityData.city}-${index}`;
                    const section = document.getElementById(`section-${cardId}`);
                    
                    if (section.classList.contains('loaded')) {
                        loaded++;
                    } else if (section.classList.contains('error')) {
                        failed++;
                    }
                });
            });
            
            const successRate = totalAttractions > 0 ? ((loaded / totalAttractions) * 100).toFixed(1) : 0;
            
            const statsContainer = document.getElementById('statsContainer');
            statsContainer.innerHTML = `
                <div class="stats">
                    <h3>📊 图片匹配统计</h3>
                    <p><strong>总景点数:</strong> ${totalAttractions} | 
                       <strong>成功加载:</strong> ${loaded} | 
                       <strong>加载失败:</strong> ${failed}</p>
                    <p><strong>成功率:</strong> ${successRate}% | 
                       <strong>图片匹配度:</strong> ${loaded > 0 ? '高度匹配' : '需要优化'}</p>
                </div>
            `;
            
            console.log('=== 景点图片匹配测试结果 ===');
            cities.forEach(cityData => {
                console.log(`\n${cityData.cityName}:`);
                cityData.attractions.forEach(attraction => {
                    const keywords = extractKeywords(attraction.image);
                    console.log(`  ${attraction.name} - 关键词: ${keywords}`);
                });
            });
        }
        
        function clearTest() {
            document.getElementById('testContainer').innerHTML = '';
            document.getElementById('statsContainer').innerHTML = '';
        }
        
        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            setTimeout(testImageMatching, 1000);
        });
    </script>
</body>
</html>
