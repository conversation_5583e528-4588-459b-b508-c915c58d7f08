<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>周边游推荐测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .cities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 20px 0;
        }
        .city-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-top: 4px solid #007bff;
        }
        .city-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }
        .city-name {
            font-size: 1.6em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .city-subtitle {
            color: #6c757d;
            font-size: 0.9em;
        }
        .attraction-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 18px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .attraction-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .attraction-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .attraction-icon {
            font-size: 1.8em;
            margin-right: 12px;
        }
        .attraction-name {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }
        .attraction-description {
            color: #6c757d;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .attraction-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
        }
        .distance {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 500;
        }
        .travel-time {
            background: #f3e5f5;
            color: #7b1fa2;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 500;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-size: 1.1em;
        }
        .test-controls {
            text-align: center;
            margin: 30px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: background 0.2s ease;
        }
        button:hover {
            background: #0056b3;
        }
        .stats {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1>🗺️ 周边游推荐测试</h1>
    <p>测试不同城市的周边游景点推荐，确保显示正确的本地景点</p>
    
    <div class="test-controls">
        <button onclick="loadNearbyAttractions()">🔄 加载周边游推荐</button>
        <button onclick="clearResults()">🗑️ 清空结果</button>
    </div>
    
    <div id="statsContainer"></div>
    <div id="attractionsContainer" class="cities-grid"></div>

    <script>
        async function loadNearbyAttractions() {
            const container = document.getElementById('attractionsContainer');
            const statsContainer = document.getElementById('statsContainer');
            
            container.innerHTML = '<div class="loading">🔄 正在加载各城市周边游推荐...</div>';
            statsContainer.innerHTML = '';
            
            const cities = [
                { key: 'Beijing', name: '北京' },
                { key: 'Shanghai', name: '上海' },
                { key: 'Guangzhou', name: '广州' },
                { key: 'Chongqing', name: '重庆' },
                { key: 'Chengdu', name: '成都' },
                { key: 'Xi\'an', name: '西安' },
                { key: 'Wuhan', name: '武汉' },
                { key: 'Nanjing', name: '南京' }
            ];
            
            try {
                const promises = cities.map(city => 
                    fetch(`http://localhost:8080/api/weather/${city.key}`)
                        .then(response => response.json())
                        .then(data => ({ 
                            ...city, 
                            attractions: data.travel_recommendations?.nearby || [] 
                        }))
                );
                
                const results = await Promise.all(promises);
                displayAttractions(results);
                displayStats(results);
                
            } catch (error) {
                container.innerHTML = `<div class="loading">❌ 加载失败: ${error.message}</div>`;
            }
        }
        
        function displayAttractions(cities) {
            const container = document.getElementById('attractionsContainer');
            
            if (cities.length === 0) {
                container.innerHTML = '<div class="loading">❌ 没有获取到数据</div>';
                return;
            }
            
            let html = '';
            
            cities.forEach(city => {
                html += `
                    <div class="city-card">
                        <div class="city-header">
                            <div class="city-name">${city.name}</div>
                            <div class="city-subtitle">${city.attractions.length} 个周边景点</div>
                        </div>
                `;
                
                city.attractions.forEach(attraction => {
                    html += `
                        <div class="attraction-item">
                            <div class="attraction-header">
                                <div class="attraction-icon">${attraction.icon || '🏞️'}</div>
                                <div class="attraction-name">${attraction.name}</div>
                            </div>
                            <div class="attraction-description">${attraction.description}</div>
                            <div class="attraction-details">
                                <span class="distance">${attraction.distance}</span>
                                <span class="travel-time">${attraction.travel_time}</span>
                            </div>
                        </div>
                    `;
                });
                
                html += '</div>';
            });
            
            container.innerHTML = html;
        }
        
        function displayStats(cities) {
            const statsContainer = document.getElementById('statsContainer');
            
            const totalAttractions = cities.reduce((sum, city) => sum + city.attractions.length, 0);
            const avgAttractions = (totalAttractions / cities.length).toFixed(1);
            
            // 统计距离分布
            const distances = [];
            cities.forEach(city => {
                city.attractions.forEach(attraction => {
                    const distanceStr = attraction.distance;
                    const distanceNum = parseInt(distanceStr);
                    if (!isNaN(distanceNum)) {
                        distances.push(distanceNum);
                    }
                });
            });
            
            const avgDistance = distances.length > 0 ? 
                (distances.reduce((sum, d) => sum + d, 0) / distances.length).toFixed(0) : 0;
            
            const within100km = distances.filter(d => d <= 100).length;
            const within100kmPercent = distances.length > 0 ? 
                ((within100km / distances.length) * 100).toFixed(0) : 0;
            
            statsContainer.innerHTML = `
                <div class="stats">
                    <h3>📊 统计信息</h3>
                    <p><strong>总景点数:</strong> ${totalAttractions} 个 | 
                       <strong>平均每城市:</strong> ${avgAttractions} 个 | 
                       <strong>平均距离:</strong> ${avgDistance}km</p>
                    <p><strong>100km内景点:</strong> ${within100km}/${distances.length} 个 (${within100kmPercent}%)</p>
                </div>
            `;
            
            console.log('=== 周边游推荐统计 ===');
            cities.forEach(city => {
                console.log(`\n${city.name}:`);
                city.attractions.forEach(attraction => {
                    console.log(`  ${attraction.name} - ${attraction.distance} (${attraction.travel_time})`);
                });
            });
        }
        
        function clearResults() {
            document.getElementById('attractionsContainer').innerHTML = '';
            document.getElementById('statsContainer').innerHTML = '';
        }
        
        // 页面加载完成后自动加载数据
        window.addEventListener('load', function() {
            setTimeout(loadNearbyAttractions, 1000);
        });
    </script>
</body>
</html>
