<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻时间点标注演示</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@3"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .chart-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .chart-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .chart-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .chart-btn {
            padding: 8px 16px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }
        .chart-btn:hover {
            background: #5a6268;
        }
        .chart-btn.active {
            background: #dc3545;
        }
        .combined-chart-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .chart-canvas {
            height: 500px;
            width: 100%;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .stock-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .stock-btn {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }
        .stock-btn:hover {
            background: #218838;
        }
        .stock-btn.active {
            background: #dc3545;
        }
        .feature-highlight {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        .feature-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }
        .legend-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .legend-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        .legend-line {
            width: 30px;
            height: 2px;
            margin-right: 10px;
            border-style: dashed;
            border-width: 1px 0;
        }
        .legend-positive { border-color: #28a745; }
        .legend-negative { border-color: #dc3545; }
        .legend-neutral { border-color: #6c757d; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📰 新闻时间点标注演示</h1>
        <p>在股价、成交量、K线趋势图上标注新闻发生的时间点</p>
    </div>

    <div class="demo-section">
        <div class="section-title">🎯 功能特色</div>
        <div class="feature-highlight">
            <div class="feature-title">📍 精准时间标注</div>
            <div>在图表上用垂直虚线标注新闻发生的具体时间点，直观显示新闻事件对股价的影响。</div>
        </div>
        <div class="feature-highlight">
            <div class="feature-title">🎨 情绪色彩编码</div>
            <div>根据新闻情绪自动着色：绿色表示积极新闻，红色表示消极新闻，灰色表示中性新闻。</div>
        </div>
        <div class="feature-highlight">
            <div class="feature-title">💬 新闻标题预览</div>
            <div>鼠标悬停在标注线上可以看到新闻标题预览，快速了解新闻内容。</div>
        </div>
        <div class="legend-box">
            <div class="legend-title">📊 标注图例</div>
            <div class="legend-item">
                <div class="legend-line legend-positive"></div>
                <span>积极新闻 - 可能推动股价上涨</span>
            </div>
            <div class="legend-item">
                <div class="legend-line legend-negative"></div>
                <span>消极新闻 - 可能导致股价下跌</span>
            </div>
            <div class="legend-item">
                <div class="legend-line legend-neutral"></div>
                <span>中性新闻 - 对股价影响不明确</span>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="section-title">🎯 股票选择</div>
        <div class="stock-selector">
            <button class="stock-btn active" onclick="loadStockChart('AAPL')">AAPL - Apple</button>
            <button class="stock-btn" onclick="loadStockChart('MSFT')">MSFT - Microsoft</button>
            <button class="stock-btn" onclick="loadStockChart('GOOGL')">GOOGL - Google</button>
            <button class="stock-btn" onclick="loadStockChart('TSLA')">TSLA - Tesla</button>
            <button class="stock-btn" onclick="loadStockChart('AMZN')">AMZN - Amazon</button>
            <button class="stock-btn" onclick="loadStockChart('META')">META - Meta</button>
        </div>
    </div>

    <div class="demo-section">
        <div class="section-title">📰 新闻标注控制</div>
        <div class="chart-controls">
            <div class="chart-title">📊 显示选项</div>
            <div class="chart-actions">
                <button class="chart-btn active" onclick="toggleNewsMarkers()" id="newsBtn">显示新闻标注</button>
                <button class="chart-btn" onclick="showPriceChart()">价格走势图</button>
                <button class="chart-btn" onclick="showVolumeChart()">成交量图</button>
                <button class="chart-btn" onclick="showCombinedChart()">组合图表</button>
            </div>
        </div>
        
        <div class="combined-chart-container">
            <canvas id="mainChart" class="chart-canvas"></canvas>
        </div>
    </div>

    <script>
        let currentChart = null;
        let currentStockData = null;
        let currentNewsData = null;
        let currentSymbol = 'AAPL';
        let showNewsMarkers = true;
        let currentChartType = 'combined';

        // 页面加载完成后自动加载AAPL数据
        window.addEventListener('load', function() {
            loadStockChart('AAPL');
        });

        // 加载股票图表
        async function loadStockChart(symbol) {
            currentSymbol = symbol;
            
            // 更新按钮状态
            document.querySelectorAll('.stock-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            try {
                const response = await fetch(`http://localhost:8080/api/stock/${symbol}`);
                const data = await response.json();
                
                if (response.ok) {
                    currentStockData = data.stock_data;
                    currentNewsData = data.news || [];
                    createChart();
                } else {
                    console.error('获取股票数据失败:', data.error);
                }
            } catch (error) {
                console.error('网络错误:', error);
            }
        }

        // 切换新闻标注显示
        function toggleNewsMarkers() {
            showNewsMarkers = !showNewsMarkers;
            const btn = document.getElementById('newsBtn');
            
            if (showNewsMarkers) {
                btn.textContent = '隐藏新闻标注';
                btn.classList.add('active');
            } else {
                btn.textContent = '显示新闻标注';
                btn.classList.remove('active');
            }
            
            createChart();
        }

        // 显示价格走势图
        function showPriceChart() {
            currentChartType = 'price';
            createChart();
        }

        // 显示成交量图
        function showVolumeChart() {
            currentChartType = 'volume';
            createChart();
        }

        // 显示组合图表
        function showCombinedChart() {
            currentChartType = 'combined';
            createChart();
        }

        // 创建图表
        function createChart() {
            if (!currentStockData) return;
            
            switch(currentChartType) {
                case 'price':
                    createPriceChart();
                    break;
                case 'volume':
                    createVolumeChart();
                    break;
                case 'combined':
                default:
                    createCombinedChart();
                    break;
            }
        }

        // 处理新闻标注
        function getNewsAnnotations() {
            if (!showNewsMarkers || !currentNewsData || currentNewsData.length === 0) {
                return [];
            }
            
            const annotations = [];
            const historicalData = currentStockData.historical_data || [];
            
            currentNewsData.forEach((news, index) => {
                const newsDate = new Date(news.published_time).toISOString().split('T')[0];
                
                // 查找对应的历史数据点
                const dataPoint = historicalData.find(item => item.date === newsDate);
                
                if (dataPoint) {
                    // 根据新闻情绪确定颜色
                    let color = '#6c757d'; // 默认灰色
                    let emoji = '📰';
                    if (news.sentiment === 'positive') {
                        color = '#28a745'; // 绿色
                        emoji = '📈';
                    } else if (news.sentiment === 'negative') {
                        color = '#dc3545'; // 红色
                        emoji = '📉';
                    }
                    
                    annotations.push({
                        type: 'line',
                        mode: 'vertical',
                        scaleID: 'x',
                        value: newsDate,
                        borderColor: color,
                        borderWidth: 2,
                        borderDash: [5, 5],
                        label: {
                            enabled: true,
                            content: `${emoji} ${news.title.substring(0, 25)}...`,
                            position: 'top',
                            backgroundColor: color,
                            color: 'white',
                            font: {
                                size: 11,
                                weight: 'bold'
                            },
                            padding: 6,
                            cornerRadius: 4,
                            rotation: 0
                        }
                    });
                }
            });
            
            return annotations;
        }

        // 创建价格走势图
        function createPriceChart() {
            const chartCanvas = document.getElementById('mainChart');
            const ctx = chartCanvas.getContext('2d');
            
            if (currentChart) {
                currentChart.destroy();
            }
            
            const historicalData = currentStockData.historical_data || [];
            const labels = historicalData.map(item => item.date);
            const prices = historicalData.map(item => item.close);
            
            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `${currentStockData.symbol} 收盘价`,
                        data: prices,
                        borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: currentStockData.price_change >= 0 ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.1,
                        pointRadius: 3,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentStockData.name} (${currentStockData.symbol}) - 价格走势与新闻标注`,
                            font: { size: 16, weight: 'bold' }
                        },
                        annotation: {
                            annotations: getNewsAnnotations()
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: { unit: 'day', displayFormats: { day: 'MM/dd' } },
                            title: { display: true, text: '日期' }
                        },
                        y: {
                            title: { display: true, text: '价格 ($)' }
                        }
                    }
                }
            });
        }

        // 创建成交量图
        function createVolumeChart() {
            const chartCanvas = document.getElementById('mainChart');
            const ctx = chartCanvas.getContext('2d');
            
            if (currentChart) {
                currentChart.destroy();
            }
            
            const historicalData = currentStockData.historical_data || [];
            const labels = historicalData.map(item => item.date);
            const volumes = historicalData.map(item => item.volume);
            
            currentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '成交量',
                        data: volumes,
                        backgroundColor: historicalData.map(item => 
                            item.close >= item.open ? 'rgba(40, 167, 69, 0.7)' : 'rgba(220, 53, 69, 0.7)'
                        ),
                        borderColor: historicalData.map(item => 
                            item.close >= item.open ? '#28a745' : '#dc3545'
                        ),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentStockData.name} (${currentStockData.symbol}) - 成交量与新闻标注`,
                            font: { size: 16, weight: 'bold' }
                        },
                        annotation: {
                            annotations: getNewsAnnotations()
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: { unit: 'day', displayFormats: { day: 'MM/dd' } },
                            title: { display: true, text: '日期' }
                        },
                        y: {
                            title: { display: true, text: '成交量' },
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    if (value >= 1000000000) return (value/1000000000).toFixed(1) + 'B';
                                    if (value >= 1000000) return (value/1000000).toFixed(1) + 'M';
                                    if (value >= 1000) return (value/1000).toFixed(1) + 'K';
                                    return value.toString();
                                }
                            }
                        }
                    }
                }
            });
        }

        // 创建组合图表
        function createCombinedChart() {
            const chartCanvas = document.getElementById('mainChart');
            const ctx = chartCanvas.getContext('2d');
            
            if (currentChart) {
                currentChart.destroy();
            }
            
            const historicalData = currentStockData.historical_data || [];
            const labels = historicalData.map(item => item.date);
            
            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '收盘价',
                        data: historicalData.map(item => item.close),
                        type: 'line',
                        borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: 'transparent',
                        borderWidth: 3,
                        pointRadius: 3,
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y'
                    }, {
                        label: '成交量',
                        data: historicalData.map(item => item.volume),
                        type: 'bar',
                        backgroundColor: historicalData.map(item => 
                            item.close >= item.open ? 'rgba(40, 167, 69, 0.3)' : 'rgba(220, 53, 69, 0.3)'
                        ),
                        borderColor: historicalData.map(item => 
                            item.close >= item.open ? '#28a745' : '#dc3545'
                        ),
                        borderWidth: 1,
                        barThickness: 6,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentStockData.name} (${currentStockData.symbol}) - 价格成交量组合图与新闻标注`,
                            font: { size: 16, weight: 'bold' }
                        },
                        annotation: {
                            annotations: getNewsAnnotations()
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: { unit: 'day', displayFormats: { day: 'MM/dd' } },
                            title: { display: true, text: '日期' }
                        },
                        y: {
                            type: 'linear',
                            position: 'left',
                            title: { display: true, text: '价格 ($)' }
                        },
                        y1: {
                            type: 'linear',
                            position: 'right',
                            title: { display: true, text: '成交量' },
                            beginAtZero: true,
                            grid: { drawOnChartArea: false },
                            ticks: {
                                callback: function(value) {
                                    if (value >= 1000000000) return (value/1000000000).toFixed(1) + 'B';
                                    if (value >= 1000000) return (value/1000000).toFixed(1) + 'M';
                                    if (value >= 1000) return (value/1000).toFixed(1) + 'K';
                                    return value.toString();
                                }
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
