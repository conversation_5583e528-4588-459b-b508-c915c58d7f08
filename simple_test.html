<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
            background: #d4edda;
        }
        .error {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <h1>🧪 简单API测试</h1>
    <p>测试股票API的基本功能</p>

    <div class="test-section">
        <h3>📊 股票数据测试</h3>
        <button onclick="testStock('AAPL')">测试 AAPL</button>
        <button onclick="testStock('MSFT')">测试 MSFT</button>
        <button onclick="testStock('GOOGL')">测试 GOOGL</button>
        <button onclick="testTrending()">测试热门股票</button>
        <div id="result" class="result">点击按钮开始测试...</div>
    </div>

    <script>
        function log(message) {
            const result = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            result.textContent += `[${timestamp}] ${message}\n`;
            result.scrollTop = result.scrollHeight;
        }

        function clearResult() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = 'result';
        }

        async function testStock(symbol) {
            clearResult();
            log(`开始测试股票: ${symbol}`);
            
            try {
                log('发送API请求...');
                const response = await fetch(`http://localhost:8080/api/stock/${symbol}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                
                const data = await response.json();
                log('数据解析成功');
                
                if (data.stock_data) {
                    const stock = data.stock_data;
                    log(`✅ 成功获取股票数据:`);
                    log(`   公司: ${stock.name}`);
                    log(`   代码: ${stock.symbol}`);
                    log(`   价格: $${stock.current_price}`);
                    log(`   涨跌: ${stock.price_change} (${stock.price_change_percent}%)`);
                    log(`   成交量: ${stock.volume.toLocaleString()}`);
                    log(`   历史数据: ${stock.historical_data ? stock.historical_data.length : 0}天`);
                    
                    if (data.news) {
                        log(`   新闻数量: ${data.news.length}条`);
                    }
                    
                    if (data.investment_recommendation) {
                        log(`   投资建议: ${data.investment_recommendation.recommendation}`);
                    }
                    
                    document.getElementById('result').className = 'result success';
                } else {
                    throw new Error('返回数据格式不正确');
                }
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
                document.getElementById('result').className = 'result error';
                
                // 提供详细的错误信息
                if (error.message.includes('Failed to fetch')) {
                    log('');
                    log('可能的原因:');
                    log('1. 服务器未启动 - 请确认运行了 python run_web.py');
                    log('2. 端口被占用 - 请检查8080端口是否可用');
                    log('3. 防火墙阻止 - 请检查防火墙设置');
                    log('4. 网络连接问题 - 请检查网络连接');
                }
            }
        }

        async function testTrending() {
            clearResult();
            log('开始测试热门股票API');
            
            try {
                log('发送API请求...');
                const response = await fetch('http://localhost:8080/api/stocks/trending', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                
                const data = await response.json();
                log('数据解析成功');
                
                if (data.trending_stocks && Array.isArray(data.trending_stocks)) {
                    log(`✅ 成功获取热门股票数据:`);
                    log(`   股票数量: ${data.trending_stocks.length}`);
                    
                    data.trending_stocks.slice(0, 5).forEach((stock, index) => {
                        log(`   ${index + 1}. ${stock.symbol} - ${stock.name}`);
                        log(`      价格: $${stock.current_price} (${stock.price_change_percent}%)`);
                        log(`      趋势分数: ${stock.trend_score}`);
                    });
                    
                    document.getElementById('result').className = 'result success';
                } else {
                    throw new Error('返回数据格式不正确');
                }
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
                document.getElementById('result').className = 'result error';
            }
        }

        // 页面加载完成后显示环境信息
        window.addEventListener('load', function() {
            log('页面加载完成');
            log(`当前URL: ${window.location.href}`);
            log(`目标服务器: http://localhost:8080`);
            log('准备就绪，点击按钮开始测试');
        });
    </script>
</body>
</html>
