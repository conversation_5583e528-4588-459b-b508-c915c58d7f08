<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻点击调试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@3"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.9em;
        }
        .chart-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            margin: 20px 0;
        }
        .chart-canvas {
            height: 500px;
            width: 100%;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            cursor: pointer;
        }
        .news-detail {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #dee2e6;
        }
        .btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .news-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .news-item.positive {
            border-left-color: #28a745;
            background: #f8fff9;
        }
        .news-item.negative {
            border-left-color: #dc3545;
            background: #fff8f8;
        }
    </style>
</head>
<body>
    <h1>🐛 新闻点击调试页面</h1>
    
    <div class="debug-info" id="debugInfo">
        调试信息将在这里显示...
    </div>
    
    <div>
        <button class="btn" onclick="loadStock('AAPL')">加载 AAPL</button>
        <button class="btn" onclick="loadStock('TSLA')">加载 TSLA</button>
        <button class="btn" onclick="toggleNews()">切换新闻显示</button>
        <button class="btn" onclick="clearDebug()">清除调试信息</button>
    </div>
    
    <div class="chart-container">
        <div>
            <canvas id="debugChart" class="chart-canvas"></canvas>
        </div>
        <div class="news-detail">
            <h3>📰 新闻详情</h3>
            <div id="newsDetail">
                <div style="text-align: center; color: #666; padding: 20px;">
                    点击图表上的彩色圆点查看新闻详情
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentChart = null;
        let currentStockData = null;
        let currentNewsData = null;
        let showNews = true;
        let debugMessages = [];

        function addDebugMessage(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugMessages.push(`[${timestamp}] ${message}`);
            document.getElementById('debugInfo').innerHTML = debugMessages.slice(-10).join('<br>');
            console.log(message);
        }

        function clearDebug() {
            debugMessages = [];
            document.getElementById('debugInfo').innerHTML = '调试信息已清除...';
        }

        async function loadStock(symbol) {
            addDebugMessage(`开始加载股票: ${symbol}`);
            
            try {
                const response = await fetch(`http://localhost:8080/api/stock/${symbol}`);
                const data = await response.json();
                
                if (response.ok) {
                    currentStockData = data.stock_data;
                    currentNewsData = data.news || [];
                    addDebugMessage(`股票数据加载成功: ${currentStockData.name}`);
                    addDebugMessage(`新闻数量: ${currentNewsData.length}`);
                    createChart();
                } else {
                    addDebugMessage(`加载失败: ${data.error}`);
                }
            } catch (error) {
                addDebugMessage(`网络错误: ${error.message}`);
            }
        }

        function toggleNews() {
            showNews = !showNews;
            addDebugMessage(`新闻显示: ${showNews ? '开启' : '关闭'}`);
            createChart();
        }

        function createChart() {
            if (!currentStockData) {
                addDebugMessage('没有股票数据，无法创建图表');
                return;
            }

            const canvas = document.getElementById('debugChart');
            const ctx = canvas.getContext('2d');

            if (currentChart) {
                currentChart.destroy();
            }

            const historicalData = currentStockData.historical_data || [];
            const labels = historicalData.map(item => item.date);
            const prices = historicalData.map(item => item.close);

            addDebugMessage(`创建图表，历史数据点数: ${historicalData.length}`);

            // 生成新闻标注
            const annotations = [];
            if (showNews && currentNewsData.length > 0) {
                currentNewsData.forEach((news, index) => {
                    const newsDate = new Date(news.published_time).toISOString().split('T')[0];
                    const dataPoint = historicalData.find(item => item.date === newsDate);
                    
                    if (dataPoint) {
                        let color = '#6c757d';
                        if (news.sentiment === 'positive') color = '#28a745';
                        if (news.sentiment === 'negative') color = '#dc3545';
                        
                        // 垂直线
                        annotations.push({
                            type: 'line',
                            mode: 'vertical',
                            scaleID: 'x',
                            value: newsDate,
                            borderColor: color,
                            borderWidth: 2,
                            borderDash: [5, 5]
                        });
                        
                        // 可点击圆点
                        annotations.push({
                            type: 'point',
                            scaleID: 'x',
                            value: newsDate,
                            yValue: dataPoint.close,
                            backgroundColor: color,
                            borderColor: 'white',
                            borderWidth: 3,
                            radius: 15,
                            newsIndex: index
                        });
                        
                        addDebugMessage(`添加新闻标注 ${index}: ${newsDate} - ${news.title.substring(0, 30)}...`);
                    }
                });
            }

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `${currentStockData.symbol} 收盘价`,
                        data: prices,
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1,
                        pointRadius: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentStockData.name} (${currentStockData.symbol}) - 调试模式`,
                            font: { size: 16, weight: 'bold' }
                        },
                        annotation: {
                            annotations: annotations
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: { unit: 'day', displayFormats: { day: 'MM/dd' } },
                            title: { display: true, text: '日期' }
                        },
                        y: {
                            title: { display: true, text: '价格 ($)' }
                        }
                    },
                    onClick: function(event, elements) {
                        addDebugMessage('图表被点击');
                        handleChartClick(event, elements, this);
                    }
                }
            });

            addDebugMessage(`图表创建完成，标注数量: ${annotations.length}`);
        }

        function handleChartClick(event, elements, chart) {
            if (!showNews || !currentNewsData) {
                addDebugMessage('新闻未启用或无新闻数据');
                return;
            }

            const canvasPosition = Chart.helpers.getRelativePosition(event, chart);
            addDebugMessage(`点击位置: x=${canvasPosition.x.toFixed(0)}, y=${canvasPosition.y.toFixed(0)}`);

            const historicalData = currentStockData.historical_data || [];
            const clickThreshold = 25;
            let foundNews = false;

            currentNewsData.forEach((news, index) => {
                const newsDate = new Date(news.published_time).toISOString().split('T')[0];
                const dataPoint = historicalData.find(item => item.date === newsDate);
                
                if (dataPoint) {
                    const annotationX = chart.scales.x.getPixelForValue(newsDate);
                    const annotationY = chart.scales.y.getPixelForValue(dataPoint.close);
                    
                    const distance = Math.sqrt(
                        Math.pow(canvasPosition.x - annotationX, 2) + 
                        Math.pow(canvasPosition.y - annotationY, 2)
                    );
                    
                    addDebugMessage(`新闻 ${index}: 位置(${annotationX.toFixed(0)}, ${annotationY.toFixed(0)}), 距离: ${distance.toFixed(1)}`);
                    
                    if (distance <= clickThreshold) {
                        addDebugMessage(`✅ 点击了新闻 ${index}: ${news.title}`);
                        showNewsDetail(index);
                        foundNews = true;
                        return;
                    }
                }
            });

            if (!foundNews) {
                addDebugMessage('❌ 没有点击到任何新闻标注点');
            }
        }

        function showNewsDetail(newsIndex) {
            if (!currentNewsData || newsIndex >= currentNewsData.length) {
                addDebugMessage('无效的新闻索引');
                return;
            }

            const news = currentNewsData[newsIndex];
            const sentimentClass = news.sentiment || 'neutral';
            const sentimentText = {
                'positive': '积极',
                'negative': '消极',
                'neutral': '中性'
            }[sentimentClass] || '中性';

            document.getElementById('newsDetail').innerHTML = `
                <div class="news-item ${sentimentClass}">
                    <h4>${news.title}</h4>
                    <p>${news.summary || '暂无摘要'}</p>
                    <div style="font-size: 0.9em; color: #666; margin-top: 10px;">
                        <strong>来源:</strong> ${news.source}<br>
                        <strong>时间:</strong> ${new Date(news.published_time).toLocaleString()}<br>
                        <strong>情绪:</strong> ${sentimentText}
                    </div>
                    ${news.url ? `<div style="margin-top: 10px;">
                        <a href="${news.url}" target="_blank" style="color: #007bff;">🔗 查看原文</a>
                    </div>` : ''}
                </div>
            `;

            addDebugMessage(`✅ 显示新闻详情: ${news.title.substring(0, 50)}...`);
        }

        // 页面加载完成后自动加载AAPL
        window.addEventListener('load', function() {
            addDebugMessage('页面加载完成，开始加载AAPL数据');
            loadStock('AAPL');
        });
    </script>
</body>
</html>
