<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>旅行推荐调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .travel-card {
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            background: white;
        }
        .travel-image {
            width: 100%;
            height: 200px;
            position: relative;
            background: #f0f0f0;
        }
        .travel-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3em;
        }
        .travel-info {
            padding: 20px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🧪 旅行推荐调试</h1>
    
    <div class="debug-section">
        <h2>API数据测试</h2>
        <button onclick="testAPI()">测试API数据</button>
        <div id="apiLog" class="log"></div>
    </div>

    <div class="debug-section">
        <h2>图片加载测试</h2>
        <button onclick="testImages()">测试图片加载</button>
        <div id="imageLog" class="log"></div>
        
        <div class="travel-card">
            <div class="travel-image">
                <img id="testImage" src="" alt="测试图片" style="display: none;">
                <div class="placeholder" id="testPlaceholder">🏞️</div>
            </div>
            <div class="travel-info">
                <h3 id="testName">测试景点</h3>
                <p id="testDescription">测试描述</p>
                <div id="testDetails">测试详情</div>
            </div>
        </div>
    </div>

    <script>
        function log(elementId, message) {
            const logElement = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        async function testAPI() {
            const logId = 'apiLog';
            document.getElementById(logId).innerHTML = '';
            
            log(logId, '开始测试API...');
            
            try {
                const response = await fetch('http://localhost:8080/api/weather/Beijing');
                log(logId, `API响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(logId, '✅ API调用成功');
                    
                    if (data.travel_recommendations) {
                        log(logId, '✅ 包含旅行推荐数据');
                        log(logId, `周边推荐数量: ${data.travel_recommendations.nearby.length}`);
                        log(logId, `国内推荐数量: ${data.travel_recommendations.domestic.length}`);
                        log(logId, `国外推荐数量: ${data.travel_recommendations.international.length}`);
                        
                        // 显示第一个周边推荐的详细信息
                        const firstNearby = data.travel_recommendations.nearby[0];
                        log(logId, `第一个周边推荐: ${firstNearby.name}`);
                        log(logId, `图片URL: ${firstNearby.image}`);
                        
                        // 存储数据供图片测试使用
                        window.testData = data.travel_recommendations;
                    } else {
                        log(logId, '❌ 没有旅行推荐数据');
                    }
                } else {
                    log(logId, `❌ API调用失败: ${response.status}`);
                }
            } catch (error) {
                log(logId, `❌ 网络错误: ${error.message}`);
            }
        }

        function testImages() {
            const logId = 'imageLog';
            document.getElementById(logId).innerHTML = '';
            
            if (!window.testData) {
                log(logId, '❌ 请先测试API获取数据');
                return;
            }
            
            log(logId, '开始测试图片加载...');
            
            const testItem = window.testData.nearby[0];
            log(logId, `测试景点: ${testItem.name}`);
            log(logId, `图片URL: ${testItem.image}`);
            
            // 更新卡片信息
            document.getElementById('testName').textContent = testItem.name;
            document.getElementById('testDescription').textContent = testItem.description;
            document.getElementById('testDetails').textContent = `${testItem.distance} | ${testItem.travel_time}`;
            
            // 测试图片加载
            const img = document.getElementById('testImage');
            const placeholder = document.getElementById('testPlaceholder');
            
            // 重置状态
            img.style.display = 'none';
            placeholder.style.display = 'flex';
            img.src = '';
            
            // 设置加载事件
            img.onload = function() {
                log(logId, '✅ 图片加载成功');
                this.style.display = 'block';
                placeholder.style.display = 'none';
            };
            
            img.onerror = function() {
                log(logId, '❌ 图片加载失败');
                this.style.display = 'none';
                placeholder.style.display = 'flex';
                placeholder.innerHTML = '❌';
            };
            
            // 开始加载图片
            log(logId, '开始加载图片...');
            img.src = testItem.image;
        }

        // 页面加载完成后自动测试API
        window.addEventListener('load', function() {
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
