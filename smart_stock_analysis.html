<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能股票分析</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@3"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1800px;
            margin: 0 auto;
            padding: 15px;
            background: #f5f5f5;
            overflow-x: hidden;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
        .header h1 {
            margin: 10px 0;
            font-size: 1.8em;
        }
        .header p {
            margin: 5px 0;
            font-size: 0.95em;
        }
        .search-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }
        .search-box {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
        }
        .search-input {
            flex: 1;
            padding: 12px 20px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1.1em;
            outline: none;
            transition: border-color 0.3s ease;
        }
        .search-input:focus {
            border-color: #007bff;
        }
        .search-btn {
            padding: 12px 25px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            transition: background 0.3s ease;
        }
        .search-btn:hover {
            background: #0056b3;
        }
        .period-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .period-btn {
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .period-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .search-results {
            margin-top: 20px;
        }
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            margin: 8px 0;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .result-item:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }
        .result-info {
            flex: 1;
        }
        .result-symbol {
            font-weight: bold;
            color: #007bff;
            font-size: 1.1em;
        }
        .result-name {
            color: #666;
            font-size: 0.9em;
        }
        .result-sector {
            color: #999;
            font-size: 0.8em;
        }
        .analysis-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            display: none;
        }
        .stock-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 12px;
            border-bottom: 2px solid #dee2e6;
        }
        .stock-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
        }
        .stock-price {
            text-align: right;
        }
        .current-price {
            font-size: 1.8em;
            font-weight: bold;
        }
        .price-change {
            font-size: 1.1em;
            margin-top: 5px;
        }
        .price-up {
            color: #28a745;
        }
        .price-down {
            color: #dc3545;
        }
        .chart-canvas {
            height: 300px;
            width: 100%;
            margin: 15px 0;
        }
        .analysis-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        .analysis-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }
        .card-title {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 12px;
            color: #333;
        }
        .impact-summary {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 12px;
            margin: 12px 0;
            border-radius: 0 6px 6px 0;
            font-size: 0.9em;
        }
        .impact-event {
            background: white;
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            border-left: 4px solid #007bff;
        }
        .impact-event.positive {
            border-left-color: #28a745;
        }
        .impact-event.negative {
            border-left-color: #dc3545;
        }
        .event-title {
            font-weight: bold;
            margin-bottom: 6px;
            font-size: 0.9em;
        }
        .event-impact {
            display: flex;
            justify-content: space-between;
            font-size: 0.85em;
            color: #666;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .analysis-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            .chart-canvas {
                height: 250px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .search-container, .analysis-container {
                padding: 15px;
                margin: 10px 0;
            }
            .chart-canvas {
                height: 200px;
            }
            .period-selector {
                flex-wrap: wrap;
                gap: 8px;
            }
            .period-btn {
                padding: 6px 12px;
                font-size: 0.9em;
            }
        }

        /* 确保内容在一屏内显示 */
        .analysis-container {
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }

        .analysis-card {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 智能股票分析</h1>
        <p>输入股票代码或公司名称，查看股价趋势和新闻影响分析</p>
    </div>

    <div class="search-container">
        <div class="search-box">
            <input type="text" class="search-input" id="searchInput" placeholder="输入股票代码或公司名称（如：AAPL、Apple、苹果）" onkeypress="handleSearchKeyPress(event)">
            <button class="search-btn" onclick="searchStock()">🔍 搜索</button>
        </div>
        
        <div class="period-selector">
            <span style="margin-right: 10px; font-weight: bold;">时间段：</span>
            <button class="period-btn" onclick="selectPeriod('1d', this)">1天</button>
            <button class="period-btn active" onclick="selectPeriod('1w', this)">1周</button>
            <button class="period-btn" onclick="selectPeriod('1mo', this)">1个月</button>
            <button class="period-btn" onclick="selectPeriod('3mo', this)">3个月</button>
            <button class="period-btn" onclick="selectPeriod('6mo', this)">6个月</button>
            <button class="period-btn" onclick="selectPeriod('1y', this)">1年</button>
        </div>
        
        <div class="search-results" id="searchResults"></div>
    </div>

    <div class="analysis-container" id="analysisContainer">
        <div class="stock-header">
            <div class="stock-info">
                <div class="stock-title" id="stockTitle">股票信息</div>
                <div class="stock-meta" id="stockMeta">加载中...</div>
            </div>
            <div class="stock-price">
                <div class="current-price" id="currentPrice">$0.00</div>
                <div class="price-change" id="priceChange">+0.00 (0.00%)</div>
            </div>
        </div>
        
        <canvas id="stockChart" class="chart-canvas"></canvas>
        
        <div class="analysis-grid">
            <div class="analysis-card">
                <div class="card-title">📊 价格变动分析</div>
                <div id="priceAnalysis">加载中...</div>
            </div>
            
            <div class="analysis-card">
                <div class="card-title">📰 新闻影响分析</div>
                <div id="newsImpactAnalysis">加载中...</div>
            </div>
        </div>
    </div>

    <script>
        let currentChart = null;
        let currentSymbol = '';
        let currentPeriod = '1w';
        let currentStockData = null;
        let currentNewsData = null;

        // 处理搜索框回车事件
        function handleSearchKeyPress(event) {
            if (event.key === 'Enter') {
                searchStock();
            }
        }

        // 选择时间段
        function selectPeriod(period, buttonElement) {
            currentPeriod = period;

            // 更新按钮状态
            document.querySelectorAll('.period-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 如果传入了按钮元素，使用它；否则查找对应的按钮
            if (buttonElement) {
                buttonElement.classList.add('active');
            } else {
                // 根据period查找对应的按钮
                const buttons = document.querySelectorAll('.period-btn');
                buttons.forEach(btn => {
                    if (btn.textContent.includes(getPeriodText(period))) {
                        btn.classList.add('active');
                    }
                });
            }

            // 如果已选择股票，重新加载数据
            if (currentSymbol) {
                loadStockData(currentSymbol);
            }
        }

        // 搜索股票
        async function searchStock() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) return;

            const resultsContainer = document.getElementById('searchResults');
            resultsContainer.innerHTML = '<div class="loading">🔍 搜索中...</div>';

            try {
                const response = await fetch(`http://localhost:8080/api/search/${encodeURIComponent(query)}`);
                const data = await response.json();

                if (data.success && data.results.length > 0) {
                    let resultsHTML = '<div style="font-weight: bold; margin-bottom: 10px;">搜索结果：</div>';
                    
                    data.results.forEach(result => {
                        resultsHTML += `
                            <div class="result-item" onclick="selectStock('${result.symbol}')">
                                <div class="result-info">
                                    <div class="result-symbol">${result.symbol}</div>
                                    <div class="result-name">${result.name}</div>
                                    <div class="result-sector">${result.sector}</div>
                                </div>
                                <div style="color: #007bff;">选择 →</div>
                            </div>
                        `;
                    });
                    
                    resultsContainer.innerHTML = resultsHTML;
                } else {
                    resultsContainer.innerHTML = '<div class="error">未找到匹配的股票，请尝试其他关键词</div>';
                }
            } catch (error) {
                resultsContainer.innerHTML = '<div class="error">搜索失败，请检查网络连接</div>';
                console.error('搜索失败:', error);
            }
        }

        // 选择股票
        function selectStock(symbol) {
            currentSymbol = symbol;
            document.getElementById('searchResults').innerHTML = '';
            document.getElementById('searchInput').value = symbol;
            loadStockData(symbol);
        }

        // 加载股票数据
        async function loadStockData(symbol) {
            const container = document.getElementById('analysisContainer');
            container.style.display = 'block';
            
            // 显示加载状态
            document.getElementById('stockTitle').textContent = '加载中...';
            document.getElementById('currentPrice').textContent = '$0.00';
            document.getElementById('priceChange').textContent = '加载中...';
            document.getElementById('priceAnalysis').innerHTML = '<div class="loading">📊 分析中...</div>';
            document.getElementById('newsImpactAnalysis').innerHTML = '<div class="loading">📰 分析中...</div>';

            try {
                const response = await fetch(`http://localhost:8080/api/stock/${symbol}?period=${currentPeriod}`);
                const data = await response.json();

                if (data.success) {
                    currentStockData = data.stock_data;
                    currentNewsData = data.news;

                    // 调试信息：输出接收到的数据
                    console.log('接收到的股票数据:', {
                        symbol: data.stock_data.symbol,
                        period: data.stock_data.period,
                        current_period: currentPeriod,
                        daily_change: data.stock_data.daily_change,
                        daily_change_percent: data.stock_data.daily_change_percent,
                        period_change: data.stock_data.period_change,
                        period_change_percent: data.stock_data.period_change_percent,
                        historical_data_length: data.stock_data.historical_data?.length
                    });

                    updateStockHeader(data.stock_data);
                    createStockChart(data.stock_data);
                    updatePriceAnalysis(data.stock_data);
                    updateNewsImpactAnalysis(data.news_impact_analysis);
                } else {
                    showError(data.error || '获取股票数据失败');
                }
            } catch (error) {
                showError('网络错误，请稍后重试');
                console.error('加载股票数据失败:', error);
            }
        }

        // 更新股票头部信息
        function updateStockHeader(stockData) {
            document.getElementById('stockTitle').textContent = `${stockData.name} (${stockData.symbol})`;
            document.getElementById('stockMeta').textContent = `${stockData.sector} • ${getPeriodText(currentPeriod)}`;
            document.getElementById('currentPrice').textContent = `$${stockData.current_price}`;

            // 根据时间段显示对应的变化
            let changePercent, changeAmount;
            if (currentPeriod === '1d') {
                // 1天显示日变化
                changePercent = stockData.daily_change_percent || 0;
                changeAmount = stockData.daily_change || 0;
            } else {
                // 其他时间段显示期间变化
                changePercent = stockData.period_change_percent || 0;
                changeAmount = stockData.period_change || 0;
            }

            const changeClass = changePercent >= 0 ? 'price-up' : 'price-down';
            const changeSymbol = changePercent >= 0 ? '+' : '';

            document.getElementById('priceChange').innerHTML =
                `<span class="${changeClass}">${changeSymbol}${changeAmount.toFixed(2)} (${changeSymbol}${changePercent.toFixed(2)}%)</span>`;
        }

        // 创建股价图表
        function createStockChart(stockData) {
            const canvas = document.getElementById('stockChart');
            const ctx = canvas.getContext('2d');

            if (currentChart) {
                currentChart.destroy();
            }

            const historicalData = stockData.historical_data || [];
            const labels = historicalData.map(item => item.date);
            const prices = historicalData.map(item => item.close);

            // 生成新闻标注
            const annotations = [];
            if (currentNewsData && currentNewsData.length > 0) {
                currentNewsData.forEach((news, index) => {
                    const newsDate = new Date(news.published_time).toISOString().split('T')[0];
                    const dataPoint = historicalData.find(item => item.date === newsDate);
                    
                    if (dataPoint) {
                        let color = '#6c757d';
                        let emoji = '📰';
                        if (news.sentiment === 'positive') {
                            color = '#28a745';
                            emoji = '📈';
                        } else if (news.sentiment === 'negative') {
                            color = '#dc3545';
                            emoji = '📉';
                        }
                        
                        annotations.push({
                            type: 'line',
                            mode: 'vertical',
                            scaleID: 'x',
                            value: newsDate,
                            borderColor: color,
                            borderWidth: 2,
                            borderDash: [5, 5],
                            label: {
                                enabled: true,
                                content: `${emoji}`,
                                position: 'top',
                                backgroundColor: color,
                                color: 'white',
                                font: { size: 12, weight: 'bold' },
                                padding: 4,
                                cornerRadius: 4
                            }
                        });
                        
                        annotations.push({
                            type: 'point',
                            scaleID: 'x',
                            value: newsDate,
                            yValue: dataPoint.close,
                            backgroundColor: color,
                            borderColor: 'white',
                            borderWidth: 2,
                            radius: 6
                        });
                    }
                });
            }

            // 根据时间段选择正确的变化百分比来着色
            let changePercent;
            if (currentPeriod === '1d') {
                changePercent = stockData.daily_change_percent || 0;
            } else {
                changePercent = stockData.period_change_percent || 0;
            }

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `${stockData.symbol} 收盘价`,
                        data: prices,
                        borderColor: changePercent >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: changePercent >= 0 ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.1,
                        pointRadius: 4,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${stockData.name} - ${getPeriodText(currentPeriod)}股价走势`,
                            font: { size: 16, weight: 'bold' }
                        },
                        annotation: {
                            annotations: annotations
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: getTimeUnit(currentPeriod),
                                displayFormats: getDisplayFormats(currentPeriod)
                            },
                            title: { display: true, text: '时间' }
                        },
                        y: {
                            title: { display: true, text: '价格 ($)' },
                            beginAtZero: false
                        }
                    }
                }
            });
        }

        // 更新价格分析
        function updatePriceAnalysis(stockData) {
            // 根据时间段显示对应的变化
            let changePercent, changeAmount;
            if (currentPeriod === '1d') {
                changePercent = stockData.daily_change_percent || 0;
                changeAmount = stockData.daily_change || 0;
            } else {
                changePercent = stockData.period_change_percent || 0;
                changeAmount = stockData.period_change || 0;
            }

            const periodText = getPeriodText(currentPeriod);

            let trendText = '';
            let trendColor = '';
            if (changePercent > 2) {
                trendText = '强势上涨';
                trendColor = '#28a745';
            } else if (changePercent > 0) {
                trendText = '温和上涨';
                trendColor = '#28a745';
            } else if (changePercent > -2) {
                trendText = '小幅下跌';
                trendColor = '#dc3545';
            } else {
                trendText = '大幅下跌';
                trendColor = '#dc3545';
            }

            const analysisHTML = `
                <div style="margin-bottom: 15px;">
                    <strong>期间表现：</strong>
                    <span style="color: ${trendColor}; font-weight: bold;">${trendText}</span>
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>${periodText}涨跌：</strong>
                    <span style="color: ${changePercent >= 0 ? '#28a745' : '#dc3545'};">
                        ${changePercent >= 0 ? '+' : ''}${changeAmount.toFixed(2)} (${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)
                    </span>
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>期间最高：</strong> $${stockData.period_high || stockData.week_52_high}
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>期间最低：</strong> $${stockData.period_low || stockData.week_52_low}
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>成交量：</strong> ${formatVolume(stockData.volume)}
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>数据期间：</strong> ${periodText}
                </div>
            `;

            document.getElementById('priceAnalysis').innerHTML = analysisHTML;
        }

        // 更新新闻影响分析
        function updateNewsImpactAnalysis(impactAnalysis) {
            if (!impactAnalysis || !impactAnalysis.impact_events || impactAnalysis.impact_events.length === 0) {
                document.getElementById('newsImpactAnalysis').innerHTML = 
                    '<div style="color: #666;">暂无新闻影响数据</div>';
                return;
            }

            let analysisHTML = `
                <div class="impact-summary">
                    <strong>影响摘要：</strong><br>
                    ${impactAnalysis.summary}
                </div>
            `;

            // 显示前3个最重要的影响事件
            const topEvents = impactAnalysis.impact_events.slice(0, 3);
            topEvents.forEach(event => {
                const impactClass = event.news_sentiment;
                const impactIcon = event.news_sentiment === 'positive' ? '📈' : 
                                 event.news_sentiment === 'negative' ? '📉' : '📰';
                
                analysisHTML += `
                    <div class="impact-event ${impactClass}">
                        <div class="event-title">
                            ${impactIcon} ${event.news_title.substring(0, 50)}...
                        </div>
                        <div class="event-impact">
                            <span>发布日期: ${event.news_date}</span>
                            <span style="color: ${event.cumulative_3day_change >= 0 ? '#28a745' : '#dc3545'}; font-weight: bold;">
                                3日影响: ${event.cumulative_3day_change >= 0 ? '+' : ''}${event.cumulative_3day_change}%
                            </span>
                        </div>
                    </div>
                `;
            });

            document.getElementById('newsImpactAnalysis').innerHTML = analysisHTML;
        }

        // 辅助函数
        function getPeriodText(period) {
            const periodMap = {
                '1d': '1天',
                '1w': '1周',
                '1mo': '1个月',
                '3mo': '3个月',
                '6mo': '6个月',
                '1y': '1年'
            };
            return periodMap[period] || '1个月';
        }

        function getTimeUnit(period) {
            if (period === '1d') return 'hour';
            if (period === '1w') return 'day';
            if (period === '1mo' || period === '3mo') return 'day';
            return 'month';
        }

        function getDisplayFormats(period) {
            if (period === '1d') return { hour: 'HH:mm' };
            if (period === '1w' || period === '1mo') return { day: 'MM/dd' };
            return { month: 'MM/yy' };
        }

        function formatVolume(volume) {
            if (volume >= 1000000000) return (volume / 1000000000).toFixed(1) + 'B';
            if (volume >= 1000000) return (volume / 1000000).toFixed(1) + 'M';
            if (volume >= 1000) return (volume / 1000).toFixed(1) + 'K';
            return volume.toString();
        }

        function showError(message) {
            document.getElementById('priceAnalysis').innerHTML = `<div class="error">${message}</div>`;
            document.getElementById('newsImpactAnalysis').innerHTML = `<div class="error">${message}</div>`;
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            // 可以在这里添加一些示例搜索或默认加载
        });
    </script>
</body>
</html>
