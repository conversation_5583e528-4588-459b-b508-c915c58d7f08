# 🌤️ 智能天气穿衣助手

一个功能完整的天气查询系统，提供命令行版本和Web版本，支持查询世界各个城市的实时天气信息，并集成Google Gemini AI提供智能穿衣建议。

## 功能特点

- 🌍 支持查询全球任意城市的天气信息
- 🌡️ 显示温度、体感温度、湿度、气压、风速等详细信息
- 🌤️ 支持中文天气描述
- 💬 支持中文和英文城市名称输入
- 🤖 AI智能穿衣建议，集成Google Gemini提供专业、个性化的穿衣建议
- 👗 基础穿衣建议，当AI不可用时提供备用建议方案
- 🛒 智能购买推荐，根据天气状况推荐相应衣物购买链接
- ✈️ 当月旅行推荐，智能推荐周边游、国内游、出境游目的地
- 🌐 Web界面，美观易用的图形化界面
- 📍 自动定位，页面加载时自动获取用户位置
- 🏙️ 城市下拉选择，支持中国和国际主要城市
- 🎨 不同天气背景，根据天气状况动态改变背景和卡片样式
- 🌤️ 天气图标动画，不同天气状况的图标动画效果
- 📱 响应式设计，支持手机和电脑访问
- 🔄 实时更新，即时获取最新天气和建议

## 安装依赖

```bash
pip install -r requirements.txt
```

## 获取API密钥

### OpenWeatherMap API（必需）
1. 访问 [OpenWeatherMap API](https://openweathermap.org/api)
2. 注册免费账户
3. 获取免费的API密钥
4. 将API密钥替换到 `main.py` 文件中

### Google Gemini API（可选，用于AI穿衣建议）
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建API密钥
3. 将API密钥替换到 `main.py` 文件中的 `YOUR_GEMINI_API_KEY_HERE`
4. 如果不配置，程序将使用基础穿衣建议功能

## 使用方法

### 🖥️ 命令行版本
```bash
python main.py
```

### 🌐 Web版本（推荐）
```bash
python run_web.py
```
然后在浏览器中访问：http://localhost:8080

## 🌐 Web版本功能

### 界面特点
- **🎨 美观界面**：渐变背景、圆角卡片、现代化设计
- **📱 响应式**：自适应手机、平板、电脑屏幕
- **🌈 动画效果**：加载动画、淡入效果、悬停交互

### 功能特色
- **📍 智能定位**：页面加载时自动获取用户位置，一键定位当前城市
- **🏙️ 城市选择**：下拉菜单包含20+热门城市，支持手动选择
- **🌤️ 动态背景**：根据天气状况自动切换背景和卡片样式
- **🎨 天气图标**：根据天气状况自动显示对应emoji图标和动画
- **📊 详细信息**：温度、体感、湿度、气压、风速
- **🤖 AI建议**：实时生成个性化穿衣建议
- **🛒 购买推荐**：智能推荐相应衣物的购买链接
- **✈️ 旅行推荐**：当月最佳旅行目的地推荐
- **⏰ 时间戳**：显示数据更新时间

### 天气图标说明
- ☀️ 炎热晴天 (>30°C)
- 🌤️ 温暖晴天 (20-30°C)
- 🌞 凉爽晴天 (<20°C)
- ⛅ 温暖多云 (>25°C)
- ☁️ 凉爽多云 (<25°C)
- 🌧️ 雨天
- ❄️ 雪天
- 🌫️ 雾霾天

### 🛒 智能购买推荐

#### 推荐逻辑
- **温度分级**：根据体感温度推荐不同厚度的衣物
- **天气状况**：根据雨雪雾等特殊天气推荐专用装备
- **风速考虑**：大风天气推荐防风衣物
- **季节适配**：结合温度和天气的综合推荐

#### 推荐类别
- **上衣**：T恤、衬衫、毛衣等
- **外套**：薄外套、厚外套、羽绒服、防风衣等
- **防晒**：遮阳帽、太阳镜、防晒霜等
- **雨具**：雨伞、雨衣等
- **鞋子**：防滑靴、运动鞋等
- **配饰**：手套、围巾、帽子等

#### 购买平台
- **淘宝**：海量商品选择
- **京东**：品质保证，快速配送
- **天猫**：品牌正品，优质服务

#### 使用方式
1. 查看天气信息后，向下滚动到"推荐购买"区域
2. 每个推荐卡片显示：商品类别、具体物品、推荐原因
3. 点击购买链接直接跳转到对应平台搜索页面
4. 不同类别的卡片有不同的颜色标识

### ✈️ 智能旅行推荐

#### 推荐分类
- **🚗 周边游**：基于当前城市的周边景点推荐
  - 显示距离和行车时间
  - 适合周末短途旅行
  - 包含热门景区和特色景点

- **🏔️ 国内游**：根据当前月份的国内最佳旅行目的地
  - 考虑季节性因素（如花期、气候）
  - 推荐理由说明
  - 涵盖自然风光和人文景观

- **🌍 出境游**：当月国外热门旅行目的地
  - 考虑当地最佳旅行季节
  - 签证便利度和安全性
  - 特色体验和文化亮点

#### 功能特色
- **景区图片**：每个推荐都配有精美的景区照片
- **详细信息**：包含景点描述、推荐理由、距离/城市信息
- **一键刷新**：点击🔄按钮即可切换到其他推荐景点
- **分步展示**：在天气和购买推荐后依次显示，避免信息过载

#### 推荐算法
- **地理位置匹配**：根据用户选择的城市智能匹配周边景点
- **季节性推荐**：根据当前月份推荐最适合的旅行目的地
- **多样化选择**：每个类别提供多个选项，支持随机切换

#### 使用方式
1. 查看天气信息和穿衣建议后，向下滚动到"当月旅行推荐"区域
2. 浏览三个类别的推荐：周边游、国内游、出境游
3. 点击任意类别右上角的🔄按钮刷新推荐
4. 查看景区图片和详细信息，规划您的旅行

### 📍 地理位置功能

#### 自动定位
- **页面加载时**：自动尝试获取用户位置（静默模式）
- **成功定位**：自动选择对应城市并显示天气
- **定位失败**：静默失败，用户可手动选择城市

#### 手动定位
- **定位按钮**：点击"📍 获取我的位置"按钮
- **权限请求**：浏览器会请求位置权限
- **状态显示**：实时显示定位状态和结果
- **错误处理**：详细的错误信息和解决建议

#### 位置匹配
- **智能匹配**：多层级城市名称匹配算法
  - 精确匹配：直接匹配城市名称
  - 别名匹配：支持城市别名（如Peking→Beijing）
  - 包含匹配：部分名称匹配
  - 模糊匹配：去除空格和特殊字符后匹配
- **国家智能推断**：根据国家代码智能推断城市
- **坐标查询**：如果列表中没有该城市，直接使用坐标查询天气
- **反向地理编码**：使用OpenWeatherMap API解析坐标为城市名

#### 隐私保护
- **用户控制**：完全由用户控制是否允许定位
- **不存储位置**：不保存用户位置信息
- **安全传输**：位置数据仅用于天气查询

## 使用示例

```
🌤️  欢迎使用世界天气查询系统！
💡 提示: 您可以输入中文或英文城市名称
💡 示例: 北京, Beijing, 上海, Shanghai, New York, London
💡 输入 'quit' 或 'exit' 退出程序

🏙️  请输入要查询的城市名称: 北京
🔍 正在查询 '北京' 的天气信息...

============================================================
🌍 城市: Beijing, CN
🌡️  当前温度: 15.2°C
🤔 体感温度: 14.8°C
☁️  天气状况: 晴
💧 湿度: 45%
📊 气压: 1015 hPa
💨 风速: 2.1 m/s
⏰ 查询时间: 2025-01-17 10:30:15

👗 AI穿衣建议:
   1. 🧥 建议穿薄毛衣或轻便夹克，既保暖又不会过热
   2. 👖 下装可选择长裤或厚一点的牛仔裤
   3. 👟 穿舒适的运动鞋或休闲鞋
   4. ☀️ 天气晴朗但温度适中，可准备一副太阳镜
   5. 💧 空气相对干燥，建议涂抹保湿霜
============================================================
```

## 支持的城市格式

### 🏙️ 下拉列表城市
- **中国城市**：北京、上海、广州、深圳、杭州、南京、成都、武汉、西安、重庆
- **国际城市**：东京、首尔、新加坡、曼谷、纽约、伦敦、巴黎、悉尼、迪拜、莫斯科

### 🔄 智能匹配别名
- **北京**：Beijing, Peking
- **上海**：Shanghai
- **广州**：Guangzhou, Canton
- **纽约**：New York, NYC, New York City
- **东京**：Tokyo
- **伦敦**：London
- **巴黎**：Paris
- **莫斯科**：Moscow, Moskva

### 📍 定位支持
- 自动识别用户位置并匹配到最近的支持城市
- 如果当前城市不在列表中，直接使用坐标查询天气

## 注意事项

- 需要稳定的网络连接
- API密钥每月有免费调用次数限制
- 部分小城市可能无法查询到信息

## AI穿衣建议功能

程序集成了Google Gemini AI，提供两种穿衣建议模式：

### 🤖 AI智能建议（推荐）
使用Google Gemini AI分析天气数据，提供：
- 专业的时尚搭配建议
- 个性化的服装选择
- 考虑舒适度和实用性的综合建议
- 自然语言表达的贴心提醒

### 👗 基础建议（备用）
当AI不可用时，程序会根据以下因素提供基础建议：

### 温度建议
- **30°C以上**: 轻薄透气衣物，防晒用品
- **25-30°C**: 短袖衬衫、薄长裤
- **20-25°C**: 长袖衬衫或薄外套
- **15-20°C**: 薄毛衣或夹克外套
- **10-15°C**: 厚外套或毛衣
- **5-10°C**: 厚大衣，内搭毛衣
- **0-5°C**: 羽绒服，手套围巾
- **0°C以下**: 厚羽绒服，全身保暖

### 特殊天气建议
- **下雨**: 提醒带雨伞或穿雨衣
- **下雪**: 建议穿防滑鞋，注意保暖防湿
- **大风**: 建议穿防风外套
- **高湿度**: 选择透气性好的衣物
- **低湿度**: 注意皮肤保湿
- **雾霾**: 建议戴口罩

## 错误处理

程序包含完善的错误处理机制：
- API密钥验证
- 网络连接检查
- 城市名称验证
- 数据格式验证