<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多选图表功能演示</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .chart-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .chart-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .chart-checkboxes {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        .chart-checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: background 0.3s ease;
        }
        .chart-checkbox:hover {
            background: #e9ecef;
        }
        .chart-checkbox input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
            cursor: pointer;
        }
        .checkbox-label {
            font-size: 0.95em;
            font-weight: 500;
            color: #333;
            cursor: pointer;
        }
        .chart-actions {
            display: flex;
            gap: 10px;
        }
        .chart-btn {
            padding: 6px 12px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.85em;
            transition: background 0.3s ease;
        }
        .chart-btn:hover {
            background: #5a6268;
        }
        .multi-chart-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            max-height: 600px;
            overflow-y: auto;
        }
        .chart-canvas {
            width: 100%;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            transition: height 0.3s ease;
        }
        /* 动态高度调整 */
        .chart-canvas.single { height: 400px; }
        .chart-canvas.dual { height: 280px; }
        .chart-canvas.triple { height: 180px; }

        /* 网格布局选项 */
        .multi-chart-container.grid-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto;
            gap: 15px;
            max-height: 500px;
        }
        .multi-chart-container.grid-layout .chart-canvas {
            height: 240px;
        }
        .multi-chart-container.grid-layout .chart-canvas.full-width {
            grid-column: 1 / -1;
            height: 200px;
        }
        .stock-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .stock-btn {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }
        .stock-btn:hover {
            background: #218838;
        }
        .stock-btn.active {
            background: #dc3545;
        }
        .feature-highlight {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        .feature-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 多选图表功能演示</h1>
        <p>价格、K线和成交量可以同时多选展示</p>
    </div>

    <div class="demo-section">
        <div class="section-title">🎯 功能亮点</div>
        <div class="feature-highlight">
            <div class="feature-title">✨ 多图表同时显示</div>
            <div>可以同时选择显示价格走势图、K线图和成交量图，每个图表独立展示，互不干扰。</div>
        </div>
        <div class="feature-highlight">
            <div class="feature-title">🎛️ 灵活控制</div>
            <div>通过复选框可以自由选择要显示的图表类型，支持全选、清空和重置默认等快捷操作。</div>
        </div>
        <div class="feature-highlight">
            <div class="feature-title">📈 专业图表</div>
            <div>每个图表都采用专业的金融图表样式，包含完整的OHLCV数据展示。</div>
        </div>
    </div>

    <div class="demo-section">
        <div class="section-title">🎯 股票选择</div>
        <div class="stock-selector">
            <button class="stock-btn active" onclick="loadStockChart('AAPL')">AAPL - Apple</button>
            <button class="stock-btn" onclick="loadStockChart('MSFT')">MSFT - Microsoft</button>
            <button class="stock-btn" onclick="loadStockChart('GOOGL')">GOOGL - Google</button>
            <button class="stock-btn" onclick="loadStockChart('TSLA')">TSLA - Tesla</button>
            <button class="stock-btn" onclick="loadStockChart('AMZN')">AMZN - Amazon</button>
            <button class="stock-btn" onclick="loadStockChart('META')">META - Meta</button>
        </div>
    </div>

    <div class="demo-section">
        <div class="section-title">📊 多选图表控制</div>
        <div class="chart-controls">
            <div class="chart-title">📊 图表显示选项</div>
            <div class="chart-checkboxes">
                <label class="chart-checkbox">
                    <input type="checkbox" id="showPrice" checked onchange="updateCharts()">
                    <span class="checkbox-label">📈 价格走势</span>
                </label>
                <label class="chart-checkbox">
                    <input type="checkbox" id="showCandlestick" onchange="updateCharts()">
                    <span class="checkbox-label">🕯️ K线图</span>
                </label>
                <label class="chart-checkbox">
                    <input type="checkbox" id="showVolume" onchange="updateCharts()">
                    <span class="checkbox-label">📊 成交量</span>
                </label>
            </div>
            <div class="chart-actions">
                <button class="chart-btn" onclick="selectAll()">全选</button>
                <button class="chart-btn" onclick="clearAll()">清空</button>
                <button class="chart-btn" onclick="resetDefault()">默认</button>
                <button class="chart-btn" onclick="toggleLayout()" id="layoutBtn">网格布局</button>
            </div>
        </div>
        
        <div class="multi-chart-container">
            <canvas id="priceChart" class="chart-canvas" style="display: block;"></canvas>
            <canvas id="candlestickChart" class="chart-canvas" style="display: none;"></canvas>
            <canvas id="volumeChart" class="chart-canvas" style="display: none;"></canvas>
        </div>
    </div>

    <script>
        let currentCharts = {
            price: null,
            candlestick: null,
            volume: null
        };
        let currentStockData = null;
        let currentSymbol = 'AAPL';
        let isGridLayout = false;

        // 页面加载完成后自动加载AAPL数据
        window.addEventListener('load', function() {
            loadStockChart('AAPL');
        });

        // 加载股票图表
        async function loadStockChart(symbol) {
            currentSymbol = symbol;
            
            // 更新按钮状态
            document.querySelectorAll('.stock-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            try {
                const response = await fetch(`http://localhost:8080/api/stock/${symbol}`);
                const data = await response.json();
                
                if (response.ok) {
                    currentStockData = data.stock_data;
                    updateCharts();
                } else {
                    console.error('获取股票数据失败:', data.error);
                }
            } catch (error) {
                console.error('网络错误:', error);
            }
        }

        // 更新图表显示
        function updateCharts() {
            if (!currentStockData) return;

            const showPrice = document.getElementById('showPrice').checked;
            const showCandlestick = document.getElementById('showCandlestick').checked;
            const showVolume = document.getElementById('showVolume').checked;

            // 计算显示的图表数量
            const visibleCharts = [showPrice, showCandlestick, showVolume].filter(Boolean).length;

            // 显示/隐藏对应的canvas
            document.getElementById('priceChart').style.display = showPrice ? 'block' : 'none';
            document.getElementById('candlestickChart').style.display = showCandlestick ? 'block' : 'none';
            document.getElementById('volumeChart').style.display = showVolume ? 'block' : 'none';

            // 动态调整图表高度
            adjustChartHeights(visibleCharts);

            // 创建选中的图表
            if (showPrice) {
                createPriceChart();
            } else {
                destroyChart('price');
            }

            if (showCandlestick) {
                createCandlestickChart();
            } else {
                destroyChart('candlestick');
            }

            if (showVolume) {
                createVolumeChart();
            } else {
                destroyChart('volume');
            }
        }

        // 动态调整图表高度
        function adjustChartHeights(visibleCharts) {
            const charts = ['priceChart', 'candlestickChart', 'volumeChart'];

            charts.forEach(chartId => {
                const canvas = document.getElementById(chartId);
                canvas.classList.remove('single', 'dual', 'triple');

                if (visibleCharts === 1) {
                    canvas.classList.add('single');
                } else if (visibleCharts === 2) {
                    canvas.classList.add('dual');
                } else if (visibleCharts === 3) {
                    canvas.classList.add('triple');
                }
            });
        }

        // 切换布局模式
        function toggleLayout() {
            const container = document.querySelector('.multi-chart-container');
            const btn = document.getElementById('layoutBtn');

            isGridLayout = !isGridLayout;

            if (isGridLayout) {
                container.classList.add('grid-layout');
                btn.textContent = '垂直布局';

                const visibleCharts = [
                    document.getElementById('showPrice').checked,
                    document.getElementById('showCandlestick').checked,
                    document.getElementById('showVolume').checked
                ].filter(Boolean).length;

                if (visibleCharts === 1) {
                    const charts = ['priceChart', 'candlestickChart', 'volumeChart'];
                    charts.forEach(chartId => {
                        const canvas = document.getElementById(chartId);
                        if (canvas.style.display === 'block') {
                            canvas.classList.add('full-width');
                        }
                    });
                }
            } else {
                container.classList.remove('grid-layout');
                btn.textContent = '网格布局';

                const charts = ['priceChart', 'candlestickChart', 'volumeChart'];
                charts.forEach(chartId => {
                    document.getElementById(chartId).classList.remove('full-width');
                });
            }

            setTimeout(() => {
                updateCharts();
            }, 100);
        }

        // 全选
        function selectAll() {
            document.getElementById('showPrice').checked = true;
            document.getElementById('showCandlestick').checked = true;
            document.getElementById('showVolume').checked = true;
            updateCharts();
        }

        // 清空
        function clearAll() {
            document.getElementById('showPrice').checked = false;
            document.getElementById('showCandlestick').checked = false;
            document.getElementById('showVolume').checked = false;
            updateCharts();
        }

        // 重置为默认
        function resetDefault() {
            document.getElementById('showPrice').checked = true;
            document.getElementById('showCandlestick').checked = false;
            document.getElementById('showVolume').checked = false;
            updateCharts();
        }

        // 销毁指定图表
        function destroyChart(chartType) {
            if (currentCharts[chartType]) {
                currentCharts[chartType].destroy();
                currentCharts[chartType] = null;
            }
        }

        // 创建价格走势图
        function createPriceChart() {
            const chartCanvas = document.getElementById('priceChart');
            if (!chartCanvas || !currentStockData) return;
            
            try {
                const ctx = chartCanvas.getContext('2d');
                destroyChart('price');
                
                const historicalData = currentStockData.historical_data || [];
                if (historicalData.length === 0) return;
                
                const labels = historicalData.map(item => item.date);
                const prices = historicalData.map(item => item.close);
                
                currentCharts.price = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: `${currentStockData.symbol} 收盘价`,
                            data: prices,
                            borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                            backgroundColor: currentStockData.price_change >= 0 ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.1,
                            pointRadius: 3,
                            pointHoverRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `${currentStockData.name} (${currentStockData.symbol}) - 价格走势`,
                                font: { size: 14, weight: 'bold' }
                            }
                        },
                        scales: {
                            x: {
                                type: 'time',
                                time: { unit: 'day', displayFormats: { day: 'MM/dd' } }
                            },
                            y: {
                                title: { display: true, text: '价格 ($)' }
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('创建价格图表时出错:', error);
            }
        }

        // 创建K线图
        function createCandlestickChart() {
            const chartCanvas = document.getElementById('candlestickChart');
            if (!chartCanvas || !currentStockData) return;
            
            try {
                const ctx = chartCanvas.getContext('2d');
                destroyChart('candlestick');
                
                const historicalData = currentStockData.historical_data || [];
                if (historicalData.length === 0) return;
                
                const labels = historicalData.map(item => item.date);
                
                currentCharts.candlestick = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '开盘价',
                            data: historicalData.map(item => item.open),
                            backgroundColor: 'rgba(54, 162, 235, 0.3)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1,
                            hidden: true
                        }, {
                            label: '收盘价',
                            data: historicalData.map(item => item.close),
                            backgroundColor: historicalData.map(item => 
                                item.close >= item.open ? 'rgba(40, 167, 69, 0.7)' : 'rgba(220, 53, 69, 0.7)'
                            ),
                            borderColor: historicalData.map(item => 
                                item.close >= item.open ? '#28a745' : '#dc3545'
                            ),
                            borderWidth: 1
                        }, {
                            label: '最高价',
                            data: historicalData.map(item => item.high),
                            type: 'line',
                            borderColor: '#17a2b8',
                            borderWidth: 1,
                            pointRadius: 2,
                            fill: false
                        }, {
                            label: '最低价',
                            data: historicalData.map(item => item.low),
                            type: 'line',
                            borderColor: '#ffc107',
                            borderWidth: 1,
                            pointRadius: 2,
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `${currentStockData.name} (${currentStockData.symbol}) - K线图 (OHLC)`,
                                font: { size: 14, weight: 'bold' }
                            }
                        },
                        scales: {
                            x: {
                                type: 'time',
                                time: { unit: 'day', displayFormats: { day: 'MM/dd' } }
                            },
                            y: {
                                title: { display: true, text: '价格 ($)' }
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('创建K线图时出错:', error);
            }
        }

        // 创建成交量图
        function createVolumeChart() {
            const chartCanvas = document.getElementById('volumeChart');
            if (!chartCanvas || !currentStockData) return;
            
            try {
                const ctx = chartCanvas.getContext('2d');
                destroyChart('volume');
                
                const historicalData = currentStockData.historical_data || [];
                if (historicalData.length === 0) return;
                
                const labels = historicalData.map(item => item.date);
                const volumes = historicalData.map(item => item.volume);
                
                currentCharts.volume = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '成交量',
                            data: volumes,
                            backgroundColor: historicalData.map(item => 
                                item.close >= item.open ? 'rgba(40, 167, 69, 0.7)' : 'rgba(220, 53, 69, 0.7)'
                            ),
                            borderColor: historicalData.map(item => 
                                item.close >= item.open ? '#28a745' : '#dc3545'
                            ),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `${currentStockData.name} (${currentStockData.symbol}) - 成交量`,
                                font: { size: 14, weight: 'bold' }
                            }
                        },
                        scales: {
                            x: {
                                type: 'time',
                                time: { unit: 'day', displayFormats: { day: 'MM/dd' } }
                            },
                            y: {
                                title: { display: true, text: '成交量' },
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        if (value >= 1000000000) return (value/1000000000).toFixed(1) + 'B';
                                        if (value >= 1000000) return (value/1000000).toFixed(1) + 'M';
                                        if (value >= 1000) return (value/1000).toFixed(1) + 'K';
                                        return value.toString();
                                    }
                                }
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('创建成交量图时出错:', error);
            }
        }
    </script>
</body>
</html>
