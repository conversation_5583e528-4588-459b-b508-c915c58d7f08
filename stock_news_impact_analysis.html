<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股价新闻影响分析</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@3"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .analysis-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .chart-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        .stock-selector {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .stock-btn {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }
        .stock-btn:hover {
            background: #218838;
        }
        .stock-btn.active {
            background: #dc3545;
        }
        .chart-canvas {
            height: 600px;
            width: 100%;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .impact-analysis {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .impact-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }
        .impact-card.positive {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        }
        .impact-card.negative {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #fff8f8 0%, #f5e8e8 100%);
        }
        .impact-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .impact-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 10px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .impact-news {
            flex: 1;
            margin-right: 15px;
        }
        .impact-news-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
            font-size: 0.9em;
        }
        .impact-news-date {
            font-size: 0.8em;
            color: #666;
        }
        .impact-change {
            text-align: right;
            font-weight: bold;
            min-width: 80px;
        }
        .impact-change.positive {
            color: #28a745;
        }
        .impact-change.negative {
            color: #dc3545;
        }
        .correlation-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            color: #666;
        }
        .metric-description {
            font-size: 0.8em;
            color: #999;
            margin-top: 5px;
        }
        .legend-box {
            background: #e3f2fd;
            border: 1px solid #90caf9;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .legend-title {
            font-weight: bold;
            color: #1565c0;
            margin-bottom: 10px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        .legend-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
            color: white;
            font-weight: bold;
        }
        .legend-positive { background: #28a745; }
        .legend-negative { background: #dc3545; }
        .legend-neutral { background: #6c757d; }
        .highlight-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .highlight-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 股价新闻影响分析</h1>
        <p>分析新闻事件对股价变动的直接影响，揭示市场联动关系</p>
    </div>

    <div class="analysis-container">
        <div class="section-title">🎯 股票选择</div>
        <div class="chart-controls">
            <div class="stock-selector">
                <button class="stock-btn active" onclick="loadImpactAnalysis('AAPL')">AAPL - Apple</button>
                <button class="stock-btn" onclick="loadImpactAnalysis('MSFT')">MSFT - Microsoft</button>
                <button class="stock-btn" onclick="loadImpactAnalysis('GOOGL')">GOOGL - Google</button>
                <button class="stock-btn" onclick="loadImpactAnalysis('TSLA')">TSLA - Tesla</button>
                <button class="stock-btn" onclick="loadImpactAnalysis('AMZN')">AMZN - Amazon</button>
                <button class="stock-btn" onclick="loadImpactAnalysis('META')">META - Meta</button>
            </div>
        </div>
    </div>

    <div class="analysis-container">
        <div class="section-title">📈 股价走势与新闻影响</div>
        <div class="highlight-box">
            <div class="highlight-title">💡 分析重点</div>
            <div>
                本分析重点展示新闻事件对股价的<strong>直接影响</strong>和<strong>联动关系</strong>：<br>
                • 📈 <strong>积极新闻</strong>：观察股价上涨反应<br>
                • 📉 <strong>消极新闻</strong>：观察股价下跌反应<br>
                • 🔄 <strong>影响强度</strong>：分析新闻对股价变动的影响程度
            </div>
        </div>
        <div class="legend-box">
            <div class="legend-title">📊 影响标识说明</div>
            <div class="legend-item">
                <div class="legend-icon legend-positive">↗</div>
                <span>积极新闻影响 - 推动股价上涨</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon legend-negative">↘</div>
                <span>消极新闻影响 - 导致股价下跌</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon legend-neutral">→</div>
                <span>中性新闻影响 - 股价波动较小</span>
            </div>
        </div>
        <canvas id="impactChart" class="chart-canvas"></canvas>
        
        <div class="correlation-metrics" id="correlationMetrics">
            <!-- 关联度指标将在这里显示 -->
        </div>
    </div>

    <div class="analysis-container">
        <div class="section-title">🎯 新闻影响效果分析</div>
        <div class="impact-analysis" id="impactAnalysis">
            <!-- 影响分析将在这里显示 -->
        </div>
    </div>

    <script>
        let currentChart = null;
        let currentStockData = null;
        let currentNewsData = null;
        let currentSymbol = 'AAPL';

        // 页面加载完成后自动加载AAPL数据
        window.addEventListener('load', function() {
            loadImpactAnalysis('AAPL');
        });

        // 加载影响分析数据
        async function loadImpactAnalysis(symbol) {
            currentSymbol = symbol;
            
            // 更新按钮状态
            document.querySelectorAll('.stock-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            try {
                const response = await fetch(`http://localhost:8080/api/stock/${symbol}`);
                const data = await response.json();
                
                if (response.ok) {
                    currentStockData = data.stock_data;
                    currentNewsData = data.news || [];
                    
                    createImpactChart();
                    createImpactAnalysis();
                    updateCorrelationMetrics();
                } else {
                    console.error('获取股票数据失败:', data.error);
                }
            } catch (error) {
                console.error('网络错误:', error);
            }
        }

        // 创建影响分析图表
        function createImpactChart() {
            if (!currentStockData) return;

            const canvas = document.getElementById('impactChart');
            const ctx = canvas.getContext('2d');

            if (currentChart) {
                currentChart.destroy();
            }

            const historicalData = currentStockData.historical_data || [];
            const labels = historicalData.map(item => item.date);
            const prices = historicalData.map(item => item.close);

            // 计算每日涨跌幅
            const dailyChanges = [];
            for (let i = 1; i < historicalData.length; i++) {
                const change = ((historicalData[i].close - historicalData[i-1].close) / historicalData[i-1].close) * 100;
                dailyChanges.push(change);
            }

            // 生成新闻影响标注
            const annotations = [];
            const newsImpacts = [];
            
            if (currentNewsData.length > 0) {
                currentNewsData.forEach((news, index) => {
                    const newsDate = new Date(news.published_time).toISOString().split('T')[0];
                    const dataPointIndex = historicalData.findIndex(item => item.date === newsDate);
                    const dataPoint = historicalData[dataPointIndex];
                    
                    if (dataPoint && dataPointIndex > 0) {
                        // 计算新闻发布后的股价变化
                        const prevPrice = historicalData[dataPointIndex - 1].close;
                        const currentPrice = dataPoint.close;
                        const priceChange = ((currentPrice - prevPrice) / prevPrice) * 100;
                        
                        // 计算后续几天的累计影响
                        let cumulativeChange = 0;
                        const impactDays = Math.min(3, historicalData.length - dataPointIndex);
                        for (let i = 0; i < impactDays; i++) {
                            if (dataPointIndex + i + 1 < historicalData.length) {
                                const dayChange = ((historicalData[dataPointIndex + i + 1].close - historicalData[dataPointIndex + i].close) / historicalData[dataPointIndex + i].close) * 100;
                                cumulativeChange += dayChange;
                            }
                        }

                        let color = '#6c757d';
                        let impactIcon = '→';
                        let impactStrength = 'low';
                        
                        if (news.sentiment === 'positive') {
                            color = '#28a745';
                            impactIcon = '↗';
                            impactStrength = cumulativeChange > 2 ? 'high' : cumulativeChange > 0.5 ? 'medium' : 'low';
                        } else if (news.sentiment === 'negative') {
                            color = '#dc3545';
                            impactIcon = '↘';
                            impactStrength = cumulativeChange < -2 ? 'high' : cumulativeChange < -0.5 ? 'medium' : 'low';
                        }
                        
                        // 影响强度决定标注大小
                        const impactSize = impactStrength === 'high' ? 12 : impactStrength === 'medium' ? 10 : 8;
                        const lineWidth = impactStrength === 'high' ? 4 : impactStrength === 'medium' ? 3 : 2;
                        
                        // 垂直影响线
                        annotations.push({
                            type: 'line',
                            mode: 'vertical',
                            scaleID: 'x',
                            value: newsDate,
                            borderColor: color,
                            borderWidth: lineWidth,
                            borderDash: impactStrength === 'high' ? [10, 5] : [6, 3],
                            label: {
                                enabled: true,
                                content: `${impactIcon} ${cumulativeChange.toFixed(1)}%`,
                                position: 'top',
                                backgroundColor: color,
                                color: 'white',
                                font: { size: 11, weight: 'bold' },
                                padding: 6,
                                cornerRadius: 4
                            }
                        });
                        
                        // 影响点标注
                        annotations.push({
                            type: 'point',
                            scaleID: 'x',
                            value: newsDate,
                            yValue: dataPoint.close,
                            backgroundColor: color,
                            borderColor: 'white',
                            borderWidth: 3,
                            radius: impactSize
                        });

                        // 记录影响数据
                        newsImpacts.push({
                            news: news,
                            date: newsDate,
                            priceChange: priceChange,
                            cumulativeChange: cumulativeChange,
                            impactStrength: impactStrength,
                            color: color
                        });
                    }
                });
            }

            // 存储影响数据供其他函数使用
            window.currentNewsImpacts = newsImpacts;

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `${currentStockData.symbol} 收盘价`,
                        data: prices,
                        borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: 'rgba(0, 123, 255, 0.05)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.1,
                        pointRadius: 3,
                        pointHoverRadius: 8,
                        pointBackgroundColor: prices.map((price, index) => {
                            if (index === 0) return '#007bff';
                            const change = ((price - prices[index-1]) / prices[index-1]) * 100;
                            return change > 0 ? '#28a745' : change < 0 ? '#dc3545' : '#6c757d';
                        }),
                        pointBorderColor: 'white',
                        pointBorderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentStockData.name} (${currentStockData.symbol}) - 新闻事件影响分析`,
                            font: { size: 18, weight: 'bold' }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        annotation: {
                            annotations: annotations
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day',
                                displayFormats: {
                                    day: 'MM/dd'
                                }
                            },
                            title: {
                                display: true,
                                text: '日期',
                                font: { size: 14, weight: 'bold' }
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '股价 ($)',
                                font: { size: 14, weight: 'bold' }
                            },
                            beginAtZero: false
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // 创建影响分析
        function createImpactAnalysis() {
            const analysisContainer = document.getElementById('impactAnalysis');
            
            if (!window.currentNewsImpacts || window.currentNewsImpacts.length === 0) {
                analysisContainer.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; color: #666; padding: 40px;">
                        暂无新闻影响数据
                    </div>
                `;
                return;
            }

            const positiveImpacts = window.currentNewsImpacts.filter(impact => 
                impact.news.sentiment === 'positive' && impact.cumulativeChange > 0
            );
            
            const negativeImpacts = window.currentNewsImpacts.filter(impact => 
                impact.news.sentiment === 'negative' && impact.cumulativeChange < 0
            );

            let analysisHTML = '';

            // 积极影响分析
            if (positiveImpacts.length > 0) {
                analysisHTML += `
                    <div class="impact-card positive">
                        <div class="impact-title">📈 积极新闻推动效果</div>
                `;
                
                positiveImpacts.sort((a, b) => b.cumulativeChange - a.cumulativeChange).forEach(impact => {
                    analysisHTML += `
                        <div class="impact-item">
                            <div class="impact-news">
                                <div class="impact-news-title">${impact.news.title}</div>
                                <div class="impact-news-date">${new Date(impact.news.published_time).toLocaleDateString('zh-CN')}</div>
                            </div>
                            <div class="impact-change positive">
                                +${impact.cumulativeChange.toFixed(2)}%
                                <div style="font-size: 0.8em;">3日累计</div>
                            </div>
                        </div>
                    `;
                });
                
                analysisHTML += `</div>`;
            }

            // 消极影响分析
            if (negativeImpacts.length > 0) {
                analysisHTML += `
                    <div class="impact-card negative">
                        <div class="impact-title">📉 消极新闻影响效果</div>
                `;
                
                negativeImpacts.sort((a, b) => a.cumulativeChange - b.cumulativeChange).forEach(impact => {
                    analysisHTML += `
                        <div class="impact-item">
                            <div class="impact-news">
                                <div class="impact-news-title">${impact.news.title}</div>
                                <div class="impact-news-date">${new Date(impact.news.published_time).toLocaleDateString('zh-CN')}</div>
                            </div>
                            <div class="impact-change negative">
                                ${impact.cumulativeChange.toFixed(2)}%
                                <div style="font-size: 0.8em;">3日累计</div>
                            </div>
                        </div>
                    `;
                });
                
                analysisHTML += `</div>`;
            }

            if (analysisHTML === '') {
                analysisHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; color: #666; padding: 40px;">
                        <div style="font-size: 1.2em; margin-bottom: 10px;">📊 影响分析</div>
                        <div>当前新闻事件与股价变动的关联性较弱，未发现明显的影响模式</div>
                    </div>
                `;
            }

            analysisContainer.innerHTML = analysisHTML;
        }

        // 更新关联度指标
        function updateCorrelationMetrics() {
            const metricsContainer = document.getElementById('correlationMetrics');
            
            if (!window.currentNewsImpacts || window.currentNewsImpacts.length === 0) {
                metricsContainer.innerHTML = '';
                return;
            }

            const impacts = window.currentNewsImpacts;
            const totalNews = impacts.length;
            const positiveNews = impacts.filter(i => i.news.sentiment === 'positive').length;
            const negativeNews = impacts.filter(i => i.news.sentiment === 'negative').length;
            
            // 计算平均影响
            const avgPositiveImpact = impacts
                .filter(i => i.news.sentiment === 'positive')
                .reduce((sum, i) => sum + i.cumulativeChange, 0) / (positiveNews || 1);
                
            const avgNegativeImpact = impacts
                .filter(i => i.news.sentiment === 'negative')
                .reduce((sum, i) => sum + i.cumulativeChange, 0) / (negativeNews || 1);

            // 计算影响强度分布
            const highImpacts = impacts.filter(i => i.impactStrength === 'high').length;
            const mediumImpacts = impacts.filter(i => i.impactStrength === 'medium').length;
            
            // 计算关联度得分
            const correlationScore = Math.min(100, Math.abs(avgPositiveImpact) + Math.abs(avgNegativeImpact));

            metricsContainer.innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${totalNews}</div>
                    <div class="metric-label">新闻事件总数</div>
                    <div class="metric-description">分析期间的新闻数量</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: ${avgPositiveImpact > 0 ? '#28a745' : '#dc3545'};">
                        ${avgPositiveImpact > 0 ? '+' : ''}${avgPositiveImpact.toFixed(1)}%
                    </div>
                    <div class="metric-label">积极新闻平均影响</div>
                    <div class="metric-description">3日累计股价变动</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: ${avgNegativeImpact < 0 ? '#dc3545' : '#28a745'};">
                        ${avgNegativeImpact.toFixed(1)}%
                    </div>
                    <div class="metric-label">消极新闻平均影响</div>
                    <div class="metric-description">3日累计股价变动</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${highImpacts + mediumImpacts}</div>
                    <div class="metric-label">显著影响事件</div>
                    <div class="metric-description">高/中等影响强度事件</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: ${correlationScore > 50 ? '#28a745' : correlationScore > 25 ? '#ffc107' : '#dc3545'};">
                        ${correlationScore.toFixed(0)}
                    </div>
                    <div class="metric-label">影响关联度</div>
                    <div class="metric-description">新闻与股价联动强度</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${((highImpacts + mediumImpacts) / totalNews * 100).toFixed(0)}%</div>
                    <div class="metric-label">有效影响率</div>
                    <div class="metric-description">产生显著影响的新闻比例</div>
                </div>
            `;
        }
    </script>
</body>
</html>
