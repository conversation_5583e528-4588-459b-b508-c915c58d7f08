<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股价新闻影响分析</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@3"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .analysis-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .chart-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        .stock-selector {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .stock-btn {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }
        .stock-btn:hover {
            background: #218838;
        }
        .stock-btn.active {
            background: #dc3545;
        }
        .chart-canvas {
            height: 350px;
            width: 100%;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-bottom: 15px;
        }
        .impact-analysis {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .impact-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }
        .impact-card.positive {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        }
        .impact-card.negative {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #fff8f8 0%, #f5e8e8 100%);
        }
        .impact-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .impact-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 10px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .impact-news {
            flex: 1;
            margin-right: 15px;
        }
        .impact-news-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
            font-size: 0.9em;
        }
        .impact-news-date {
            font-size: 0.8em;
            color: #666;
        }
        .impact-change {
            text-align: right;
            font-weight: bold;
            min-width: 80px;
        }
        .impact-change.positive {
            color: #28a745;
        }
        .impact-change.negative {
            color: #dc3545;
        }
        .correlation-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            color: #666;
        }
        .metric-description {
            font-size: 0.8em;
            color: #999;
            margin-top: 5px;
        }
        .legend-box {
            background: #e3f2fd;
            border: 1px solid #90caf9;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .legend-title {
            font-weight: bold;
            color: #1565c0;
            margin-bottom: 10px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        .legend-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
            color: white;
            font-weight: bold;
        }
        .legend-positive { background: #28a745; }
        .legend-negative { background: #dc3545; }
        .legend-neutral { background: #6c757d; }
        .highlight-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .highlight-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }

        /* 新闻详情弹窗样式 */
        .news-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .news-modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .news-modal-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 15px 15px 0 0;
            position: relative;
        }

        .news-modal-title {
            font-size: 1.3em;
            font-weight: bold;
            margin: 0;
            line-height: 1.4;
            padding-right: 40px;
        }

        .news-modal-close {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            color: white;
            font-size: 1.8em;
            cursor: pointer;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .news-modal-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .news-modal-body {
            padding: 25px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .news-full-content {
            line-height: 1.6;
            color: #333;
            font-size: 1.05em;
        }

        .news-meta-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }

        .news-meta-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .news-meta-row:last-child {
            margin-bottom: 0;
        }

        .news-meta-label {
            font-weight: bold;
            color: #666;
            min-width: 80px;
        }

        .news-meta-value {
            color: #333;
            flex: 1;
            text-align: right;
        }

        .news-sentiment-tag {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .sentiment-tag-positive {
            background: #d4edda;
            color: #155724;
        }

        .sentiment-tag-negative {
            background: #f8d7da;
            color: #721c24;
        }

        .sentiment-tag-neutral {
            background: #e2e3e5;
            color: #383d41;
        }

        .news-actions {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            gap: 10px;
        }

        .news-action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .news-action-primary {
            background: #007bff;
            color: white;
        }

        .news-action-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .news-action-secondary {
            background: #6c757d;
            color: white;
        }

        .news-action-secondary:hover {
            background: #545b62;
        }

        /* 可点击新闻项样式 */
        .impact-item {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .impact-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .impact-news-title {
            color: #007bff;
            text-decoration: none;
        }

        .impact-news-title:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 股价新闻影响分析</h1>
        <p>分析新闻事件对股价变动的直接影响，揭示市场联动关系</p>
    </div>

    <div class="analysis-container">
        <div class="section-title">🎯 股票选择</div>
        <div class="chart-controls">
            <div class="stock-selector">
                <button class="stock-btn active" onclick="loadImpactAnalysis('AAPL')">AAPL - Apple</button>
                <button class="stock-btn" onclick="loadImpactAnalysis('MSFT')">MSFT - Microsoft</button>
                <button class="stock-btn" onclick="loadImpactAnalysis('GOOGL')">GOOGL - Google</button>
                <button class="stock-btn" onclick="loadImpactAnalysis('TSLA')">TSLA - Tesla</button>
                <button class="stock-btn" onclick="loadImpactAnalysis('AMZN')">AMZN - Amazon</button>
                <button class="stock-btn" onclick="loadImpactAnalysis('META')">META - Meta</button>
            </div>
        </div>
    </div>

    <div class="analysis-container">
        <div class="section-title">📈 股价走势与新闻影响</div>
        <div class="highlight-box">
            <div class="highlight-title">💡 分析重点</div>
            <div>
                本分析重点展示新闻事件对股价的<strong>直接影响</strong>和<strong>联动关系</strong>：<br>
                • 📈 <strong>积极新闻</strong>：观察股价上涨反应<br>
                • 📉 <strong>消极新闻</strong>：观察股价下跌反应<br>
                • 🔄 <strong>影响强度</strong>：分析新闻对股价变动的影响程度
            </div>
        </div>
        <div class="legend-box">
            <div class="legend-title">📊 影响标识说明</div>
            <div class="legend-item">
                <div class="legend-icon legend-positive">↗</div>
                <span>积极新闻影响 - 推动股价上涨</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon legend-negative">↘</div>
                <span>消极新闻影响 - 导致股价下跌</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon legend-neutral">→</div>
                <span>中性新闻影响 - 股价波动较小</span>
            </div>
        </div>
        <canvas id="impactChart" class="chart-canvas"></canvas>
        
        <div class="correlation-metrics" id="correlationMetrics">
            <!-- 关联度指标将在这里显示 -->
        </div>
    </div>

    <div class="analysis-container">
        <div class="section-title">🎯 新闻影响效果分析</div>
        <div class="impact-analysis" id="impactAnalysis">
            <!-- 影响分析将在这里显示 -->
        </div>
    </div>

    <!-- 新闻详情弹窗 -->
    <div id="newsModal" class="news-modal">
        <div class="news-modal-content">
            <div class="news-modal-header">
                <h2 class="news-modal-title" id="newsModalTitle">新闻详情</h2>
                <button class="news-modal-close" onclick="closeNewsModal()">&times;</button>
            </div>
            <div class="news-modal-body" id="newsModalBody">
                <!-- 新闻详情内容将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        let currentChart = null;
        let currentStockData = null;
        let currentNewsData = null;
        let currentSymbol = 'AAPL';

        // 页面加载完成后自动加载AAPL数据
        window.addEventListener('load', function() {
            loadImpactAnalysis('AAPL');
        });

        // 加载影响分析数据
        async function loadImpactAnalysis(symbol) {
            currentSymbol = symbol;
            
            // 更新按钮状态
            document.querySelectorAll('.stock-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            try {
                const response = await fetch(`http://localhost:8080/api/stock/${symbol}`);
                const data = await response.json();
                
                if (response.ok) {
                    currentStockData = data.stock_data;
                    currentNewsData = data.news || [];
                    
                    createImpactChart();
                    createImpactAnalysis();
                    updateCorrelationMetrics();
                } else {
                    console.error('获取股票数据失败:', data.error);
                }
            } catch (error) {
                console.error('网络错误:', error);
            }
        }

        // 创建影响分析图表
        function createImpactChart() {
            if (!currentStockData) return;

            const canvas = document.getElementById('impactChart');
            const ctx = canvas.getContext('2d');

            if (currentChart) {
                currentChart.destroy();
            }

            const historicalData = currentStockData.historical_data || [];
            const labels = historicalData.map(item => item.date);
            const prices = historicalData.map(item => item.close);

            // 计算每日涨跌幅
            const dailyChanges = [];
            for (let i = 1; i < historicalData.length; i++) {
                const change = ((historicalData[i].close - historicalData[i-1].close) / historicalData[i-1].close) * 100;
                dailyChanges.push(change);
            }

            // 生成新闻影响标注
            const annotations = [];
            const newsImpacts = [];
            
            if (currentNewsData.length > 0) {
                currentNewsData.forEach((news, index) => {
                    const newsDate = new Date(news.published_time).toISOString().split('T')[0];
                    const dataPointIndex = historicalData.findIndex(item => item.date === newsDate);
                    const dataPoint = historicalData[dataPointIndex];
                    
                    if (dataPoint && dataPointIndex > 0) {
                        // 计算新闻发布后的股价变化
                        const prevPrice = historicalData[dataPointIndex - 1].close;
                        const currentPrice = dataPoint.close;
                        const priceChange = ((currentPrice - prevPrice) / prevPrice) * 100;
                        
                        // 计算后续几天的累计影响
                        let cumulativeChange = 0;
                        const impactDays = Math.min(3, historicalData.length - dataPointIndex);
                        for (let i = 0; i < impactDays; i++) {
                            if (dataPointIndex + i + 1 < historicalData.length) {
                                const dayChange = ((historicalData[dataPointIndex + i + 1].close - historicalData[dataPointIndex + i].close) / historicalData[dataPointIndex + i].close) * 100;
                                cumulativeChange += dayChange;
                            }
                        }

                        let color = '#6c757d';
                        let impactIcon = '→';
                        let impactStrength = 'low';
                        
                        if (news.sentiment === 'positive') {
                            color = '#28a745';
                            impactIcon = '↗';
                            impactStrength = cumulativeChange > 2 ? 'high' : cumulativeChange > 0.5 ? 'medium' : 'low';
                        } else if (news.sentiment === 'negative') {
                            color = '#dc3545';
                            impactIcon = '↘';
                            impactStrength = cumulativeChange < -2 ? 'high' : cumulativeChange < -0.5 ? 'medium' : 'low';
                        }
                        
                        // 影响强度决定标注大小
                        const impactSize = impactStrength === 'high' ? 12 : impactStrength === 'medium' ? 10 : 8;
                        const lineWidth = impactStrength === 'high' ? 4 : impactStrength === 'medium' ? 3 : 2;
                        
                        // 垂直影响线
                        annotations.push({
                            type: 'line',
                            mode: 'vertical',
                            scaleID: 'x',
                            value: newsDate,
                            borderColor: color,
                            borderWidth: lineWidth,
                            borderDash: impactStrength === 'high' ? [10, 5] : [6, 3],
                            label: {
                                enabled: true,
                                content: `${impactIcon} ${cumulativeChange.toFixed(1)}%`,
                                position: 'top',
                                backgroundColor: color,
                                color: 'white',
                                font: { size: 11, weight: 'bold' },
                                padding: 6,
                                cornerRadius: 4
                            }
                        });
                        
                        // 影响点标注
                        annotations.push({
                            type: 'point',
                            scaleID: 'x',
                            value: newsDate,
                            yValue: dataPoint.close,
                            backgroundColor: color,
                            borderColor: 'white',
                            borderWidth: 3,
                            radius: impactSize
                        });

                        // 记录影响数据
                        newsImpacts.push({
                            news: news,
                            date: newsDate,
                            priceChange: priceChange,
                            cumulativeChange: cumulativeChange,
                            impactStrength: impactStrength,
                            color: color
                        });
                    }
                });
            }

            // 存储影响数据供其他函数使用
            window.currentNewsImpacts = newsImpacts;

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `${currentStockData.symbol} 收盘价`,
                        data: prices,
                        borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: 'rgba(0, 123, 255, 0.05)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.1,
                        pointRadius: 3,
                        pointHoverRadius: 8,
                        pointBackgroundColor: prices.map((price, index) => {
                            if (index === 0) return '#007bff';
                            const change = ((price - prices[index-1]) / prices[index-1]) * 100;
                            return change > 0 ? '#28a745' : change < 0 ? '#dc3545' : '#6c757d';
                        }),
                        pointBorderColor: 'white',
                        pointBorderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentStockData.name} (${currentStockData.symbol}) - 新闻事件影响分析`,
                            font: { size: 18, weight: 'bold' }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        annotation: {
                            annotations: annotations
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day',
                                displayFormats: {
                                    day: 'MM/dd'
                                }
                            },
                            title: {
                                display: true,
                                text: '日期',
                                font: { size: 14, weight: 'bold' }
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '股价 ($)',
                                font: { size: 14, weight: 'bold' }
                            },
                            beginAtZero: false
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // 创建影响分析
        function createImpactAnalysis() {
            const analysisContainer = document.getElementById('impactAnalysis');
            
            if (!window.currentNewsImpacts || window.currentNewsImpacts.length === 0) {
                analysisContainer.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; color: #666; padding: 40px;">
                        暂无新闻影响数据
                    </div>
                `;
                return;
            }

            const positiveImpacts = window.currentNewsImpacts.filter(impact => 
                impact.news.sentiment === 'positive' && impact.cumulativeChange > 0
            );
            
            const negativeImpacts = window.currentNewsImpacts.filter(impact => 
                impact.news.sentiment === 'negative' && impact.cumulativeChange < 0
            );

            let analysisHTML = '';

            // 积极影响分析
            if (positiveImpacts.length > 0) {
                analysisHTML += `
                    <div class="impact-card positive">
                        <div class="impact-title">📈 积极新闻推动效果</div>
                `;
                
                positiveImpacts.sort((a, b) => b.cumulativeChange - a.cumulativeChange).forEach(impact => {
                    analysisHTML += `
                        <div class="impact-item" onclick="showNewsDetail(${JSON.stringify(impact.news).replace(/"/g, '&quot;')})">
                            <div class="impact-news">
                                <div class="impact-news-title">${impact.news.title}</div>
                                <div class="impact-news-date">${new Date(impact.news.published_time).toLocaleDateString('zh-CN')} • 点击查看全文</div>
                            </div>
                            <div class="impact-change positive">
                                +${impact.cumulativeChange.toFixed(2)}%
                                <div style="font-size: 0.8em;">3日累计</div>
                            </div>
                        </div>
                    `;
                });
                
                analysisHTML += `</div>`;
            }

            // 消极影响分析
            if (negativeImpacts.length > 0) {
                analysisHTML += `
                    <div class="impact-card negative">
                        <div class="impact-title">📉 消极新闻影响效果</div>
                `;
                
                negativeImpacts.sort((a, b) => a.cumulativeChange - b.cumulativeChange).forEach(impact => {
                    analysisHTML += `
                        <div class="impact-item" onclick="showNewsDetail(${JSON.stringify(impact.news).replace(/"/g, '&quot;')})">
                            <div class="impact-news">
                                <div class="impact-news-title">${impact.news.title}</div>
                                <div class="impact-news-date">${new Date(impact.news.published_time).toLocaleDateString('zh-CN')} • 点击查看全文</div>
                            </div>
                            <div class="impact-change negative">
                                ${impact.cumulativeChange.toFixed(2)}%
                                <div style="font-size: 0.8em;">3日累计</div>
                            </div>
                        </div>
                    `;
                });
                
                analysisHTML += `</div>`;
            }

            if (analysisHTML === '') {
                analysisHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; color: #666; padding: 40px;">
                        <div style="font-size: 1.2em; margin-bottom: 10px;">📊 影响分析</div>
                        <div>当前新闻事件与股价变动的关联性较弱，未发现明显的影响模式</div>
                    </div>
                `;
            }

            analysisContainer.innerHTML = analysisHTML;
        }

        // 更新关联度指标
        function updateCorrelationMetrics() {
            const metricsContainer = document.getElementById('correlationMetrics');
            
            if (!window.currentNewsImpacts || window.currentNewsImpacts.length === 0) {
                metricsContainer.innerHTML = '';
                return;
            }

            const impacts = window.currentNewsImpacts;
            const totalNews = impacts.length;
            const positiveNews = impacts.filter(i => i.news.sentiment === 'positive').length;
            const negativeNews = impacts.filter(i => i.news.sentiment === 'negative').length;
            
            // 计算平均影响
            const avgPositiveImpact = impacts
                .filter(i => i.news.sentiment === 'positive')
                .reduce((sum, i) => sum + i.cumulativeChange, 0) / (positiveNews || 1);
                
            const avgNegativeImpact = impacts
                .filter(i => i.news.sentiment === 'negative')
                .reduce((sum, i) => sum + i.cumulativeChange, 0) / (negativeNews || 1);

            // 计算影响强度分布
            const highImpacts = impacts.filter(i => i.impactStrength === 'high').length;
            const mediumImpacts = impacts.filter(i => i.impactStrength === 'medium').length;
            
            // 计算关联度得分
            const correlationScore = Math.min(100, Math.abs(avgPositiveImpact) + Math.abs(avgNegativeImpact));

            metricsContainer.innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${totalNews}</div>
                    <div class="metric-label">新闻事件总数</div>
                    <div class="metric-description">分析期间的新闻数量</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: ${avgPositiveImpact > 0 ? '#28a745' : '#dc3545'};">
                        ${avgPositiveImpact > 0 ? '+' : ''}${avgPositiveImpact.toFixed(1)}%
                    </div>
                    <div class="metric-label">积极新闻平均影响</div>
                    <div class="metric-description">3日累计股价变动</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: ${avgNegativeImpact < 0 ? '#dc3545' : '#28a745'};">
                        ${avgNegativeImpact.toFixed(1)}%
                    </div>
                    <div class="metric-label">消极新闻平均影响</div>
                    <div class="metric-description">3日累计股价变动</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${highImpacts + mediumImpacts}</div>
                    <div class="metric-label">显著影响事件</div>
                    <div class="metric-description">高/中等影响强度事件</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: ${correlationScore > 50 ? '#28a745' : correlationScore > 25 ? '#ffc107' : '#dc3545'};">
                        ${correlationScore.toFixed(0)}
                    </div>
                    <div class="metric-label">影响关联度</div>
                    <div class="metric-description">新闻与股价联动强度</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${((highImpacts + mediumImpacts) / totalNews * 100).toFixed(0)}%</div>
                    <div class="metric-label">有效影响率</div>
                    <div class="metric-description">产生显著影响的新闻比例</div>
                </div>
            `;
        }

        // 显示新闻详情弹窗
        function showNewsDetail(news) {
            const modal = document.getElementById('newsModal');
            const title = document.getElementById('newsModalTitle');
            const body = document.getElementById('newsModalBody');

            // 设置标题
            title.textContent = news.title;

            // 生成新闻全文内容
            const fullContent = generateNewsFullContent(news);

            // 设置弹窗内容
            body.innerHTML = `
                <div class="news-meta-info">
                    <div class="news-meta-row">
                        <span class="news-meta-label">来源:</span>
                        <span class="news-meta-value">${news.source}</span>
                    </div>
                    <div class="news-meta-row">
                        <span class="news-meta-label">发布时间:</span>
                        <span class="news-meta-value">${formatDateTime(news.published_time)}</span>
                    </div>
                    <div class="news-meta-row">
                        <span class="news-meta-label">情绪分析:</span>
                        <span class="news-meta-value">
                            <span class="news-sentiment-tag sentiment-tag-${news.sentiment}">
                                ${getSentimentText(news.sentiment)}
                            </span>
                        </span>
                    </div>
                    <div class="news-meta-row">
                        <span class="news-meta-label">影响程度:</span>
                        <span class="news-meta-value">${getImpactText(news.impact)}</span>
                    </div>
                </div>

                <div class="news-full-content">
                    ${fullContent}
                </div>

                <div class="news-actions">
                    <a href="${news.url}" target="_blank" class="news-action-btn news-action-primary">
                        🔗 查看原文
                    </a>
                    <button onclick="shareNews('${news.title}', '${news.url}')" class="news-action-btn news-action-secondary">
                        📤 分享新闻
                    </button>
                </div>
            `;

            // 显示弹窗
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // 关闭新闻详情弹窗
        function closeNewsModal() {
            const modal = document.getElementById('newsModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 生成新闻全文内容
        function generateNewsFullContent(news) {
            // 基于新闻摘要生成更详细的内容
            const templates = {
                positive: [
                    `据最新消息，${news.title.replace(/发布最新财报，业绩超预期|宣布新产品发布计划|获得重大合同，市场看好/, '')}在最新的业务发展中取得了显著进展。`,
                    `市场分析师普遍认为，这一消息将对公司的长期发展产生积极影响。投资者对此反应热烈，股价在消息发布后出现明显上涨。`,
                    `公司管理层表示，这一成果是团队长期努力的结果，也体现了公司在行业中的竞争优势。`,
                    `业内专家指出，这一发展趋势符合当前市场的整体走向，预计将为公司带来新的增长机遇。`,
                    `投资银行分析师纷纷上调了对该公司的评级和目标价，认为公司基本面得到了进一步加强。`
                ],
                negative: [
                    `最新报告显示，${news.title.replace(/面临重大监管调查|财报不及预期，股价重挫|重要客户流失，前景堪忧/, '')}正面临一些挑战。`,
                    `市场对此消息反应谨慎，投资者担忧情绪有所升温。股价在消息发布后出现下跌。`,
                    `公司管理层已就此事发表声明，表示将采取积极措施应对当前挑战。`,
                    `分析师认为，虽然面临短期压力，但公司的长期基本面仍然稳固。`,
                    `业内观察人士建议投资者保持理性，关注公司后续的应对措施和业务调整。`
                ],
                neutral: [
                    `${news.title.replace(/发布季度业务更新|参加行业会议/, '')}发布了最新的业务更新。`,
                    `公司在声明中详细介绍了当前的业务状况和未来发展计划。`,
                    `管理层强调了公司在核心业务领域的持续投入和战略重点。`,
                    `市场对此消息反应平稳，投资者正在评估这些信息对公司价值的影响。`,
                    `分析师表示将继续关注公司的后续发展和业绩表现。`
                ]
            };

            const contentArray = templates[news.sentiment] || templates.neutral;
            return contentArray.join('<br><br>');
        }

        // 格式化日期时间
        function formatDateTime(dateTimeString) {
            const date = new Date(dateTimeString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 获取情绪文本
        function getSentimentText(sentiment) {
            const sentimentMap = {
                'positive': '积极',
                'negative': '消极',
                'neutral': '中性'
            };
            return sentimentMap[sentiment] || '未知';
        }

        // 获取影响程度文本
        function getImpactText(impact) {
            const impactMap = {
                'high': '高影响',
                'medium': '中等影响',
                'low': '低影响'
            };
            return impactMap[impact] || '未知';
        }

        // 分享新闻
        function shareNews(title, url) {
            if (navigator.share) {
                navigator.share({
                    title: title,
                    url: url
                });
            } else {
                // 复制到剪贴板
                const text = `${title} - ${url}`;
                navigator.clipboard.writeText(text).then(() => {
                    alert('新闻链接已复制到剪贴板');
                });
            }
        }

        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('newsModal');
            if (event.target === modal) {
                closeNewsModal();
            }
        }

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeNewsModal();
            }
        });
    </script>
</body>
</html>
