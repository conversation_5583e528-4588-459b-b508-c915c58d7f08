<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实景点图片测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .image-item {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        .image-container {
            width: 100%;
            height: 200px;
            position: relative;
            background: #f0f0f0;
        }
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .image-info {
            padding: 15px;
        }
        .image-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .image-status {
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
            margin-top: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .loading { background: #d1ecf1; color: #0c5460; }
        .placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3em;
        }
    </style>
</head>
<body>
    <h1>🖼️ 真实景点图片测试</h1>
    
    <div class="test-section">
        <h2>当前Pixabay图片测试</h2>
        <div class="image-grid" id="pixabayGrid"></div>
    </div>

    <div class="test-section">
        <h2>备用Unsplash图片测试</h2>
        <div class="image-grid" id="unsplashGrid"></div>
    </div>

    <div class="test-section">
        <h2>Wikipedia图片测试</h2>
        <div class="image-grid" id="wikipediaGrid"></div>
    </div>

    <script>
        // 当前使用的Pixabay图片
        const pixabayImages = [
            {name: "承德避暑山庄", url: "https://cdn.pixabay.com/photo/2019/09/25/14/56/chengde-mountain-resort-4502715_960_720.jpg"},
            {name: "天津海河", url: "https://cdn.pixabay.com/photo/2018/08/14/13/23/tianjin-3605833_960_720.jpg"},
            {name: "杭州西湖", url: "https://cdn.pixabay.com/photo/2018/09/07/14/42/west-lake-3661456_960_720.jpg"},
            {name: "哈尔滨冰雪大世界", url: "https://cdn.pixabay.com/photo/2019/01/15/16/52/harbin-3934787_960_720.jpg"},
            {name: "海南三亚", url: "https://cdn.pixabay.com/photo/2018/08/21/23/29/sanya-3622702_960_720.jpg"}
        ];

        // 备用Unsplash图片
        const unsplashImages = [
            {name: "承德避暑山庄", url: "https://source.unsplash.com/400x200/?palace,garden"},
            {name: "天津海河", url: "https://source.unsplash.com/400x200/?river,city"},
            {name: "杭州西湖", url: "https://source.unsplash.com/400x200/?lake,scenic"},
            {name: "哈尔滨冰雪", url: "https://source.unsplash.com/400x200/?ice,snow"},
            {name: "海南三亚", url: "https://source.unsplash.com/400x200/?tropical,beach"}
        ];

        // Wikipedia图片（更稳定）
        const wikipediaImages = [
            {name: "故宫", url: "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2c/Forbidden_City_Beijing_Shenwumen_Gate.JPG/400px-Forbidden_City_Beijing_Shenwumen_Gate.JPG"},
            {name: "长城", url: "https://upload.wikimedia.org/wikipedia/commons/thumb/1/10/20090529_Great_Wall_8185.jpg/400px-20090529_Great_Wall_8185.jpg"},
            {name: "西湖", url: "https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Hangzhou_Xihu.jpg/400px-Hangzhou_Xihu.jpg"},
            {name: "天坛", url: "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a2/Temple_of_Heaven%2C_Beijing%2C_China.jpg/400px-Temple_of_Heaven%2C_Beijing%2C_China.jpg"},
            {name: "兵马俑", url: "https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Terracotta_Army_Pit_1_-_14-11-2014.jpg/400px-Terracotta_Army_Pit_1_-_14-11-2014.jpg"}
        ];

        function createImageTest(images, containerId) {
            const container = document.getElementById(containerId);
            
            images.forEach(item => {
                const div = document.createElement('div');
                div.className = 'image-item';
                
                div.innerHTML = `
                    <div class="image-container">
                        <img src="${item.url}" alt="${item.name}" 
                             onload="updateStatus(this, 'success')" 
                             onerror="updateStatus(this, 'error')">
                        <div class="placeholder" style="display: none;">🏞️</div>
                    </div>
                    <div class="image-info">
                        <div class="image-name">${item.name}</div>
                        <div class="image-status loading">⏳ 加载中...</div>
                    </div>
                `;
                
                container.appendChild(div);
            });
        }

        function updateStatus(img, status) {
            const statusDiv = img.closest('.image-item').querySelector('.image-status');
            const placeholder = img.nextElementSibling;
            
            if (status === 'success') {
                statusDiv.className = 'image-status success';
                statusDiv.textContent = '✅ 加载成功';
                img.style.display = 'block';
                placeholder.style.display = 'none';
            } else {
                statusDiv.className = 'image-status error';
                statusDiv.textContent = '❌ 加载失败';
                img.style.display = 'none';
                placeholder.style.display = 'flex';
            }
        }

        // 创建测试
        createImageTest(pixabayImages, 'pixabayGrid');
        createImageTest(unsplashImages, 'unsplashGrid');
        createImageTest(wikipediaImages, 'wikipediaGrid');

        // 统计结果
        setTimeout(() => {
            const allImages = document.querySelectorAll('img');
            let loaded = 0, failed = 0;
            
            allImages.forEach(img => {
                if (img.complete) {
                    if (img.naturalWidth > 0) {
                        loaded++;
                    } else {
                        failed++;
                    }
                }
            });
            
            console.log(`图片加载统计: 成功 ${loaded} 张, 失败 ${failed} 张`);
        }, 5000);
    </script>
</body>
</html>
