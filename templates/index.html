<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌤️ 智能天气穿衣助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            padding: 20px;
            transition: background 1s ease;
            position: relative;
        }

        /* 天气背景样式 */
        body.weather-sunny {
            background: linear-gradient(135deg, #f7b733 0%, #fc4a1a 100%);
        }

        body.weather-cloudy {
            background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
        }

        body.weather-rainy {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
        }

        body.weather-snowy {
            background: linear-gradient(135deg, #e6ddd4 0%, #d5d4d0 100%);
        }

        body.weather-foggy {
            background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
        }

        body.weather-hot {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }

        body.weather-cold {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        /* 动态背景效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0.1;
            z-index: -1;
            transition: opacity 1s ease;
        }

        /* 晴天背景图案 */
        body.weather-sunny::before {
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
            opacity: 0.3;
        }

        /* 雨天背景图案 */
        body.weather-rainy::before {
            background-image:
                linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.1) 50%, transparent 60%),
                linear-gradient(-45deg, transparent 40%, rgba(255, 255, 255, 0.1) 50%, transparent 60%);
            background-size: 20px 20px;
            animation: rain 2s linear infinite;
            opacity: 0.2;
        }

        @keyframes rain {
            0% { background-position: 0 0, 0 0; }
            100% { background-position: 20px 20px, -20px 20px; }
        }

        /* 雪天背景图案 */
        body.weather-snowy::before {
            background-image:
                radial-gradient(circle at 25% 25%, white 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, white 1px, transparent 1px);
            background-size: 50px 50px, 30px 30px;
            animation: snow 10s linear infinite;
            opacity: 0.4;
        }

        @keyframes snow {
            0% { background-position: 0 0, 0 0; }
            100% { background-position: 50px 50px, 30px 30px; }
        }

        /* 多云背景图案 */
        body.weather-cloudy::before {
            background-image:
                radial-gradient(ellipse at 50% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
                radial-gradient(ellipse at 20% 60%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
                radial-gradient(ellipse at 80% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0.3;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .location-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .location-btn {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }

        .location-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 184, 148, 0.4);
        }

        .location-btn:disabled {
            background: #ddd;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .location-status {
            margin-top: 15px;
            font-size: 0.9em;
            min-height: 20px;
        }

        .location-status.success {
            color: #00b894;
            font-weight: 600;
        }

        .location-status.error {
            color: #e17055;
            font-weight: 600;
        }

        .location-status.loading {
            color: #74b9ff;
            font-weight: 600;
        }

        .location-hint {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 15px;
            margin-top: 15px;
            animation: slideIn 0.3s ease;
        }

        .hint-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .hint-icon {
            font-size: 1.2em;
        }

        .hint-text {
            flex: 1;
            font-size: 0.9em;
        }

        .hint-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            font-weight: bold;
        }

        .troubleshooting {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 15px;
            margin-top: 20px;
            padding: 20px;
            text-align: left;
            animation: slideIn 0.3s ease;
        }

        .trouble-content h4 {
            color: #2d3436;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .trouble-content ul, .trouble-content ol {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .trouble-content li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .trouble-content ul ul {
            margin-top: 5px;
            margin-bottom: 5px;
        }

        .trouble-close {
            background: #e17055;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }

        .trouble-close:hover {
            background: #d63031;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .city-selector {
            margin-bottom: 30px;
            text-align: center;
        }

        .city-selector label {
            display: block;
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2d3436;
        }

        .city-select {
            width: 300px;
            padding: 15px 20px;
            font-size: 1.1em;
            border: 2px solid #ddd;
            border-radius: 50px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
        }

        .city-select:focus {
            border-color: #74b9ff;
            box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.2);
        }

        .loading {
            text-align: center;
            padding: 40px;
            display: none;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #74b9ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .weather-card {
            display: none;
            background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            color: white;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: background 1s ease, box-shadow 0.5s ease;
        }

        /* 不同天气状况的卡片样式 */
        body.weather-sunny .weather-card {
            background: linear-gradient(135deg, #f7b733 0%, #fc4a1a 100%);
            box-shadow: 0 10px 30px rgba(247, 183, 51, 0.3);
        }

        body.weather-hot .weather-card {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        body.weather-cloudy .weather-card {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            box-shadow: 0 10px 30px rgba(116, 185, 255, 0.3);
        }

        body.weather-rainy .weather-card {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            box-shadow: 0 10px 30px rgba(75, 108, 183, 0.3);
        }

        body.weather-snowy .weather-card {
            background: linear-gradient(135deg, #ddd6fe 0%, #a78bfa 100%);
            box-shadow: 0 10px 30px rgba(221, 214, 254, 0.3);
        }

        body.weather-foggy .weather-card {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            box-shadow: 0 10px 30px rgba(149, 165, 166, 0.3);
        }

        body.weather-cold .weather-card {
            background: linear-gradient(135deg, #a8edea 0%, #74b9ff 100%);
            box-shadow: 0 10px 30px rgba(168, 237, 234, 0.3);
        }

        .weather-icon {
            font-size: 4em;
            margin-bottom: 15px;
            display: block;
            transition: transform 0.3s ease;
            animation: float 3s ease-in-out infinite;
        }

        .weather-icon:hover {
            transform: scale(1.1);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 特殊天气图标动画 */
        body.weather-rainy .weather-icon {
            animation: bounce 2s ease-in-out infinite;
        }

        body.weather-snowy .weather-icon {
            animation: spin 8s linear infinite;
        }

        body.weather-sunny .weather-icon {
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .weather-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .weather-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .weather-item h4 {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .weather-item p {
            font-size: 1.2em;
            font-weight: 600;
        }

        .advice-section {
            display: none;
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .recommendations-section {
            display: none;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .recommendations-section h3 {
            color: #2d3436;
            font-size: 1.5em;
            margin-bottom: 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .recommendations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .recommendation-card {
            background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
            border-radius: 15px;
            padding: 20px;
            color: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .recommendation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .recommendation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .recommendation-category {
            font-size: 0.9em;
            opacity: 0.8;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 20px;
        }

        .recommendation-item {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .recommendation-reason {
            font-size: 0.9em;
            opacity: 0.9;
            margin-bottom: 15px;
        }

        .recommendation-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .recommendation-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            transition: background 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .recommendation-link:hover {
            background: rgba(255, 255, 255, 0.3);
            text-decoration: none;
            color: white;
        }

        /* 不同类别的卡片颜色 */
        .recommendation-card.category-上衣 {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        .recommendation-card.category-外套 {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
        }

        .recommendation-card.category-防晒 {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
        }

        .recommendation-card.category-雨具 {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        }

        .recommendation-card.category-鞋子 {
            background: linear-gradient(135deg, #e84393 0%, #fd79a8 100%);
        }

        .recommendation-card.category-配饰 {
            background: linear-gradient(135deg, #00cec9 0%, #55a3ff 100%);
        }

        /* 加载提示样式 */
        .loading-item {
            color: #74b9ff !important;
            font-style: italic;
            text-align: center;
            padding: 20px;
            background: rgba(116, 185, 255, 0.1);
            border-radius: 10px;
            animation: pulse 2s ease-in-out infinite;
        }

        .loading-recommendation {
            grid-column: 1 / -1;
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
            border-radius: 15px;
            color: white;
            animation: pulse 2s ease-in-out infinite;
        }

        .loading-icon {
            font-size: 3em;
            margin-bottom: 15px;
            animation: bounce 1s ease-in-out infinite;
        }

        .loading-text {
            font-size: 1.2em;
            font-weight: 500;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        /* 旅行推荐样式 */
        .travel-section {
            display: none;
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .travel-section h3 {
            color: #2d3436;
            font-size: 1.5em;
            margin-bottom: 25px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .travel-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .travel-category {
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .travel-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .category-header {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .category-header h4 {
            margin: 0;
            font-size: 1.1em;
            font-weight: 600;
        }

        .refresh-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2em;
            transition: background 0.3s ease, transform 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .refresh-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(180deg);
        }

        .travel-card {
            padding: 0;
        }

        .travel-image {
            width: 100%;
            height: 200px;
            overflow: hidden;
            position: relative;
        }

        .scenic-icon {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 4em;
            text-shadow: 0 3px 6px rgba(0,0,0,0.3);
            transition: transform 0.3s ease, background 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .scenic-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }

        .travel-category:hover .scenic-icon {
            transform: scale(1.05);
        }

        /* 真实图片样式 */
        .travel-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 10px;
            transition: transform 0.3s ease, opacity 0.3s ease;
        }

        .travel-image img:hover {
            transform: scale(1.02);
        }

        /* 图片加载状态 */
        .travel-image.loading img {
            opacity: 0.7;
        }

        .travel-image.loaded .scenic-icon {
            display: none;
        }

        .travel-image.error .scenic-icon {
            display: flex;
        }

        .travel-image.error img {
            display: none;
        }

        .travel-category:hover .travel-image img {
            transform: scale(1.05);
        }

        .travel-info {
            padding: 20px;
        }

        .travel-info h5 {
            color: #2d3436;
            font-size: 1.2em;
            margin: 0 0 10px 0;
            font-weight: 600;
        }

        .travel-info p {
            color: #636e72;
            font-size: 0.9em;
            line-height: 1.5;
            margin: 0 0 15px 0;
        }

        .travel-details {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .travel-details span {
            background: #e17055;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .distance {
            background: #00b894 !important;
        }

        .travel-time {
            background: #fdcb6e !important;
        }

        .city {
            background: #a29bfe !important;
        }

        .reason {
            background: #fd79a8 !important;
        }

        /* 机票价格样式 */
        .flight-price {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            border-left: 4px solid #007bff;
        }

        .flight-price h6 {
            color: #007bff;
            font-size: 0.9em;
            margin: 0 0 10px 0;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .price-main {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .cheapest-price {
            font-size: 1.4em;
            font-weight: bold;
            color: #28a745;
        }

        .price-date {
            font-size: 0.8em;
            color: #6c757d;
        }

        .price-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.8em;
            color: #6c757d;
        }

        .price-savings {
            color: #28a745;
            font-weight: 500;
        }

        .price-trend {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7em;
            font-weight: 500;
        }

        .trend-stable {
            background: #d4edda;
            color: #155724;
        }

        .trend-volatile {
            background: #fff3cd;
            color: #856404;
        }

        .advice-section h3 {
            color: #2d3436;
            font-size: 1.5em;
            margin-bottom: 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .advice-list {
            list-style: none;
        }

        .advice-item {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            padding: 15px 20px;
            margin-bottom: 15px;
            border-radius: 15px;
            font-size: 1.1em;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .advice-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .advice-item:last-child {
            margin-bottom: 0;
        }

        .error-message {
            display: none;
            background: #ff7675;
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 20px;
        }

        .timestamp {
            text-align: center;
            color: #636e72;
            font-size: 0.9em;
            margin-top: 20px;
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .content {
                padding: 20px;
            }

            .city-select {
                width: 100%;
                max-width: 300px;
            }

            .weather-info {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .recommendations-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .recommendation-links {
                justify-content: center;
            }

            .recommendation-link {
                font-size: 0.8em;
                padding: 6px 12px;
            }

            .travel-categories {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .travel-image {
                height: 150px;
            }

            .travel-details {
                justify-content: center;
            }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌤️ 智能天气穿衣助手</h1>
            <p>选择城市，获取天气信息和AI穿衣建议</p>
        </div>

        <div class="content">
            <div class="location-section">
                <button id="locationBtn" class="location-btn">
                    📍 获取我的位置
                </button>
                <button onclick="testImageLoad()" class="location-btn" style="background: #e17055; margin-left: 10px;">
                    🧪 测试图片
                </button>
                <div class="location-status" id="locationStatus"></div>
            </div>

            <div class="city-selector">
                <label for="citySelect">🏙️ 选择城市：</label>
                <select id="citySelect" class="city-select">
                    <option value="">请选择城市...</option>
                    <optgroup label="🇨🇳 中国主要城市">
                        <option value="Beijing">北京 Beijing</option>
                        <option value="Shanghai">上海 Shanghai</option>
                        <option value="Guangzhou">广州 Guangzhou</option>
                        <option value="Shenzhen">深圳 Shenzhen</option>
                        <option value="Hangzhou">杭州 Hangzhou</option>
                        <option value="Nanjing">南京 Nanjing</option>
                        <option value="Chengdu">成都 Chengdu</option>
                        <option value="Wuhan">武汉 Wuhan</option>
                        <option value="Xi'an">西安 Xi'an</option>
                        <option value="Chongqing">重庆 Chongqing</option>
                    </optgroup>
                    <optgroup label="🌍 国际城市">
                        <option value="Tokyo">东京 Tokyo</option>
                        <option value="Seoul">首尔 Seoul</option>
                        <option value="Singapore">新加坡 Singapore</option>
                        <option value="Bangkok">曼谷 Bangkok</option>
                        <option value="New York">纽约 New York</option>
                        <option value="London">伦敦 London</option>
                        <option value="Paris">巴黎 Paris</option>
                        <option value="Sydney">悉尼 Sydney</option>
                        <option value="Dubai">迪拜 Dubai</option>
                        <option value="Moscow">莫斯科 Moscow</option>
                    </optgroup>
                </select>
            </div>

            <div class="loading" id="loading">
                <div class="loading-spinner"></div>
                <p>正在获取天气信息和AI建议...</p>
            </div>

            <div class="error-message" id="errorMessage"></div>

            <div class="weather-card" id="weatherCard">
                <span class="weather-icon" id="weatherIcon"></span>
                <h2 id="cityName"></h2>
                <h3 id="temperature"></h3>
                <p id="description"></p>
                
                <div class="weather-info">
                    <div class="weather-item">
                        <h4>体感温度</h4>
                        <p id="feelsLike"></p>
                    </div>
                    <div class="weather-item">
                        <h4>湿度</h4>
                        <p id="humidity"></p>
                    </div>
                    <div class="weather-item">
                        <h4>气压</h4>
                        <p id="pressure"></p>
                    </div>
                    <div class="weather-item">
                        <h4>风速</h4>
                        <p id="windSpeed"></p>
                    </div>
                </div>
            </div>

            <div class="advice-section" id="adviceSection">
                <h3>👗 AI穿衣建议</h3>
                <ul class="advice-list" id="adviceList"></ul>
                <div class="timestamp" id="timestamp"></div>
            </div>

            <div class="recommendations-section" id="recommendationsSection">
                <h3>🛒 推荐购买</h3>
                <div class="recommendations-grid" id="recommendationsGrid"></div>
            </div>

            <div class="travel-section" id="travelSection">
                <h3>✈️ 当月旅行推荐</h3>
                <div class="travel-categories">
                    <!-- 周边推荐 -->
                    <div class="travel-category">
                        <div class="category-header">
                            <h4>🚗 周边游</h4>
                            <button class="refresh-btn" onclick="refreshTravel('nearby')">🔄</button>
                        </div>
                        <div class="travel-card" id="nearbyCard">
                            <div class="travel-image" id="nearbyImageContainer">
                                <img id="nearbyImage" src="" alt="景点图片" style="display: none;">
                                <div class="scenic-icon" id="nearbyIcon">🚗</div>
                            </div>
                            <div class="travel-info">
                                <h5 id="nearbyName">加载中...</h5>
                                <p id="nearbyDescription">正在加载推荐...</p>
                                <div class="travel-details">
                                    <span id="nearbyDistance" class="distance">-</span>
                                    <span id="nearbyTime" class="travel-time">-</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 国内推荐 -->
                    <div class="travel-category">
                        <div class="category-header">
                            <h4>🏔️ 国内游</h4>
                            <button class="refresh-btn" onclick="refreshTravel('domestic')">🔄</button>
                        </div>
                        <div class="travel-card" id="domesticCard">
                            <div class="travel-image" id="domesticImageContainer">
                                <img id="domesticImage" src="" alt="景点图片" style="display: none;">
                                <div class="scenic-icon" id="domesticIcon">🏔️</div>
                            </div>
                            <div class="travel-info">
                                <h5 id="domesticName">加载中...</h5>
                                <p id="domesticDescription">正在加载推荐...</p>
                                <div class="travel-details">
                                    <span id="domesticCity" class="city">-</span>
                                    <span id="domesticReason" class="reason">-</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 国外推荐 -->
                    <div class="travel-category">
                        <div class="category-header">
                            <h4>🌍 出境游</h4>
                            <button class="refresh-btn" onclick="refreshTravel('international')">🔄</button>
                        </div>
                        <div class="travel-card" id="internationalCard">
                            <div class="travel-image" id="internationalImageContainer">
                                <img id="internationalImage" src="" alt="景点图片" style="display: none;">
                                <div class="scenic-icon" id="internationalIcon">🌍</div>
                            </div>
                            <div class="travel-info">
                                <h5 id="internationalName">加载中...</h5>
                                <p id="internationalDescription">正在加载推荐...</p>
                                <div class="travel-details">
                                    <span id="internationalCity" class="city">-</span>
                                    <span id="internationalReason" class="reason">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const citySelect = document.getElementById('citySelect');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('errorMessage');
        const weatherCard = document.getElementById('weatherCard');
        const adviceSection = document.getElementById('adviceSection');
        const recommendationsSection = document.getElementById('recommendationsSection');
        const travelSection = document.getElementById('travelSection');
        const locationBtn = document.getElementById('locationBtn');
        const locationStatus = document.getElementById('locationStatus');

        // 存储旅行推荐数据
        let currentTravelData = null;
        let currentTravelIndices = {
            nearby: 0,
            domestic: 0,
            international: 0
        };

        // 页面加载时自动尝试获取位置
        window.addEventListener('load', function() {
            if (navigator.geolocation) {
                setTimeout(autoGetLocation, 1000); // 延迟1秒自动获取位置
            }
        });

        // 位置按钮点击事件
        locationBtn.addEventListener('click', function() {
            getCurrentLocation();
        });

        citySelect.addEventListener('change', function() {
            const selectedCity = this.value;
            if (selectedCity) {
                getWeatherData(selectedCity);
            } else {
                hideAllSections();
            }
        });

        function hideAllSections() {
            weatherCard.style.display = 'none';
            adviceSection.style.display = 'none';
            recommendationsSection.style.display = 'none';
            travelSection.style.display = 'none';
            errorMessage.style.display = 'none';
            loading.style.display = 'none';

            // 重置背景为默认状态
            resetWeatherBackground();
        }

        // 重置天气背景为默认状态
        function resetWeatherBackground() {
            document.body.classList.remove(
                'weather-sunny', 'weather-cloudy', 'weather-rainy',
                'weather-snowy', 'weather-foggy', 'weather-hot', 'weather-cold'
            );
        }

        function showLoading() {
            hideAllSections();
            loading.style.display = 'block';
        }

        function showError(message) {
            hideAllSections();
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            errorMessage.classList.add('fade-in');
        }

        // 自动获取位置（静默模式）
        function autoGetLocation() {
            if (!navigator.geolocation) {
                console.log('浏览器不支持地理位置');
                return; // 静默失败，不显示错误
            }

            // 检查是否是HTTPS或localhost
            if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
                console.log('地理位置需要HTTPS或localhost环境');
                return;
            }

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const lat = position.coords.latitude;
                    const lon = position.coords.longitude;
                    console.log('自动定位成功:', lat, lon);
                    getCityFromCoordinates(lat, lon, true); // true表示自动模式
                },
                function(error) {
                    // 自动获取失败时不显示错误，保持静默
                    console.log('自动定位失败:', error.code, error.message);
                    // 显示提示信息，引导用户手动定位
                    showLocationHint();
                },
                {
                    timeout: 8000,
                    maximumAge: 600000, // 10分钟缓存
                    enableHighAccuracy: false // 使用网络定位，更快
                }
            );
        }

        // 显示定位提示
        function showLocationHint() {
            const hint = document.createElement('div');
            hint.className = 'location-hint';
            hint.innerHTML = `
                <div class="hint-content">
                    <span class="hint-icon">💡</span>
                    <span class="hint-text">点击"📍 获取我的位置"按钮来自动定位您的城市</span>
                    <button class="hint-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
            `;

            const locationSection = document.querySelector('.location-section');
            locationSection.appendChild(hint);

            // 3秒后自动消失
            setTimeout(() => {
                if (hint.parentElement) {
                    hint.remove();
                }
            }, 5000);
        }

        // 手动获取位置
        function getCurrentLocation() {
            if (!navigator.geolocation) {
                showLocationStatus('您的浏览器不支持地理位置功能', 'error');
                showLocationTroubleshooting('browser');
                return;
            }

            // 检查协议
            if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
                showLocationStatus('地理位置需要HTTPS或localhost环境', 'error');
                showLocationTroubleshooting('protocol');
                return;
            }

            locationBtn.disabled = true;
            locationBtn.textContent = '🔍 定位中...';
            showLocationStatus('正在获取您的位置...', 'loading');

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const lat = position.coords.latitude;
                    const lon = position.coords.longitude;
                    console.log('手动定位成功:', lat, lon);
                    getCityFromCoordinates(lat, lon, false); // false表示手动模式
                },
                function(error) {
                    let errorMsg = '定位失败: ';
                    let troubleshootType = 'general';

                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMsg += '用户拒绝了定位请求';
                            troubleshootType = 'permission';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMsg += '位置信息不可用';
                            troubleshootType = 'unavailable';
                            break;
                        case error.TIMEOUT:
                            errorMsg += '定位请求超时';
                            troubleshootType = 'timeout';
                            break;
                        default:
                            errorMsg += '未知错误';
                            troubleshootType = 'general';
                            break;
                    }

                    console.error('定位错误:', error.code, error.message);
                    showLocationStatus(errorMsg, 'error');
                    showLocationTroubleshooting(troubleshootType);
                    resetLocationButton();
                },
                {
                    timeout: 15000,
                    maximumAge: 60000, // 1分钟缓存
                    enableHighAccuracy: false // Mac上使用网络定位更稳定
                }
            );
        }

        function showLocationStatus(message, type) {
            locationStatus.textContent = message;
            locationStatus.className = `location-status ${type}`;
        }

        function resetLocationButton() {
            locationBtn.disabled = false;
            locationBtn.textContent = '📍 获取我的位置';
        }

        // 显示故障排除建议
        function showLocationTroubleshooting(type) {
            const existingTrouble = document.querySelector('.troubleshooting');
            if (existingTrouble) {
                existingTrouble.remove();
            }

            const troubleshooting = document.createElement('div');
            troubleshooting.className = 'troubleshooting';

            let content = '';

            switch(type) {
                case 'permission':
                    content = `
                        <h4>🔧 Mac定位权限设置：</h4>
                        <ol>
                            <li><strong>Safari浏览器：</strong>
                                <ul>
                                    <li>点击Safari菜单 → 偏好设置 → 网站</li>
                                    <li>在左侧选择"位置"</li>
                                    <li>找到localhost或您的网站，设置为"允许"</li>
                                </ul>
                            </li>
                            <li><strong>Chrome浏览器：</strong>
                                <ul>
                                    <li>点击地址栏左侧的🔒图标</li>
                                    <li>将"位置"设置为"允许"</li>
                                    <li>刷新页面重试</li>
                                </ul>
                            </li>
                            <li><strong>系统设置：</strong>
                                <ul>
                                    <li>系统偏好设置 → 安全性与隐私 → 隐私</li>
                                    <li>选择"位置服务"</li>
                                    <li>确保浏览器有位置权限</li>
                                </ul>
                            </li>
                        </ol>
                    `;
                    break;
                case 'unavailable':
                    content = `
                        <h4>🔧 位置服务故障排除：</h4>
                        <ul>
                            <li>确保Mac的位置服务已开启</li>
                            <li>检查WiFi连接是否正常</li>
                            <li>尝试重启浏览器</li>
                            <li>如果使用VPN，请尝试关闭</li>
                        </ul>
                    `;
                    break;
                case 'timeout':
                    content = `
                        <h4>🔧 定位超时解决方案：</h4>
                        <ul>
                            <li>检查网络连接</li>
                            <li>稍等片刻后重试</li>
                            <li>尝试刷新页面</li>
                            <li>可以手动选择城市</li>
                        </ul>
                    `;
                    break;
                case 'protocol':
                    content = `
                        <h4>🔧 协议问题：</h4>
                        <ul>
                            <li>地理位置API需要HTTPS或localhost环境</li>
                            <li>当前正在localhost，应该可以正常工作</li>
                            <li>请检查浏览器权限设置</li>
                        </ul>
                    `;
                    break;
                default:
                    content = `
                        <h4>🔧 通用解决方案：</h4>
                        <ul>
                            <li>刷新页面重试</li>
                            <li>检查浏览器权限设置</li>
                            <li>确保位置服务已开启</li>
                            <li>可以手动选择城市</li>
                        </ul>
                    `;
            }

            troubleshooting.innerHTML = `
                <div class="trouble-content">
                    ${content}
                    <button class="trouble-close" onclick="this.parentElement.parentElement.remove()">关闭</button>
                </div>
            `;

            const locationSection = document.querySelector('.location-section');
            locationSection.appendChild(troubleshooting);
        }

        // 根据坐标获取城市信息
        async function getCityFromCoordinates(lat, lon, isAuto = false) {
            try {
                if (!isAuto) {
                    showLocationStatus('正在解析位置信息...', 'loading');
                }

                const response = await fetch(`/api/location/${lat}/${lon}`);

                if (!response.ok) {
                    throw new Error('无法获取位置信息');
                }

                const data = await response.json();

                if (data.city) {
                    // 尝试在下拉列表中找到匹配的城市
                    const cityOption = findCityOption(data.city);

                    if (cityOption) {
                        citySelect.value = cityOption.value;
                        if (!isAuto) {
                            showLocationStatus(`✅ 已定位到: ${data.city}`, 'success');
                        }
                        // 自动获取该城市的天气
                        if (cityOption.value) {
                            getWeatherData(cityOption.value);
                        }
                    } else {
                        // 如果下拉列表中没有该城市，直接使用坐标获取天气
                        if (!isAuto) {
                            showLocationStatus(`📍 当前位置: ${data.city}`, 'success');
                        }
                        if (data.city) {
                            getWeatherDataByCoords(lat, lon, data.city);
                        }
                    }
                } else {
                    throw new Error('无法识别当前位置');
                }

                if (!isAuto) {
                    resetLocationButton();
                }

            } catch (error) {
                console.error('获取城市信息失败:', error);
                if (!isAuto) {
                    showLocationStatus('获取位置信息失败，请手动选择城市', 'error');
                    resetLocationButton();
                }
            }
        }

        // 城市别名映射表
        const cityAliases = {
            // 中国城市别名
            'beijing': ['beijing', '北京', 'peking'],
            'shanghai': ['shanghai', '上海'],
            'guangzhou': ['guangzhou', '广州', 'canton'],
            'shenzhen': ['shenzhen', '深圳'],
            'hangzhou': ['hangzhou', '杭州'],
            'nanjing': ['nanjing', '南京', 'nanking'],
            'chengdu': ['chengdu', '成都'],
            'wuhan': ['wuhan', '武汉'],
            "xi'an": ["xi'an", 'xian', '西安'],
            'chongqing': ['chongqing', '重庆'],

            // 国际城市别名
            'tokyo': ['tokyo', '东京', 'edo'],
            'seoul': ['seoul', '首尔', 'hanyang'],
            'singapore': ['singapore', '新加坡'],
            'bangkok': ['bangkok', '曼谷', 'krung thep'],
            'new york': ['new york', 'newyork', 'nyc', '纽约', 'new york city'],
            'london': ['london', '伦敦'],
            'paris': ['paris', '巴黎'],
            'sydney': ['sydney', '悉尼'],
            'dubai': ['dubai', '迪拜'],
            'moscow': ['moscow', '莫斯科', 'moskva']
        };

        // 在下拉列表中查找城市
        function findCityOption(cityName) {
            const options = citySelect.querySelectorAll('option');
            const cityLower = cityName.toLowerCase().trim();

            console.log('正在匹配城市:', cityName);

            // 第一轮：精确匹配
            for (let option of options) {
                if (!option.value) continue; // 跳过空选项

                const optionValue = option.value.toLowerCase();
                const optionText = option.textContent.toLowerCase();

                // 精确匹配option value
                if (optionValue === cityLower) {
                    console.log('精确匹配成功:', option.value);
                    return option;
                }

                // 检查别名映射
                const aliases = cityAliases[optionValue];
                if (aliases && aliases.some(alias => alias.toLowerCase() === cityLower)) {
                    console.log('别名匹配成功:', option.value, '别名:', cityLower);
                    return option;
                }
            }

            // 第二轮：包含匹配
            for (let option of options) {
                if (!option.value) continue;

                const optionValue = option.value.toLowerCase();
                const optionText = option.textContent.toLowerCase();

                // 检查是否包含城市名
                if (optionValue.includes(cityLower) ||
                    cityLower.includes(optionValue) ||
                    optionText.includes(cityLower)) {
                    console.log('包含匹配成功:', option.value);
                    return option;
                }

                // 检查别名包含匹配
                const aliases = cityAliases[optionValue];
                if (aliases) {
                    for (let alias of aliases) {
                        if (alias.toLowerCase().includes(cityLower) ||
                            cityLower.includes(alias.toLowerCase())) {
                            console.log('别名包含匹配成功:', option.value, '别名:', alias);
                            return option;
                        }
                    }
                }
            }

            // 第三轮：模糊匹配（去除空格和特殊字符）
            const cleanCityName = cityLower.replace(/[\s\-_']/g, '');

            for (let option of options) {
                if (!option.value) continue;

                const cleanOptionValue = option.value.toLowerCase().replace(/[\s\-_']/g, '');

                if (cleanOptionValue.includes(cleanCityName) ||
                    cleanCityName.includes(cleanOptionValue)) {
                    console.log('模糊匹配成功:', option.value);
                    return option;
                }
            }

            console.log('未找到匹配的城市:', cityName);
            return null;
        }

        // 使用坐标直接获取天气
        async function getWeatherDataByCoords(lat, lon, cityName) {
            showLoading();

            try {
                const response = await fetch(`/api/weather/coords/${lat}/${lon}`);

                if (!response.ok) {
                    throw new Error('无法获取天气信息');
                }

                const data = await response.json();
                data.city = cityName; // 使用解析出的城市名
                displayWeatherData(data);

            } catch (error) {
                console.error('Error:', error);
                showError('获取天气信息失败，请稍后重试');
            }
        }

        async function getWeatherData(city) {
            showLoading();

            try {
                const response = await fetch(`/api/weather/${encodeURIComponent(city)}`);
                
                if (!response.ok) {
                    throw new Error('无法获取天气信息');
                }

                const data = await response.json();
                displayWeatherData(data);
                
            } catch (error) {
                console.error('Error:', error);
                showError('获取天气信息失败，请稍后重试');
            }
        }

        // 根据天气状况设置背景
        function setWeatherBackground(description, temperature) {
            // 移除所有天气背景类
            document.body.classList.remove(
                'weather-sunny', 'weather-cloudy', 'weather-rainy',
                'weather-snowy', 'weather-foggy', 'weather-hot', 'weather-cold'
            );

            const desc = description.toLowerCase();

            // 根据天气描述设置背景
            if (desc.includes('雨') || desc.includes('rain') || desc.includes('shower')) {
                document.body.classList.add('weather-rainy');
            } else if (desc.includes('雪') || desc.includes('snow')) {
                document.body.classList.add('weather-snowy');
            } else if (desc.includes('雾') || desc.includes('fog') || desc.includes('霾') || desc.includes('haze')) {
                document.body.classList.add('weather-foggy');
            } else if (desc.includes('云') || desc.includes('cloud') || desc.includes('阴')) {
                document.body.classList.add('weather-cloudy');
            } else if (desc.includes('晴') || desc.includes('clear') || desc.includes('sunny')) {
                // 根据温度细分晴天背景
                if (temperature > 30) {
                    document.body.classList.add('weather-hot');
                } else {
                    document.body.classList.add('weather-sunny');
                }
            } else {
                // 默认根据温度设置背景
                if (temperature > 30) {
                    document.body.classList.add('weather-hot');
                } else if (temperature < 5) {
                    document.body.classList.add('weather-cold');
                } else {
                    document.body.classList.add('weather-sunny');
                }
            }

            console.log(`设置天气背景: ${description} (${temperature}°C)`);
        }

        function displayWeatherData(data) {
            hideAllSections();

            // 设置天气背景
            setWeatherBackground(data.description, data.temperature);

            // 第一步：显示天气信息
            displayWeatherInfo(data);

            // 第二步：延迟显示穿衣建议
            setTimeout(() => {
                displayClothingAdvice(data);
            }, 800);

            // 第三步：延迟显示购买推荐
            setTimeout(() => {
                displayPurchaseRecommendations(data);
            }, 1600);

            // 第四步：延迟显示旅行推荐
            setTimeout(() => {
                displayTravelRecommendations(data);
            }, 2400);
        }

        // 第一步：显示天气信息
        function displayWeatherInfo(data) {
            // 填充天气数据
            document.getElementById('weatherIcon').textContent = data.weather_icon;
            document.getElementById('cityName').textContent = `${data.city}, ${data.country}`;
            document.getElementById('temperature').textContent = `${data.temperature}°C`;
            document.getElementById('description').textContent = data.description;
            document.getElementById('feelsLike').textContent = `${data.feels_like}°C`;
            document.getElementById('humidity').textContent = `${data.humidity}%`;
            document.getElementById('pressure').textContent = `${data.pressure} hPa`;
            document.getElementById('windSpeed').textContent = `${data.wind_speed} m/s`;

            // 显示天气卡片
            weatherCard.style.display = 'block';
            weatherCard.classList.add('fade-in');

            console.log('✅ 第一步：天气信息已显示');
        }

        // 第二步：显示穿衣建议
        function displayClothingAdvice(data) {
            // 显示建议区域（先显示标题）
            adviceSection.style.display = 'block';
            adviceSection.classList.add('fade-in');

            // 显示加载提示
            const adviceList = document.getElementById('adviceList');
            adviceList.innerHTML = '<li class="advice-item loading-item">🤖 AI正在分析天气，生成穿衣建议...</li>';

            // 延迟显示实际建议
            setTimeout(() => {
                adviceList.innerHTML = '';

                // 逐个添加建议项，带动画效果
                data.clothing_advice.forEach((advice, index) => {
                    setTimeout(() => {
                        const li = document.createElement('li');
                        li.className = 'advice-item';
                        li.textContent = advice;
                        li.style.opacity = '0';
                        li.style.transform = 'translateY(20px)';
                        adviceList.appendChild(li);

                        // 添加淡入动画
                        setTimeout(() => {
                            li.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                            li.style.opacity = '1';
                            li.style.transform = 'translateY(0)';
                        }, 50);
                    }, index * 200); // 每个建议间隔200ms
                });
            }, 500);

            // 显示时间戳
            document.getElementById('timestamp').textContent = `更新时间: ${data.timestamp}`;

            console.log('✅ 第二步：穿衣建议已显示');
        }

        // 第三步：显示购买推荐
        function displayPurchaseRecommendations(data) {
            console.log('检查购买推荐数据:', data.clothing_recommendations);

            if (data.clothing_recommendations && data.clothing_recommendations.length > 0) {
                console.log('显示购买推荐，数量:', data.clothing_recommendations.length);

                // 显示推荐区域
                recommendationsSection.style.display = 'block';
                recommendationsSection.classList.add('fade-in');

                // 显示加载提示
                const recommendationsGrid = document.getElementById('recommendationsGrid');
                recommendationsGrid.innerHTML = `
                    <div class="loading-recommendation">
                        <div class="loading-icon">🛒</div>
                        <div class="loading-text">正在为您推荐合适的衣物...</div>
                    </div>
                `;

                // 延迟显示推荐卡片，带动画效果
                setTimeout(() => {
                    displayRecommendations(data.clothing_recommendations);
                }, 800);

                console.log('✅ 第三步：购买推荐已显示');
            } else {
                console.log('没有购买推荐数据或数据为空');
                recommendationsSection.style.display = 'none';
            }
        }

        // 显示购买推荐（带动画效果）
        function displayRecommendations(recommendations) {
            const recommendationsGrid = document.getElementById('recommendationsGrid');
            recommendationsGrid.innerHTML = '';

            recommendations.forEach((rec, index) => {
                setTimeout(() => {
                    const card = document.createElement('div');
                    card.className = `recommendation-card category-${rec.category}`;

                    const linksHtml = Object.entries(rec.links).map(([platform, url]) =>
                        `<a href="${url}" target="_blank" class="recommendation-link">${platform}</a>`
                    ).join('');

                    card.innerHTML = `
                        <div class="recommendation-header">
                            <div class="recommendation-category">${rec.category}</div>
                        </div>
                        <div class="recommendation-item">${rec.item}</div>
                        <div class="recommendation-reason">${rec.reason}</div>
                        <div class="recommendation-links">
                            ${linksHtml}
                        </div>
                    `;

                    // 初始状态：隐藏和向上偏移
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px) scale(0.95)';
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

                    recommendationsGrid.appendChild(card);

                    // 添加淡入和滑入动画
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0) scale(1)';
                    }, 100);

                }, index * 300); // 每个卡片间隔300ms
            });
        }

        // 第四步：显示旅行推荐
        function displayTravelRecommendations(data) {
            console.log('检查旅行推荐数据:', data.travel_recommendations);

            if (data.travel_recommendations) {
                currentTravelData = data.travel_recommendations;

                // 显示旅行推荐区域
                travelSection.style.display = 'block';
                travelSection.classList.add('fade-in');

                // 显示各类推荐
                displayTravelCategory('nearby', currentTravelData.nearby);
                setTimeout(() => displayTravelCategory('domestic', currentTravelData.domestic), 400);
                setTimeout(() => displayTravelCategory('international', currentTravelData.international), 800);

                console.log('✅ 第四步：旅行推荐已显示');
            } else {
                console.log('没有旅行推荐数据');
                travelSection.style.display = 'none';
            }
        }

        // 显示特定类别的旅行推荐
        function displayTravelCategory(category, data) {
            console.log(`显示${category}推荐, 数据:`, data);
            if (!data || data.length === 0) {
                console.log(`${category}数据为空`);
                return;
            }

            const index = currentTravelIndices[category];
            const item = data[index];
            console.log(`${category}当前项目:`, item);

            // 更新对应的卡片
            document.getElementById(`${category}Name`).textContent = item.name;
            document.getElementById(`${category}Description`).textContent = item.description;

            // 处理景点图片和图标
            const imgElement = document.getElementById(`${category}Image`);
            const iconElement = document.getElementById(`${category}Icon`);
            const imageContainer = document.getElementById(`${category}ImageContainer`);

            // 设置图标作为备用
            if (iconElement && item.icon) {
                iconElement.textContent = item.icon;
                console.log(`设置${category}图标: ${item.icon}`);
            }

            // 设置背景色
            if (imageContainer && item.color) {
                imageContainer.style.background = item.color;
                console.log(`设置${category}背景: ${item.color}`);
            }

            // 尝试加载真实图片
            if (imgElement && item.image) {
                loadSceneryImage(imgElement, iconElement, imageContainer, item.image, item.name);
            }

            if (category === 'nearby') {
                document.getElementById(`${category}Distance`).textContent = item.distance;
                document.getElementById(`${category}Time`).textContent = item.travel_time;
            } else {
                document.getElementById(`${category}City`).textContent = item.city;
                document.getElementById(`${category}Reason`).textContent = item.reason;

                // 显示机票价格信息
                if (item.flight_price) {
                    displayFlightPrice(category, item.flight_price);
                }
            }

            // 添加淡入动画
            const card = document.getElementById(`${category}Card`);
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }

        // 获取占位符图标
        function getPlaceholderIcon(category) {
            const icons = {
                'nearby': '🚗',
                'domestic': '🏔️',
                'international': '🌍'
            };
            return icons[category] || '🏞️';
        }

        // 获取备用图片URL
        function getFallbackImageUrl(name, category) {
            const fallbackImages = {
                // 周边景点备用图片
                "承德避暑山庄": "https://source.unsplash.com/400x200/?palace,garden",
                "天津海河": "https://source.unsplash.com/400x200/?river,city",
                "秦皇岛北戴河": "https://source.unsplash.com/400x200/?beach,seaside",
                "苏州园林": "https://source.unsplash.com/400x200/?garden,traditional",
                "杭州西湖": "https://source.unsplash.com/400x200/?lake,scenic",
                "南京夫子庙": "https://source.unsplash.com/400x200/?temple,traditional",
                "富士山": "https://source.unsplash.com/400x200/?fuji,mountain",
                "镰仓大佛": "https://source.unsplash.com/400x200/?buddha,temple",
                "箱根温泉": "https://source.unsplash.com/400x200/?hotspring,japan",

                // 国内景点备用图片
                "哈尔滨冰雪大世界": "https://source.unsplash.com/400x200/?ice,snow",
                "海南三亚": "https://source.unsplash.com/400x200/?tropical,beach",
                "云南大理": "https://source.unsplash.com/400x200/?mountain,ancient",
                "青海湖": "https://source.unsplash.com/400x200/?lake,plateau",
                "内蒙古草原": "https://source.unsplash.com/400x200/?grassland,prairie",
                "新疆伊犁": "https://source.unsplash.com/400x200/?lavender,mountain",

                // 国外景点备用图片
                "日本北海道": "https://source.unsplash.com/400x200/?japan,snow",
                "泰国清迈": "https://source.unsplash.com/400x200/?thailand,temple",
                "新西兰南岛": "https://source.unsplash.com/400x200/?newzealand,nature",
                "法国普罗旺斯": "https://source.unsplash.com/400x200/?provence,lavender",
                "挪威峡湾": "https://source.unsplash.com/400x200/?norway,fjord",
                "土耳其卡帕多奇亚": "https://source.unsplash.com/400x200/?cappadocia,balloon"
            };

            return fallbackImages[name] || `https://source.unsplash.com/400x200/?${category},travel`;
        }

        // 测试图片加载功能
        function testImageLoad() {
            console.log('开始测试图片加载...');

            // 显示旅行推荐区域
            travelSection.style.display = 'block';

            // 创建测试数据
            const testData = {
                nearby: [
                    {
                        name: "故宫博物院",
                        description: "明清两朝的皇家宫殿",
                        icon: "🏯",
                        color: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                        distance: "5km",
                        travel_time: "30分钟"
                    }
                ],
                domestic: [
                    {
                        name: "西湖",
                        city: "杭州",
                        description: "人间天堂，湖光山色",
                        icon: "🌸",
                        color: "linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)",
                        reason: "四季皆宜",
                        flight_price: {
                            cheapest_price: 1280,
                            cheapest_date: "2024-08-15",
                            average_price: 1450,
                            savings: 170,
                            price_trend: "stable"
                        }
                    }
                ],
                international: [
                    {
                        name: "富士山",
                        city: "东京",
                        description: "日本最高峰，神圣象征",
                        icon: "🗻",
                        color: "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)",
                        reason: "自然奇观",
                        flight_price: {
                            cheapest_price: 3200,
                            cheapest_date: "2024-08-20",
                            average_price: 3800,
                            savings: 600,
                            price_trend: "volatile"
                        }
                    }
                ]
            };

            currentTravelData = testData;

            // 测试显示
            console.log('测试数据:', testData);
            displayTravelCategory('nearby', testData.nearby);
            displayTravelCategory('domestic', testData.domestic);
            displayTravelCategory('international', testData.international);

            alert('测试图片加载已启动，请查看控制台日志和页面效果');
        }

        // 智能景点图片加载函数
        function loadSceneryImage(imgElement, iconElement, container, imageUrl, sceneryName) {
            console.log(`开始加载${sceneryName}图片: ${imageUrl}`);

            // 设置加载状态
            container.classList.add('loading');
            container.classList.remove('loaded', 'error');

            // 显示图标作为占位符
            if (iconElement) {
                iconElement.style.display = 'flex';
            }
            imgElement.style.display = 'none';

            // 创建新的图片对象进行预加载
            const tempImg = new Image();

            tempImg.onload = function() {
                console.log(`✅ ${sceneryName}图片加载成功`);

                // 图片加载成功，显示图片，隐藏图标
                imgElement.src = imageUrl;
                imgElement.style.display = 'block';

                if (iconElement) {
                    iconElement.style.display = 'none';
                }

                container.classList.remove('loading');
                container.classList.add('loaded');

                // 添加淡入效果
                imgElement.style.opacity = '0';
                setTimeout(() => {
                    imgElement.style.opacity = '1';
                }, 50);
            };

            tempImg.onerror = function() {
                console.log(`❌ ${sceneryName}图片加载失败，使用备用方案`);

                // 图片加载失败，尝试备用图片源
                const fallbackUrls = getFallbackImageUrls(sceneryName);
                tryFallbackImages(imgElement, iconElement, container, fallbackUrls, sceneryName, 0);
            };

            // 设置超时处理
            setTimeout(() => {
                if (container.classList.contains('loading')) {
                    console.log(`⏰ ${sceneryName}图片加载超时，使用图标`);
                    tempImg.onerror();
                }
            }, 5000);

            // 开始加载图片
            tempImg.src = imageUrl;
        }

        // 获取备用图片URL
        function getFallbackImageUrls(sceneryName) {
            const fallbackMap = {
                // 重庆景点
                '大足石刻': [
                    'https://source.unsplash.com/400x300/?stone,carving,buddha',
                    'https://source.unsplash.com/400x300/?sculpture,ancient,art'
                ],
                '武隆天生三桥': [
                    'https://source.unsplash.com/400x300/?natural,bridge,canyon',
                    'https://source.unsplash.com/400x300/?karst,landscape,rock'
                ],
                '合川钓鱼城': [
                    'https://source.unsplash.com/400x300/?ancient,fortress,castle',
                    'https://source.unsplash.com/400x300/?historical,architecture,china'
                ],

                // 成都景点
                '都江堰': [
                    'https://source.unsplash.com/400x300/?irrigation,water,ancient',
                    'https://source.unsplash.com/400x300/?engineering,river,heritage'
                ],
                '青城山': [
                    'https://source.unsplash.com/400x300/?mountain,temple,forest',
                    'https://source.unsplash.com/400x300/?taoist,nature,green'
                ],
                '峨眉山': [
                    'https://source.unsplash.com/400x300/?buddhist,temple,mountain',
                    'https://source.unsplash.com/400x300/?golden,summit,clouds'
                ],

                // 北京景点
                '承德避暑山庄': [
                    'https://source.unsplash.com/400x300/?imperial,palace,garden',
                    'https://source.unsplash.com/400x300/?chinese,architecture,royal'
                ],
                '天津海河': [
                    'https://source.unsplash.com/400x300/?river,city,night',
                    'https://source.unsplash.com/400x300/?urban,waterfront,lights'
                ],
                '秦皇岛北戴河': [
                    'https://source.unsplash.com/400x300/?beach,seaside,china',
                    'https://source.unsplash.com/400x300/?coastal,resort,sand'
                ],

                // 上海景点
                '苏州园林': [
                    'https://source.unsplash.com/400x300/?classical,garden,pavilion',
                    'https://source.unsplash.com/400x300/?chinese,traditional,pond'
                ],
                '杭州西湖': [
                    'https://source.unsplash.com/400x300/?lake,pagoda,willow',
                    'https://source.unsplash.com/400x300/?hangzhou,scenic,water'
                ],
                '南京夫子庙': [
                    'https://source.unsplash.com/400x300/?confucius,temple,traditional',
                    'https://source.unsplash.com/400x300/?ancient,architecture,nanjing'
                ],

                // 国内游
                '哈尔滨冰雪大世界': [
                    'https://source.unsplash.com/400x300/?ice,sculpture,winter',
                    'https://source.unsplash.com/400x300/?snow,festival,art'
                ],
                '海南三亚': [
                    'https://source.unsplash.com/400x300/?tropical,beach,palm',
                    'https://source.unsplash.com/400x300/?hainan,resort,ocean'
                ],
                '云南大理': [
                    'https://source.unsplash.com/400x300/?ancient,town,yunnan',
                    'https://source.unsplash.com/400x300/?dali,lake,mountain'
                ],
                '青海湖': [
                    'https://source.unsplash.com/400x300/?plateau,lake,flower',
                    'https://source.unsplash.com/400x300/?qinghai,rapeseed,yellow'
                ],
                '内蒙古草原': [
                    'https://source.unsplash.com/400x300/?grassland,horse,prairie',
                    'https://source.unsplash.com/400x300/?mongolia,vast,green'
                ],
                '新疆伊犁': [
                    'https://source.unsplash.com/400x300/?lavender,field,purple',
                    'https://source.unsplash.com/400x300/?xinjiang,mountain,flower'
                ],

                // 出境游
                '日本北海道': [
                    'https://source.unsplash.com/400x300/?hokkaido,snow,skiing',
                    'https://source.unsplash.com/400x300/?japan,winter,onsen'
                ],
                '泰国清迈': [
                    'https://source.unsplash.com/400x300/?thailand,temple,buddhist',
                    'https://source.unsplash.com/400x300/?chiang,mai,golden'
                ],
                '新西兰南岛': [
                    'https://source.unsplash.com/400x300/?new,zealand,mountain',
                    'https://source.unsplash.com/400x300/?queenstown,lake,nature'
                ],
                '法国普罗旺斯': [
                    'https://source.unsplash.com/400x300/?provence,lavender,france',
                    'https://source.unsplash.com/400x300/?purple,field,romantic'
                ],
                '挪威峡湾': [
                    'https://source.unsplash.com/400x300/?norway,fjord,waterfall',
                    'https://source.unsplash.com/400x300/?bergen,mountain,scenic'
                ],
                '土耳其卡帕多奇亚': [
                    'https://source.unsplash.com/400x300/?cappadocia,balloon,turkey',
                    'https://source.unsplash.com/400x300/?hot,air,landscape'
                ]
            };

            return fallbackMap[sceneryName] || [
                'https://source.unsplash.com/400x300/?landscape,travel,scenic',
                'https://source.unsplash.com/400x300/?nature,tourism,beautiful'
            ];
        }

        // 尝试备用图片
        function tryFallbackImages(imgElement, iconElement, container, fallbackUrls, sceneryName, index) {
            if (index >= fallbackUrls.length) {
                // 所有备用图片都失败，使用图标
                console.log(`🎨 ${sceneryName}所有图片都失败，使用图标显示`);
                container.classList.remove('loading');
                container.classList.add('error');

                if (iconElement) {
                    iconElement.style.display = 'flex';
                }
                imgElement.style.display = 'none';
                return;
            }

            const fallbackUrl = fallbackUrls[index];
            console.log(`🔄 尝试${sceneryName}备用图片 ${index + 1}: ${fallbackUrl}`);

            const tempImg = new Image();

            tempImg.onload = function() {
                console.log(`✅ ${sceneryName}备用图片加载成功`);

                imgElement.src = fallbackUrl;
                imgElement.style.display = 'block';

                if (iconElement) {
                    iconElement.style.display = 'none';
                }

                container.classList.remove('loading');
                container.classList.add('loaded');

                imgElement.style.opacity = '0';
                setTimeout(() => {
                    imgElement.style.opacity = '1';
                }, 50);
            };

            tempImg.onerror = function() {
                console.log(`❌ ${sceneryName}备用图片 ${index + 1} 失败`);
                tryFallbackImages(imgElement, iconElement, container, fallbackUrls, sceneryName, index + 1);
            };

            tempImg.src = fallbackUrl;
        }

        // 显示机票价格信息
        function displayFlightPrice(category, flightPrice) {
            console.log(`显示${category}机票价格:`, flightPrice);

            // 查找或创建机票价格容器
            const infoContainer = document.querySelector(`#${category}Card .travel-info`);
            if (!infoContainer) return;

            // 移除之前的机票价格信息
            const existingPrice = infoContainer.querySelector('.flight-price');
            if (existingPrice) {
                existingPrice.remove();
            }

            // 创建机票价格元素
            const priceElement = document.createElement('div');
            priceElement.className = 'flight-price';

            // 格式化日期
            const cheapestDate = new Date(flightPrice.cheapest_date);
            const dateStr = cheapestDate.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric'
            });

            // 计算节省金额
            const savings = flightPrice.savings;
            const savingsText = savings > 0 ? `节省 ¥${savings}` : '价格稳定';

            // 价格趋势
            const trendClass = flightPrice.price_trend === 'stable' ? 'trend-stable' : 'trend-volatile';
            const trendText = flightPrice.price_trend === 'stable' ? '价格稳定' : '价格波动';

            priceElement.innerHTML = `
                <h6>✈️ ${flightPrice.origin} → ${flightPrice.destination}</h6>
                <div class="price-main">
                    <div>
                        <div class="cheapest-price">¥${flightPrice.cheapest_price}</div>
                        <div class="price-date">${dateStr} 最便宜</div>
                    </div>
                    <div class="price-trend ${trendClass}">${trendText}</div>
                </div>
                <div class="price-details">
                    <span>平均价格: ¥${flightPrice.average_price}</span>
                    <span class="price-savings">${savingsText}</span>
                </div>
            `;

            // 添加到信息容器
            infoContainer.appendChild(priceElement);

            // 添加淡入动画
            setTimeout(() => {
                priceElement.style.opacity = '0';
                priceElement.style.transform = 'translateY(10px)';
                priceElement.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

                setTimeout(() => {
                    priceElement.style.opacity = '1';
                    priceElement.style.transform = 'translateY(0)';
                }, 50);
            }, 200);
        }

        // 刷新旅行推荐
        function refreshTravel(category) {
            if (!currentTravelData || !currentTravelData[category]) return;

            const data = currentTravelData[category];

            // 切换到下一个推荐
            currentTravelIndices[category] = (currentTravelIndices[category] + 1) % data.length;

            // 添加刷新动画
            const card = document.getElementById(`${category}Card`);
            card.style.transform = 'scale(0.95)';
            card.style.opacity = '0.7';

            setTimeout(() => {
                displayTravelCategory(category, data);
            }, 200);

            console.log(`刷新${category}推荐:`, data[currentTravelIndices[category]].name);
        }
    </script>
</body>
</html>
