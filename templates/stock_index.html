<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美股分析平台 - 股价波动与新闻</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@3"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .search-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .search-container {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 250px;
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 1em;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #667eea;
        }

        .search-btn, .trending-btn {
            padding: 12px 25px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s ease;
        }

        .search-btn:hover, .trending-btn:hover {
            background: #5a6fd8;
        }

        .popular-stocks {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        .stock-tag {
            padding: 6px 12px;
            background: #f0f0f0;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }

        .stock-tag:hover {
            background: #667eea;
            color: white;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 30px;
        }

        .chart-with-news {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
        }

        .news-detail-sidebar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            height: fit-content;
            max-height: 600px;
            overflow-y: auto;
            display: none;
        }

        .news-detail-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .news-detail-content {
            display: none;
        }

        .news-detail-content.active {
            display: block;
        }

        .news-item-detail {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }

        .news-item-detail.positive {
            border-left-color: #28a745;
            background: #f8fff9;
        }

        .news-item-detail.negative {
            border-left-color: #dc3545;
            background: #fff8f8;
        }

        .news-item-detail.neutral {
            border-left-color: #6c757d;
            background: #f8f9fa;
        }

        .news-detail-headline {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .news-detail-summary {
            color: #666;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .news-detail-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
            color: #999;
            margin-bottom: 10px;
        }

        .news-sentiment-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .sentiment-positive {
            background: #d4edda;
            color: #155724;
        }

        .sentiment-negative {
            background: #f8d7da;
            color: #721c24;
        }

        .sentiment-neutral {
            background: #e2e3e5;
            color: #383d41;
        }

        .close-news-detail {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            color: #999;
        }

        .close-news-detail:hover {
            color: #333;
        }

        .news-placeholder {
            text-align: center;
            color: #666;
            padding: 40px 20px;
        }

        .stock-detail {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            display: none;
        }

        .stock-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .stock-info h2 {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 5px;
        }

        .stock-symbol {
            color: #666;
            font-size: 1.1em;
        }

        .stock-price {
            text-align: right;
        }

        .current-price {
            font-size: 2.2em;
            font-weight: bold;
            color: #333;
        }

        .price-change {
            font-size: 1.1em;
            font-weight: 500;
        }

        .price-up {
            color: #28a745;
        }

        .price-down {
            color: #dc3545;
        }

        .price-neutral {
            color: #6c757d;
        }

        .stock-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .metric-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .metric-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }

        .chart-container {
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 25px;
            padding: 20px;
            position: relative;
        }

        .chart-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .chart-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }

        .chart-mode-selector {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 8px;
        }

        .chart-mode {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background 0.3s ease;
        }

        .chart-mode:hover {
            background: #dee2e6;
        }

        .chart-mode input[type="radio"] {
            margin-right: 8px;
            transform: scale(1.2);
            cursor: pointer;
        }

        .mode-label {
            font-size: 0.95em;
            font-weight: 500;
            color: #333;
            cursor: pointer;
        }

        .chart-checkboxes {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .combined-chart-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .chart-checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .chart-checkbox:hover {
            background: #f8f9fa;
        }

        .chart-checkbox input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
            cursor: pointer;
        }

        .checkbox-label {
            font-size: 0.95em;
            font-weight: 500;
            color: #333;
            cursor: pointer;
        }

        .chart-actions {
            display: flex;
            gap: 10px;
        }

        .chart-btn {
            padding: 6px 12px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.85em;
            transition: background 0.3s ease;
        }

        .chart-btn:hover {
            background: #5a6268;
        }

        .multi-chart-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            max-height: 600px;
            overflow-y: auto;
        }

        .chart-canvas {
            width: 100%;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            transition: height 0.3s ease;
        }

        /* 动态高度调整 */
        .chart-canvas.single {
            height: 400px;
        }

        .chart-canvas.dual {
            height: 280px;
        }

        .chart-canvas.triple {
            height: 180px;
        }

        /* 网格布局选项 */
        .multi-chart-container.grid-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto;
            gap: 15px;
            max-height: 500px;
        }

        .multi-chart-container.grid-layout .chart-canvas {
            height: 240px;
        }

        .multi-chart-container.grid-layout .chart-canvas.full-width {
            grid-column: 1 / -1;
            height: 200px;
        }

        .analysis-section {
            margin-bottom: 25px;
        }

        .analysis-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .recommendation-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .recommendation-text {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .recommendation-reasons {
            list-style: none;
        }

        .recommendation-reasons li {
            margin: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .recommendation-reasons li:before {
            content: "•";
            color: #667eea;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .news-sidebar {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            height: fit-content;
        }

        .news-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }

        .news-item {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .news-item:last-child {
            border-bottom: none;
        }

        .news-headline {
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.4;
            color: #333;
        }

        .news-summary {
            font-size: 0.9em;
            color: #666;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8em;
            color: #999;
        }

        .sentiment-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .sentiment-positive {
            background: #d4edda;
            color: #155724;
        }

        .sentiment-negative {
            background: #f8d7da;
            color: #721c24;
        }

        .sentiment-neutral {
            background: #e2e3e5;
            color: #383d41;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 40px;
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .search-container {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-input {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 美股分析平台</h1>
            <p>实时股价波动分析与相关新闻资讯</p>
        </div>

        <div class="search-section">
            <div class="search-container">
                <input type="text" class="search-input" id="stockSearch" placeholder="输入股票代码或公司名称 (如: AAPL, Apple)">
                <button class="search-btn" onclick="searchStock()">🔍 搜索</button>
                <button class="trending-btn" onclick="loadTrendingStocks()">📊 热门股票</button>
            </div>
            
            <div class="popular-stocks">
                <span style="color: #666; margin-right: 10px;">热门:</span>
                <div class="stock-tag" onclick="loadStock('AAPL')">AAPL</div>
                <div class="stock-tag" onclick="loadStock('MSFT')">MSFT</div>
                <div class="stock-tag" onclick="loadStock('GOOGL')">GOOGL</div>
                <div class="stock-tag" onclick="loadStock('AMZN')">AMZN</div>
                <div class="stock-tag" onclick="loadStock('TSLA')">TSLA</div>
                <div class="stock-tag" onclick="loadStock('META')">META</div>
                <div class="stock-tag" onclick="loadStock('NVDA')">NVDA</div>
            </div>
        </div>

        <div class="main-content">
            <div class="stock-detail" id="stockDetail">
                <div class="loading" id="loadingIndicator">
                    <div style="font-size: 2em; margin-bottom: 10px;">📊</div>
                    <div>正在加载股票数据...</div>
                </div>
            </div>

            <div class="news-sidebar" id="newsSidebar">
                <div class="news-title">📰 相关新闻</div>
                <div class="loading">
                    <div style="font-size: 1.5em; margin-bottom: 10px;">📰</div>
                    <div>选择股票查看相关新闻</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentCharts = {
            price: null,
            candlestick: null,
            volume: null,
            combined: null
        };
        let currentStockData = null;
        let currentNewsData = null;
        let isGridLayout = false;
        let chartMode = 'separate'; // 'separate' 或 'combined'
        let showNewsMarkers = false;

        // 页面加载完成后自动加载AAPL数据
        window.addEventListener('load', function() {
            loadStock('AAPL');
        });

        // 更新图表显示
        function updateCharts() {
            if (!currentStockData) return;

            const showPrice = document.getElementById('showPrice').checked;
            const showCandlestick = document.getElementById('showCandlestick').checked;
            const showVolume = document.getElementById('showVolume').checked;

            // 计算显示的图表数量
            const visibleCharts = [showPrice, showCandlestick, showVolume].filter(Boolean).length;

            // 显示/隐藏对应的canvas
            document.getElementById('priceChart').style.display = showPrice ? 'block' : 'none';
            document.getElementById('candlestickChart').style.display = showCandlestick ? 'block' : 'none';
            document.getElementById('volumeChart').style.display = showVolume ? 'block' : 'none';

            // 动态调整图表高度
            adjustChartHeights(visibleCharts);

            // 创建选中的图表
            if (showPrice) {
                createPriceChart();
            } else {
                destroyChart('price');
            }

            if (showCandlestick) {
                createCandlestickChart();
            } else {
                destroyChart('candlestick');
            }

            if (showVolume) {
                createVolumeChart();
            } else {
                destroyChart('volume');
            }
        }

        // 动态调整图表高度
        function adjustChartHeights(visibleCharts) {
            const charts = ['priceChart', 'candlestickChart', 'volumeChart'];

            charts.forEach(chartId => {
                const canvas = document.getElementById(chartId);
                // 清除所有高度类
                canvas.classList.remove('single', 'dual', 'triple');

                // 根据显示的图表数量添加对应的类
                if (visibleCharts === 1) {
                    canvas.classList.add('single');
                } else if (visibleCharts === 2) {
                    canvas.classList.add('dual');
                } else if (visibleCharts === 3) {
                    canvas.classList.add('triple');
                }
            });
        }

        // 切换布局模式
        function toggleLayout() {
            const container = document.querySelector('.multi-chart-container');
            const btn = document.getElementById('layoutBtn');

            isGridLayout = !isGridLayout;

            if (isGridLayout) {
                container.classList.add('grid-layout');
                btn.textContent = '垂直布局';

                // 在网格布局中，如果只有一个图表，让它占满宽度
                const visibleCharts = [
                    document.getElementById('showPrice').checked,
                    document.getElementById('showCandlestick').checked,
                    document.getElementById('showVolume').checked
                ].filter(Boolean).length;

                if (visibleCharts === 1) {
                    const charts = ['priceChart', 'candlestickChart', 'volumeChart'];
                    charts.forEach(chartId => {
                        const canvas = document.getElementById(chartId);
                        if (canvas.style.display === 'block') {
                            canvas.classList.add('full-width');
                        }
                    });
                }
            } else {
                container.classList.remove('grid-layout');
                btn.textContent = '网格布局';

                // 移除full-width类
                const charts = ['priceChart', 'candlestickChart', 'volumeChart'];
                charts.forEach(chartId => {
                    document.getElementById(chartId).classList.remove('full-width');
                });
            }

            // 重新创建图表以适应新布局
            setTimeout(() => {
                updateCharts();
            }, 100);
        }

        // 全选
        function selectAll() {
            document.getElementById('showPrice').checked = true;
            document.getElementById('showCandlestick').checked = true;
            document.getElementById('showVolume').checked = true;
            updateCharts();
        }

        // 清空
        function clearAll() {
            document.getElementById('showPrice').checked = false;
            document.getElementById('showCandlestick').checked = false;
            document.getElementById('showVolume').checked = false;
            updateCharts();
        }

        // 重置为默认
        function resetDefault() {
            document.getElementById('showPrice').checked = true;
            document.getElementById('showCandlestick').checked = false;
            document.getElementById('showVolume').checked = false;
            updateCharts();
        }

        // 销毁指定图表
        function destroyChart(chartType) {
            if (currentCharts[chartType]) {
                currentCharts[chartType].destroy();
                currentCharts[chartType] = null;
            }
        }

        // 切换图表模式
        function switchChartMode() {
            const selectedMode = document.querySelector('input[name="chartMode"]:checked').value;
            chartMode = selectedMode;

            const separateContainer = document.getElementById('separateChartContainer');
            const combinedContainer = document.getElementById('combinedChartContainer');
            const separateControls = document.getElementById('separateControls');
            const combinedControls = document.getElementById('combinedControls');

            if (chartMode === 'combined') {
                // 切换到组合模式
                separateContainer.style.display = 'none';
                combinedContainer.style.display = 'block';
                separateControls.style.display = 'none';
                combinedControls.style.display = 'flex';

                // 销毁分离图表
                destroyChart('price');
                destroyChart('candlestick');
                destroyChart('volume');

                // 创建组合图表
                updateCombinedChart();
            } else {
                // 切换到分离模式
                separateContainer.style.display = 'block';
                combinedContainer.style.display = 'none';
                separateControls.style.display = 'flex';
                combinedControls.style.display = 'none';

                // 销毁组合图表
                destroyChart('combined');

                // 创建分离图表
                updateCharts();
            }
        }

        // 更新组合图表
        function updateCombinedChart() {
            if (!currentStockData || chartMode !== 'combined') return;

            const showPrice = document.getElementById('combinedPrice').checked;
            const showCandlestick = document.getElementById('combinedCandlestick').checked;
            const showVolume = document.getElementById('combinedVolume').checked;

            createCombinedChart(showPrice, showCandlestick, showVolume);
        }

        // 切换新闻标注显示
        function toggleNewsMarkers() {
            showNewsMarkers = !showNewsMarkers;
            const btn = document.getElementById('newsBtn');

            if (showNewsMarkers) {
                btn.textContent = '隐藏新闻';
                btn.style.background = '#dc3545';
            } else {
                btn.textContent = '显示新闻';
                btn.style.background = '#6c757d';
            }

            // 重新创建图表以显示/隐藏新闻标注
            if (chartMode === 'combined') {
                updateCombinedChart();
            } else {
                updateCharts();
            }
        }

        // 搜索股票
        function searchStock() {
            const query = document.getElementById('stockSearch').value.trim();
            if (query) {
                loadStock(query.toUpperCase());
            }
        }

        // 回车键搜索
        document.getElementById('stockSearch').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchStock();
            }
        });

        // 加载股票数据
        async function loadStock(symbol) {
            const stockDetail = document.getElementById('stockDetail');
            const loadingIndicator = document.getElementById('loadingIndicator');

            // 显示加载状态
            stockDetail.style.display = 'block';
            stockDetail.innerHTML = `
                <div class="loading" id="loadingIndicator">
                    <div style="font-size: 2em; margin-bottom: 10px;">📊</div>
                    <div>正在加载 ${symbol} 数据...</div>
                </div>
            `;

            try {
                console.log(`正在请求股票数据: ${symbol}`);

                // 添加超时控制
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

                const response = await fetch(`/api/stock/${symbol}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                console.log(`API响应状态: ${response.status}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('股票数据获取成功:', data);

                if (data && data.stock_data) {
                    displayStockData(data);
                    displayNews(data.news);
                } else {
                    showError('返回的数据格式不正确');
                }
            } catch (error) {
                console.error('股票数据加载失败:', error);

                if (error.name === 'AbortError') {
                    showError('请求超时，请检查网络连接后重试');
                } else if (error.message.includes('Failed to fetch')) {
                    showError('无法连接到服务器，请确认服务器正在运行 (http://localhost:8080)');
                } else {
                    showError(`网络错误: ${error.message}`);
                }
            }
        }

        // 显示股票数据
        function displayStockData(data) {
            const stockData = data.stock_data;
            const technical = data.technical_analysis;
            const recommendation = data.investment_recommendation;
            const sentiment = data.sentiment_analysis;

            // 保存当前股票数据和新闻数据用于图表
            currentStockData = stockData;
            currentNewsData = data.news || [];

            const priceChangeClass = stockData.price_change >= 0 ? 'price-up' : 'price-down';
            const priceChangeSign = stockData.price_change >= 0 ? '+' : '';

            const stockDetail = document.getElementById('stockDetail');
            stockDetail.innerHTML = `
                <div class="stock-header">
                    <div class="stock-info">
                        <h2>${stockData.name}</h2>
                        <div class="stock-symbol">${stockData.symbol} • ${stockData.sector}</div>
                    </div>
                    <div class="stock-price">
                        <div class="current-price">$${stockData.current_price}</div>
                        <div class="price-change ${priceChangeClass}">
                            ${priceChangeSign}${stockData.price_change} (${priceChangeSign}${stockData.price_change_percent}%)
                        </div>
                    </div>
                </div>

                <div class="stock-metrics">
                    <div class="metric-card">
                        <div class="metric-label">成交量</div>
                        <div class="metric-value">${formatNumber(stockData.volume)}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">市值</div>
                        <div class="metric-value">${formatMarketCap(stockData.market_cap)}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">P/E比率</div>
                        <div class="metric-value">${stockData.pe_ratio || 'N/A'}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">52周高点</div>
                        <div class="metric-value">$${stockData.week_52_high}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">52周低点</div>
                        <div class="metric-value">$${stockData.week_52_low}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Beta</div>
                        <div class="metric-value">${stockData.beta || 'N/A'}</div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-controls">
                        <div class="chart-title">📊 图表显示选项</div>
                        <div class="chart-mode-selector">
                            <label class="chart-mode">
                                <input type="radio" name="chartMode" value="separate" checked onchange="switchChartMode()">
                                <span class="mode-label">📊 分离显示</span>
                            </label>
                            <label class="chart-mode">
                                <input type="radio" name="chartMode" value="combined" onchange="switchChartMode()">
                                <span class="mode-label">🔄 组合显示</span>
                            </label>
                        </div>

                        <div class="chart-checkboxes" id="separateControls">
                            <label class="chart-checkbox">
                                <input type="checkbox" id="showPrice" checked onchange="updateCharts()">
                                <span class="checkbox-label">📈 价格走势</span>
                            </label>
                            <label class="chart-checkbox">
                                <input type="checkbox" id="showCandlestick" onchange="updateCharts()">
                                <span class="checkbox-label">🕯️ K线图</span>
                            </label>
                            <label class="chart-checkbox">
                                <input type="checkbox" id="showVolume" onchange="updateCharts()">
                                <span class="checkbox-label">📊 成交量</span>
                            </label>
                        </div>

                        <div class="chart-checkboxes" id="combinedControls" style="display: none;">
                            <label class="chart-checkbox">
                                <input type="checkbox" id="combinedPrice" checked onchange="updateCombinedChart()">
                                <span class="checkbox-label">📈 价格线</span>
                            </label>
                            <label class="chart-checkbox">
                                <input type="checkbox" id="combinedCandlestick" onchange="updateCombinedChart()">
                                <span class="checkbox-label">🕯️ K线柱</span>
                            </label>
                            <label class="chart-checkbox">
                                <input type="checkbox" id="combinedVolume" checked onchange="updateCombinedChart()">
                                <span class="checkbox-label">📊 成交量</span>
                            </label>
                        </div>
                        <div class="chart-actions">
                            <button class="chart-btn" onclick="selectAll()">全选</button>
                            <button class="chart-btn" onclick="clearAll()">清空</button>
                            <button class="chart-btn" onclick="resetDefault()">默认</button>
                            <button class="chart-btn" onclick="toggleLayout()" id="layoutBtn">网格布局</button>
                            <button class="chart-btn" onclick="toggleNewsMarkers()" id="newsBtn">显示新闻</button>
                        </div>
                    </div>
                    <div class="chart-with-news">
                        <div class="chart-main-area">
                            <div class="multi-chart-container" id="separateChartContainer">
                                <canvas id="priceChart" class="chart-canvas" style="display: block;"></canvas>
                                <canvas id="candlestickChart" class="chart-canvas" style="display: none;"></canvas>
                                <canvas id="volumeChart" class="chart-canvas" style="display: none;"></canvas>
                            </div>

                            <div class="combined-chart-container" id="combinedChartContainer" style="display: none;">
                                <canvas id="combinedChart" class="chart-canvas single"></canvas>
                            </div>
                        </div>

                        <div class="news-detail-sidebar" id="newsDetailSidebar">
                            <div style="position: relative;">
                                <button class="close-news-detail" onclick="closeNewsDetail()">×</button>
                                <div class="news-detail-title">📰 新闻详情</div>
                            </div>
                            <div class="news-detail-content" id="newsDetailContent">
                                <div class="news-placeholder">
                                    <div style="font-size: 2em; margin-bottom: 10px;">📰</div>
                                    <div>点击图表上的新闻标注点查看详情</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analysis-section">
                    <div class="analysis-title">💡 投资建议</div>
                    <div class="recommendation-card" style="border-left-color: ${recommendation.color}">
                        <div class="recommendation-text" style="color: ${recommendation.color}">
                            ${recommendation.recommendation} (评分: ${recommendation.score}/100)
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>目标价:</strong> $${recommendation.target_price} | 
                            <strong>风险等级:</strong> ${recommendation.risk_level}
                        </div>
                        <ul class="recommendation-reasons">
                            ${recommendation.reasons.map(reason => `<li>${reason}</li>`).join('')}
                        </ul>
                    </div>
                </div>

                <div class="analysis-section">
                    <div class="analysis-title">📊 技术分析</div>
                    <div style="margin-bottom: 15px;">
                        <strong>综合信号:</strong> 
                        <span style="color: ${technical.overall_signal === 'bullish' ? '#28a745' : technical.overall_signal === 'bearish' ? '#dc3545' : '#6c757d'}">
                            ${technical.signal_text}
                        </span>
                    </div>
                    <div>${technical.summary}</div>
                </div>

                <div class="analysis-section">
                    <div class="analysis-title">📰 市场情绪</div>
                    <div>
                        <strong>整体情绪:</strong> 
                        <span class="sentiment-badge sentiment-${sentiment.overall_sentiment}">
                            ${sentiment.overall_sentiment === 'positive' ? '积极' : sentiment.overall_sentiment === 'negative' ? '消极' : '中性'}
                        </span>
                        (${sentiment.sentiment_score}/100)
                    </div>
                    <div style="margin-top: 10px; color: #666;">
                        ${sentiment.summary}
                    </div>
                </div>
            `;

            // 等待DOM更新后创建图表
            setTimeout(() => {
                if (chartMode === 'combined') {
                    updateCombinedChart();
                } else {
                    updateCharts();
                }
            }, 100);
        }

        // 显示新闻
        function displayNews(newsList) {
            const newsSidebar = document.getElementById('newsSidebar');
            
            if (!newsList || newsList.length === 0) {
                newsSidebar.innerHTML = `
                    <div class="news-title">📰 相关新闻</div>
                    <div class="loading">暂无相关新闻</div>
                `;
                return;
            }

            let newsHtml = '<div class="news-title">📰 相关新闻</div>';
            
            newsList.forEach(news => {
                newsHtml += `
                    <div class="news-item">
                        <div class="news-headline">${news.title}</div>
                        <div class="news-summary">${news.summary}</div>
                        <div class="news-meta">
                            <span>${news.source} • ${formatTime(news.published_time)}</span>
                            <span class="sentiment-badge sentiment-${news.sentiment}">
                                ${news.sentiment === 'positive' ? '积极' : news.sentiment === 'negative' ? '消极' : '中性'}
                            </span>
                        </div>
                    </div>
                `;
            });

            newsSidebar.innerHTML = newsHtml;
        }

        // 显示错误
        function showError(message) {
            const stockDetail = document.getElementById('stockDetail');
            stockDetail.innerHTML = `
                <div class="error">
                    <div style="font-size: 2em; margin-bottom: 10px;">❌</div>
                    <div>${message}</div>
                </div>
            `;
        }

        // 加载热门股票
        async function loadTrendingStocks() {
            try {
                console.log('正在获取热门股票...');

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000);

                const response = await fetch('/api/stocks/trending', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('热门股票数据:', data);

                if (data && data.trending_stocks && data.trending_stocks.length > 0) {
                    // 显示第一个热门股票
                    loadStock(data.trending_stocks[0].symbol);
                } else {
                    console.warn('没有热门股票数据');
                }
            } catch (error) {
                console.error('获取热门股票失败:', error);
                showError('获取热门股票失败，请稍后重试');
            }
        }

        // 格式化数字
        function formatNumber(num) {
            if (num >= 1000000000) {
                return (num / 1000000000).toFixed(1) + 'B';
            } else if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // 格式化市值
        function formatMarketCap(marketCap) {
            if (!marketCap) return 'N/A';
            return formatNumber(marketCap);
        }

        // 创建价格走势图
        function createPriceChart() {
            const chartCanvas = document.getElementById('priceChart');
            if (!chartCanvas || !currentStockData) return;

            try {
                const ctx = chartCanvas.getContext('2d');

                // 销毁现有图表
                destroyChart('price');

                const historicalData = currentStockData.historical_data || [];
                if (historicalData.length === 0) return;

                // 检查Chart.js是否已加载
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js库未加载');
                    return;
                }

                const labels = historicalData.map(item => item.date);
                const prices = historicalData.map(item => item.close);

                currentCharts.price = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: `${currentStockData.symbol} 收盘价`,
                            data: prices,
                            borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                            backgroundColor: currentStockData.price_change >= 0 ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.1,
                            pointRadius: 3,
                            pointHoverRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `${currentStockData.name} (${currentStockData.symbol}) - 价格走势`,
                                font: { size: 14, weight: 'bold' }
                            },
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            ...getNewsAnnotationConfig()
                        },
                        scales: {
                            x: {
                                type: 'time',
                                time: {
                                    unit: 'day',
                                    displayFormats: {
                                        day: 'MM/dd'
                                    }
                                },
                                title: {
                                    display: true,
                                    text: '日期'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: '价格 ($)'
                                },
                                beginAtZero: false
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        onClick: function(event, elements) {
                            handleChartClick(event, elements, this);
                        }
                    }
                });
            } catch (error) {
                console.error('创建价格图表时出错:', error);
            }
        }

        // 创建K线图
        function createCandlestickChart() {
            const chartCanvas = document.getElementById('candlestickChart');
            if (!chartCanvas || !currentStockData) return;

            try {
                const ctx = chartCanvas.getContext('2d');

                // 销毁现有图表
                destroyChart('candlestick');

                const historicalData = currentStockData.historical_data || [];
                if (historicalData.length === 0) return;

                // 检查Chart.js是否已加载
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js库未加载');
                    return;
                }

                const labels = historicalData.map(item => item.date);

                // 创建简化的K线图（使用柱状图模拟）
                currentCharts.candlestick = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '开盘价',
                            data: historicalData.map(item => item.open),
                            backgroundColor: 'rgba(54, 162, 235, 0.3)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1,
                            hidden: true
                        }, {
                            label: '收盘价',
                            data: historicalData.map(item => item.close),
                            backgroundColor: historicalData.map(item =>
                                item.close >= item.open ? 'rgba(40, 167, 69, 0.7)' : 'rgba(220, 53, 69, 0.7)'
                            ),
                            borderColor: historicalData.map(item =>
                                item.close >= item.open ? '#28a745' : '#dc3545'
                            ),
                            borderWidth: 1
                        }, {
                            label: '最高价',
                            data: historicalData.map(item => item.high),
                            type: 'line',
                            borderColor: '#17a2b8',
                            backgroundColor: 'rgba(23, 162, 184, 0.1)',
                            borderWidth: 1,
                            pointRadius: 2,
                            fill: false
                        }, {
                            label: '最低价',
                            data: historicalData.map(item => item.low),
                            type: 'line',
                            borderColor: '#ffc107',
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            borderWidth: 1,
                            pointRadius: 2,
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `${currentStockData.name} (${currentStockData.symbol}) - K线图 (OHLC)`,
                                font: { size: 14, weight: 'bold' }
                            },
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            ...getNewsAnnotationConfig()
                        },
                        scales: {
                            x: {
                                type: 'time',
                                time: {
                                    unit: 'day',
                                    displayFormats: {
                                        day: 'MM/dd'
                                    }
                                },
                                title: {
                                    display: true,
                                    text: '日期'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: '价格 ($)'
                                },
                                beginAtZero: false
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        onClick: function(event, elements) {
                            handleChartClick(event, elements, this);
                        }
                    }
                });
            } catch (error) {
                console.error('创建K线图时出错:', error);
            }
        }

        // 创建成交量图
        function createVolumeChart() {
            const chartCanvas = document.getElementById('volumeChart');
            if (!chartCanvas || !currentStockData) return;

            try {
                const ctx = chartCanvas.getContext('2d');

                // 销毁现有图表
                destroyChart('volume');

                const historicalData = currentStockData.historical_data || [];
                if (historicalData.length === 0) return;

                // 检查Chart.js是否已加载
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js库未加载');
                    return;
                }

                const labels = historicalData.map(item => item.date);
                const volumes = historicalData.map(item => item.volume);

                currentCharts.volume = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '成交量',
                            data: volumes,
                            backgroundColor: historicalData.map(item =>
                                item.close >= item.open ? 'rgba(40, 167, 69, 0.7)' : 'rgba(220, 53, 69, 0.7)'
                            ),
                            borderColor: historicalData.map(item =>
                                item.close >= item.open ? '#28a745' : '#dc3545'
                            ),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `${currentStockData.name} (${currentStockData.symbol}) - 成交量`,
                                font: { size: 14, weight: 'bold' }
                            },
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            ...getNewsAnnotationConfig()
                        },
                        scales: {
                            x: {
                                type: 'time',
                                time: {
                                    unit: 'day',
                                    displayFormats: {
                                        day: 'MM/dd'
                                    }
                                },
                                title: {
                                    display: true,
                                    text: '日期'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: '成交量'
                                },
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return formatNumber(value);
                                    }
                                }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        onClick: function(event, elements) {
                            handleChartClick(event, elements, this);
                        }
                    }
                });
            } catch (error) {
                console.error('创建成交量图时出错:', error);
            }
        }

        // 创建组合图表
        function createCombinedChart(showPrice, showCandlestick, showVolume) {
            const chartCanvas = document.getElementById('combinedChart');
            if (!chartCanvas || !currentStockData) return;

            try {
                const ctx = chartCanvas.getContext('2d');
                destroyChart('combined');

                const historicalData = currentStockData.historical_data || [];
                if (historicalData.length === 0) return;

                const labels = historicalData.map(item => item.date);
                const datasets = [];

                // 价格线数据集
                if (showPrice) {
                    datasets.push({
                        label: '收盘价',
                        data: historicalData.map(item => item.close),
                        type: 'line',
                        borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: 'transparent',
                        borderWidth: 3,
                        pointRadius: 2,
                        pointHoverRadius: 5,
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y'
                    });
                }

                // K线柱状图数据集
                if (showCandlestick) {
                    // 开盘价
                    datasets.push({
                        label: '开盘价',
                        data: historicalData.map(item => item.open),
                        type: 'bar',
                        backgroundColor: 'rgba(54, 162, 235, 0.3)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1,
                        barThickness: 8,
                        yAxisID: 'y',
                        hidden: true
                    });

                    // 收盘价柱状图
                    datasets.push({
                        label: 'K线收盘',
                        data: historicalData.map(item => item.close),
                        type: 'bar',
                        backgroundColor: historicalData.map(item =>
                            item.close >= item.open ? 'rgba(40, 167, 69, 0.6)' : 'rgba(220, 53, 69, 0.6)'
                        ),
                        borderColor: historicalData.map(item =>
                            item.close >= item.open ? '#28a745' : '#dc3545'
                        ),
                        borderWidth: 1,
                        barThickness: 6,
                        yAxisID: 'y'
                    });

                    // 最高价线
                    datasets.push({
                        label: '最高价',
                        data: historicalData.map(item => item.high),
                        type: 'line',
                        borderColor: '#17a2b8',
                        backgroundColor: 'transparent',
                        borderWidth: 1,
                        pointRadius: 1,
                        fill: false,
                        yAxisID: 'y'
                    });

                    // 最低价线
                    datasets.push({
                        label: '最低价',
                        data: historicalData.map(item => item.low),
                        type: 'line',
                        borderColor: '#ffc107',
                        backgroundColor: 'transparent',
                        borderWidth: 1,
                        pointRadius: 1,
                        fill: false,
                        yAxisID: 'y'
                    });
                }

                // 成交量数据集
                if (showVolume) {
                    datasets.push({
                        label: '成交量',
                        data: historicalData.map(item => item.volume),
                        type: 'bar',
                        backgroundColor: historicalData.map(item =>
                            item.close >= item.open ? 'rgba(40, 167, 69, 0.4)' : 'rgba(220, 53, 69, 0.4)'
                        ),
                        borderColor: historicalData.map(item =>
                            item.close >= item.open ? '#28a745' : '#dc3545'
                        ),
                        borderWidth: 1,
                        barThickness: 4,
                        yAxisID: 'y1'
                    });
                }

                currentCharts.combined = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `${currentStockData.name} (${currentStockData.symbol}) - 组合图表`,
                                font: { size: 16, weight: 'bold' }
                            },
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    filter: function(legendItem, chartData) {
                                        // 隐藏开盘价图例
                                        return legendItem.text !== '开盘价';
                                    }
                                }
                            },
                            ...getNewsAnnotationConfig()
                        },
                        scales: {
                            x: {
                                type: 'time',
                                time: {
                                    unit: 'day',
                                    displayFormats: {
                                        day: 'MM/dd'
                                    }
                                },
                                title: {
                                    display: true,
                                    text: '日期'
                                }
                            },
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: {
                                    display: true,
                                    text: '价格 ($)'
                                },
                                beginAtZero: false
                            },
                            y1: {
                                type: 'linear',
                                display: showVolume,
                                position: 'right',
                                title: {
                                    display: true,
                                    text: '成交量'
                                },
                                beginAtZero: true,
                                grid: {
                                    drawOnChartArea: false
                                },
                                ticks: {
                                    callback: function(value) {
                                        return formatNumber(value);
                                    }
                                }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        onClick: function(event, elements) {
                            handleChartClick(event, elements, this);
                        }
                    }
                });
            } catch (error) {
                console.error('创建组合图表时出错:', error);
            }
        }

        // 处理新闻标注
        function getNewsAnnotations() {
            if (!showNewsMarkers || !currentNewsData || currentNewsData.length === 0) {
                return [];
            }

            const annotations = [];
            const historicalData = currentStockData.historical_data || [];

            currentNewsData.forEach((news, index) => {
                const newsDate = new Date(news.published_time).toISOString().split('T')[0];

                // 查找对应的历史数据点
                const dataPoint = historicalData.find(item => item.date === newsDate);

                if (dataPoint) {
                    // 根据新闻情绪确定颜色和图标
                    let color = '#6c757d'; // 默认灰色
                    let emoji = '📰';
                    if (news.sentiment === 'positive') {
                        color = '#28a745'; // 绿色
                        emoji = '📈';
                    } else if (news.sentiment === 'negative') {
                        color = '#dc3545'; // 红色
                        emoji = '📉';
                    }

                    // 添加垂直线标注
                    annotations.push({
                        type: 'line',
                        mode: 'vertical',
                        scaleID: 'x',
                        value: newsDate,
                        borderColor: color,
                        borderWidth: 2,
                        borderDash: [5, 5],
                        label: {
                            enabled: true,
                            content: `${emoji} ${news.title.substring(0, 15)}...`,
                            position: 'top',
                            backgroundColor: color,
                            color: 'white',
                            font: {
                                size: 10,
                                weight: 'bold'
                            },
                            padding: 4,
                            cornerRadius: 4
                        }
                    });

                    // 添加可点击的圆点标注
                    annotations.push({
                        id: `news-point-${index}`,
                        type: 'point',
                        scaleID: 'x',
                        value: newsDate,
                        yValue: dataPoint.close,
                        backgroundColor: color,
                        borderColor: 'white',
                        borderWidth: 4,
                        radius: 15,
                        pointStyle: 'circle',
                        label: {
                            enabled: false
                        },
                        // 添加点击事件标识
                        newsIndex: index,
                        clickable: true
                    });
                }
            });

            return annotations;
        }

        // 获取新闻标注的通用配置
        function getNewsAnnotationConfig() {
            if (!showNewsMarkers) {
                return {};
            }

            return {
                annotation: {
                    annotations: getNewsAnnotations()
                }
            };
        }

        // 显示新闻详情
        function showNewsDetail(newsIndex) {
            const sidebar = document.getElementById('newsDetailSidebar');
            const content = document.getElementById('newsDetailContent');

            if (!currentNewsData || newsIndex >= currentNewsData.length) {
                return;
            }

            const news = currentNewsData[newsIndex];
            const sentimentClass = news.sentiment || 'neutral';
            const sentimentText = {
                'positive': '积极',
                'negative': '消极',
                'neutral': '中性'
            }[sentimentClass] || '中性';

            const sentimentEmoji = {
                'positive': '📈',
                'negative': '📉',
                'neutral': '📰'
            }[sentimentClass] || '📰';

            content.innerHTML = `
                <div class="news-item-detail ${sentimentClass}">
                    <div class="news-detail-headline">
                        ${sentimentEmoji} ${news.title}
                    </div>
                    <div class="news-detail-summary">
                        ${news.summary || '暂无摘要'}
                    </div>
                    <div class="news-detail-meta">
                        <span>${news.source} • ${formatTime(news.published_time)}</span>
                        <span class="news-sentiment-badge sentiment-${sentimentClass}">
                            ${sentimentText}
                        </span>
                    </div>
                    ${news.url ? `<div style="margin-top: 10px;">
                        <a href="${news.url}" target="_blank" style="color: #007bff; text-decoration: none;">
                            🔗 查看原文
                        </a>
                    </div>` : ''}
                </div>
            `;

            sidebar.style.display = 'block';
            content.classList.add('active');
        }

        // 关闭新闻详情
        function closeNewsDetail() {
            const sidebar = document.getElementById('newsDetailSidebar');
            const content = document.getElementById('newsDetailContent');

            sidebar.style.display = 'none';
            content.classList.remove('active');
        }

        // 格式化时间
        function formatTime(timeString) {
            const date = new Date(timeString);
            const now = new Date();
            const diffHours = Math.floor((now - date) / (1000 * 60 * 60));

            if (diffHours < 1) {
                return '刚刚';
            } else if (diffHours < 24) {
                return `${diffHours}小时前`;
            } else {
                const diffDays = Math.floor(diffHours / 24);
                if (diffDays < 7) {
                    return `${diffDays}天前`;
                } else {
                    return date.toLocaleDateString('zh-CN');
                }
            }
        }

        // 处理图表点击事件
        function handleChartClick(event, elements, chart) {
            if (!showNewsMarkers || !currentNewsData) {
                console.log('新闻标注未启用或无新闻数据');
                return;
            }

            console.log('图表被点击');

            // 获取点击位置
            const canvasPosition = Chart.helpers.getRelativePosition(event, chart);
            console.log('点击位置:', canvasPosition);

            // 检查是否有新闻在这个日期
            const historicalData = currentStockData.historical_data || [];
            const clickThreshold = 25; // 点击阈值（像素）
            let foundNews = false;

            currentNewsData.forEach((news, index) => {
                const newsDate = new Date(news.published_time).toISOString().split('T')[0];
                const dataPoint = historicalData.find(item => item.date === newsDate);

                if (dataPoint) {
                    const annotationX = chart.scales.x.getPixelForValue(newsDate);
                    // 根据图表类型选择正确的Y轴
                    let annotationY;
                    if (chart.scales.y) {
                        annotationY = chart.scales.y.getPixelForValue(dataPoint.close);
                    } else if (chart.scales['y-axis-0']) {
                        annotationY = chart.scales['y-axis-0'].getPixelForValue(dataPoint.close);
                    } else {
                        // 如果找不到Y轴，跳过这个新闻点
                        return;
                    }

                    const distance = Math.sqrt(
                        Math.pow(canvasPosition.x - annotationX, 2) +
                        Math.pow(canvasPosition.y - annotationY, 2)
                    );

                    console.log(`新闻 ${index}: 位置(${annotationX.toFixed(0)}, ${annotationY.toFixed(0)}), 距离: ${distance.toFixed(1)}`);

                    if (distance <= clickThreshold) {
                        console.log(`✅ 点击了新闻 ${index}: ${news.title}`);
                        showNewsDetail(index);
                        foundNews = true;
                        return;
                    }
                }
            });

            if (!foundNews) {
                console.log('❌ 没有点击到任何新闻标注点');
            }
        }





        // 格式化时间
        function formatTime(timeString) {
            const date = new Date(timeString);
            const now = new Date();
            const diffHours = Math.floor((now - date) / (1000 * 60 * 60));

            if (diffHours < 1) {
                return '刚刚';
            } else if (diffHours < 24) {
                return `${diffHours}小时前`;
            } else {
                return `${Math.floor(diffHours / 24)}天前`;
            }
        }
    </script>
</body>
</html>
