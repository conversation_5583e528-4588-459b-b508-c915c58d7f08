<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美股分析平台 - 股价波动与新闻</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .search-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .search-container {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 250px;
            padding: 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 1em;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #667eea;
        }

        .search-btn, .trending-btn {
            padding: 12px 25px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s ease;
        }

        .search-btn:hover, .trending-btn:hover {
            background: #5a6fd8;
        }

        .popular-stocks {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        .stock-tag {
            padding: 6px 12px;
            background: #f0f0f0;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }

        .stock-tag:hover {
            background: #667eea;
            color: white;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 30px;
        }

        .stock-detail {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            display: none;
        }

        .stock-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .stock-info h2 {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 5px;
        }

        .stock-symbol {
            color: #666;
            font-size: 1.1em;
        }

        .stock-price {
            text-align: right;
        }

        .current-price {
            font-size: 2.2em;
            font-weight: bold;
            color: #333;
        }

        .price-change {
            font-size: 1.1em;
            font-weight: 500;
        }

        .price-up {
            color: #28a745;
        }

        .price-down {
            color: #dc3545;
        }

        .price-neutral {
            color: #6c757d;
        }

        .stock-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .metric-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .metric-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }

        .chart-container {
            height: 400px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 25px;
            padding: 20px;
            position: relative;
        }

        .chart-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .chart-tab {
            padding: 8px 16px;
            background: #e9ecef;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .chart-tab.active {
            background: #007bff;
            color: white;
        }

        .chart-tab:hover {
            background: #007bff;
            color: white;
        }

        .chart-canvas {
            height: 320px;
            width: 100%;
        }

        .analysis-section {
            margin-bottom: 25px;
        }

        .analysis-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .recommendation-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .recommendation-text {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .recommendation-reasons {
            list-style: none;
        }

        .recommendation-reasons li {
            margin: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .recommendation-reasons li:before {
            content: "•";
            color: #667eea;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .news-sidebar {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            height: fit-content;
        }

        .news-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }

        .news-item {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .news-item:last-child {
            border-bottom: none;
        }

        .news-headline {
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.4;
            color: #333;
        }

        .news-summary {
            font-size: 0.9em;
            color: #666;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8em;
            color: #999;
        }

        .sentiment-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .sentiment-positive {
            background: #d4edda;
            color: #155724;
        }

        .sentiment-negative {
            background: #f8d7da;
            color: #721c24;
        }

        .sentiment-neutral {
            background: #e2e3e5;
            color: #383d41;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 40px;
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .search-container {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-input {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 美股分析平台</h1>
            <p>实时股价波动分析与相关新闻资讯</p>
        </div>

        <div class="search-section">
            <div class="search-container">
                <input type="text" class="search-input" id="stockSearch" placeholder="输入股票代码或公司名称 (如: AAPL, Apple)">
                <button class="search-btn" onclick="searchStock()">🔍 搜索</button>
                <button class="trending-btn" onclick="loadTrendingStocks()">📊 热门股票</button>
            </div>
            
            <div class="popular-stocks">
                <span style="color: #666; margin-right: 10px;">热门:</span>
                <div class="stock-tag" onclick="loadStock('AAPL')">AAPL</div>
                <div class="stock-tag" onclick="loadStock('MSFT')">MSFT</div>
                <div class="stock-tag" onclick="loadStock('GOOGL')">GOOGL</div>
                <div class="stock-tag" onclick="loadStock('AMZN')">AMZN</div>
                <div class="stock-tag" onclick="loadStock('TSLA')">TSLA</div>
                <div class="stock-tag" onclick="loadStock('META')">META</div>
                <div class="stock-tag" onclick="loadStock('NVDA')">NVDA</div>
            </div>
        </div>

        <div class="main-content">
            <div class="stock-detail" id="stockDetail">
                <div class="loading" id="loadingIndicator">
                    <div style="font-size: 2em; margin-bottom: 10px;">📊</div>
                    <div>正在加载股票数据...</div>
                </div>
            </div>

            <div class="news-sidebar" id="newsSidebar">
                <div class="news-title">📰 相关新闻</div>
                <div class="loading">
                    <div style="font-size: 1.5em; margin-bottom: 10px;">📰</div>
                    <div>选择股票查看相关新闻</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentChart = null;
        let currentStockData = null;
        let currentChartType = 'line';

        // 页面加载完成后自动加载AAPL数据
        window.addEventListener('load', function() {
            loadStock('AAPL');
        });

        // 切换图表类型
        function switchChart(chartType) {
            currentChartType = chartType;

            // 更新标签状态
            document.querySelectorAll('.chart-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 重新绘制图表
            if (currentStockData) {
                createChart(currentStockData, chartType);
            }
        }

        // 搜索股票
        function searchStock() {
            const query = document.getElementById('stockSearch').value.trim();
            if (query) {
                loadStock(query.toUpperCase());
            }
        }

        // 回车键搜索
        document.getElementById('stockSearch').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchStock();
            }
        });

        // 加载股票数据
        async function loadStock(symbol) {
            const stockDetail = document.getElementById('stockDetail');
            const loadingIndicator = document.getElementById('loadingIndicator');
            
            // 显示加载状态
            stockDetail.style.display = 'block';
            stockDetail.innerHTML = `
                <div class="loading" id="loadingIndicator">
                    <div style="font-size: 2em; margin-bottom: 10px;">📊</div>
                    <div>正在加载 ${symbol} 数据...</div>
                </div>
            `;

            try {
                const response = await fetch(`/api/stock/${symbol}`);
                const data = await response.json();

                if (response.ok) {
                    displayStockData(data);
                    displayNews(data.news);
                } else {
                    showError(data.error || '获取股票数据失败');
                }
            } catch (error) {
                showError('网络错误，请稍后重试');
                console.error('Error:', error);
            }
        }

        // 显示股票数据
        function displayStockData(data) {
            const stockData = data.stock_data;
            const technical = data.technical_analysis;
            const recommendation = data.investment_recommendation;
            const sentiment = data.sentiment_analysis;

            // 保存当前股票数据用于图表
            currentStockData = stockData;

            const priceChangeClass = stockData.price_change >= 0 ? 'price-up' : 'price-down';
            const priceChangeSign = stockData.price_change >= 0 ? '+' : '';

            // 创建图表
            createChart(stockData, currentChartType);

            const stockDetail = document.getElementById('stockDetail');
            stockDetail.innerHTML = `
                <div class="stock-header">
                    <div class="stock-info">
                        <h2>${stockData.name}</h2>
                        <div class="stock-symbol">${stockData.symbol} • ${stockData.sector}</div>
                    </div>
                    <div class="stock-price">
                        <div class="current-price">$${stockData.current_price}</div>
                        <div class="price-change ${priceChangeClass}">
                            ${priceChangeSign}${stockData.price_change} (${priceChangeSign}${stockData.price_change_percent}%)
                        </div>
                    </div>
                </div>

                <div class="stock-metrics">
                    <div class="metric-card">
                        <div class="metric-label">成交量</div>
                        <div class="metric-value">${formatNumber(stockData.volume)}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">市值</div>
                        <div class="metric-value">${formatMarketCap(stockData.market_cap)}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">P/E比率</div>
                        <div class="metric-value">${stockData.pe_ratio || 'N/A'}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">52周高点</div>
                        <div class="metric-value">$${stockData.week_52_high}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">52周低点</div>
                        <div class="metric-value">$${stockData.week_52_low}</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Beta</div>
                        <div class="metric-value">${stockData.beta || 'N/A'}</div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-tabs">
                        <button class="chart-tab active" onclick="switchChart('line')">📈 价格走势</button>
                        <button class="chart-tab" onclick="switchChart('candlestick')">🕯️ K线图</button>
                        <button class="chart-tab" onclick="switchChart('volume')">📊 成交量</button>
                    </div>
                    <canvas id="stockChart" class="chart-canvas"></canvas>
                </div>

                <div class="analysis-section">
                    <div class="analysis-title">💡 投资建议</div>
                    <div class="recommendation-card" style="border-left-color: ${recommendation.color}">
                        <div class="recommendation-text" style="color: ${recommendation.color}">
                            ${recommendation.recommendation} (评分: ${recommendation.score}/100)
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>目标价:</strong> $${recommendation.target_price} | 
                            <strong>风险等级:</strong> ${recommendation.risk_level}
                        </div>
                        <ul class="recommendation-reasons">
                            ${recommendation.reasons.map(reason => `<li>${reason}</li>`).join('')}
                        </ul>
                    </div>
                </div>

                <div class="analysis-section">
                    <div class="analysis-title">📊 技术分析</div>
                    <div style="margin-bottom: 15px;">
                        <strong>综合信号:</strong> 
                        <span style="color: ${technical.overall_signal === 'bullish' ? '#28a745' : technical.overall_signal === 'bearish' ? '#dc3545' : '#6c757d'}">
                            ${technical.signal_text}
                        </span>
                    </div>
                    <div>${technical.summary}</div>
                </div>

                <div class="analysis-section">
                    <div class="analysis-title">📰 市场情绪</div>
                    <div>
                        <strong>整体情绪:</strong> 
                        <span class="sentiment-badge sentiment-${sentiment.overall_sentiment}">
                            ${sentiment.overall_sentiment === 'positive' ? '积极' : sentiment.overall_sentiment === 'negative' ? '消极' : '中性'}
                        </span>
                        (${sentiment.sentiment_score}/100)
                    </div>
                    <div style="margin-top: 10px; color: #666;">
                        ${sentiment.summary}
                    </div>
                </div>
            `;
        }

        // 显示新闻
        function displayNews(newsList) {
            const newsSidebar = document.getElementById('newsSidebar');
            
            if (!newsList || newsList.length === 0) {
                newsSidebar.innerHTML = `
                    <div class="news-title">📰 相关新闻</div>
                    <div class="loading">暂无相关新闻</div>
                `;
                return;
            }

            let newsHtml = '<div class="news-title">📰 相关新闻</div>';
            
            newsList.forEach(news => {
                newsHtml += `
                    <div class="news-item">
                        <div class="news-headline">${news.title}</div>
                        <div class="news-summary">${news.summary}</div>
                        <div class="news-meta">
                            <span>${news.source} • ${formatTime(news.published_time)}</span>
                            <span class="sentiment-badge sentiment-${news.sentiment}">
                                ${news.sentiment === 'positive' ? '积极' : news.sentiment === 'negative' ? '消极' : '中性'}
                            </span>
                        </div>
                    </div>
                `;
            });

            newsSidebar.innerHTML = newsHtml;
        }

        // 显示错误
        function showError(message) {
            const stockDetail = document.getElementById('stockDetail');
            stockDetail.innerHTML = `
                <div class="error">
                    <div style="font-size: 2em; margin-bottom: 10px;">❌</div>
                    <div>${message}</div>
                </div>
            `;
        }

        // 加载热门股票
        async function loadTrendingStocks() {
            try {
                const response = await fetch('/api/stocks/trending');
                const data = await response.json();
                
                if (response.ok && data.trending_stocks) {
                    // 显示第一个热门股票
                    if (data.trending_stocks.length > 0) {
                        loadStock(data.trending_stocks[0].symbol);
                    }
                }
            } catch (error) {
                console.error('获取热门股票失败:', error);
            }
        }

        // 格式化数字
        function formatNumber(num) {
            if (num >= 1000000000) {
                return (num / 1000000000).toFixed(1) + 'B';
            } else if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // 格式化市值
        function formatMarketCap(marketCap) {
            if (!marketCap) return 'N/A';
            return formatNumber(marketCap);
        }

        // 创建股票图表
        function createChart(stockData, chartType) {
            const ctx = document.getElementById('stockChart').getContext('2d');

            // 销毁现有图表
            if (currentChart) {
                currentChart.destroy();
            }

            const historicalData = stockData.historical_data || [];

            if (chartType === 'line') {
                createLineChart(ctx, historicalData, stockData);
            } else if (chartType === 'candlestick') {
                createCandlestickChart(ctx, historicalData, stockData);
            } else if (chartType === 'volume') {
                createVolumeChart(ctx, historicalData, stockData);
            }
        }

        // 创建价格走势图
        function createLineChart(ctx, historicalData, stockData) {
            const labels = historicalData.map(item => item.date);
            const prices = historicalData.map(item => item.close);

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `${stockData.symbol} 收盘价`,
                        data: prices,
                        borderColor: stockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: stockData.price_change >= 0 ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1,
                        pointRadius: 3,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${stockData.name} (${stockData.symbol}) - 30天价格走势`,
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day',
                                displayFormats: {
                                    day: 'MM/dd'
                                }
                            },
                            title: {
                                display: true,
                                text: '日期'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '价格 ($)'
                            },
                            beginAtZero: false
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // 创建K线图
        function createCandlestickChart(ctx, historicalData, stockData) {
            const candleData = historicalData.map(item => ({
                x: item.date,
                o: item.open,
                h: item.high,
                l: item.low,
                c: item.close
            }));

            currentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    datasets: [{
                        label: '高点',
                        data: historicalData.map(item => ({ x: item.date, y: [item.low, item.high] })),
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        borderColor: 'rgba(0, 0, 0, 0.8)',
                        borderWidth: 1,
                        barThickness: 2
                    }, {
                        label: '实体',
                        data: historicalData.map(item => ({
                            x: item.date,
                            y: [Math.min(item.open, item.close), Math.max(item.open, item.close)],
                            color: item.close >= item.open ? '#28a745' : '#dc3545'
                        })),
                        backgroundColor: function(context) {
                            const item = historicalData[context.dataIndex];
                            return item.close >= item.open ? '#28a745' : '#dc3545';
                        },
                        borderColor: function(context) {
                            const item = historicalData[context.dataIndex];
                            return item.close >= item.open ? '#28a745' : '#dc3545';
                        },
                        borderWidth: 1,
                        barThickness: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${stockData.name} (${stockData.symbol}) - K线图`,
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day',
                                displayFormats: {
                                    day: 'MM/dd'
                                }
                            },
                            title: {
                                display: true,
                                text: '日期'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '价格 ($)'
                            },
                            beginAtZero: false
                        }
                    }
                }
            });
        }

        // 创建成交量图
        function createVolumeChart(ctx, historicalData, stockData) {
            const labels = historicalData.map(item => item.date);
            const volumes = historicalData.map(item => item.volume);

            currentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '成交量',
                        data: volumes,
                        backgroundColor: historicalData.map(item =>
                            item.close >= item.open ? 'rgba(40, 167, 69, 0.7)' : 'rgba(220, 53, 69, 0.7)'
                        ),
                        borderColor: historicalData.map(item =>
                            item.close >= item.open ? '#28a745' : '#dc3545'
                        ),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${stockData.name} (${stockData.symbol}) - 成交量`,
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day',
                                displayFormats: {
                                    day: 'MM/dd'
                                }
                            },
                            title: {
                                display: true,
                                text: '日期'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '成交量'
                            },
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatNumber(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        // 格式化时间
        function formatTime(timeString) {
            const date = new Date(timeString);
            const now = new Date();
            const diffHours = Math.floor((now - date) / (1000 * 60 * 60));

            if (diffHours < 1) {
                return '刚刚';
            } else if (diffHours < 24) {
                return `${diffHours}小时前`;
            } else {
                return `${Math.floor(diffHours / 24)}天前`;
            }
        }
    </script>
</body>
</html>
