<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股价新闻关联分析</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@3"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .analysis-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .chart-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        .stock-selector {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .stock-btn {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }
        .stock-btn:hover {
            background: #218838;
        }
        .stock-btn.active {
            background: #dc3545;
        }
        .chart-canvas {
            height: 600px;
            width: 100%;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .news-timeline {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .timeline-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .news-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .news-item.positive {
            border-left-color: #28a745;
        }
        .news-item.negative {
            border-left-color: #dc3545;
        }
        .news-item.neutral {
            border-left-color: #6c757d;
        }
        .news-date {
            min-width: 80px;
            font-weight: bold;
            color: #666;
            margin-right: 15px;
            font-size: 0.9em;
        }
        .news-content {
            flex: 1;
        }
        .news-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            line-height: 1.4;
        }
        .news-summary {
            color: #666;
            font-size: 0.9em;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8em;
            color: #999;
        }
        .sentiment-badge {
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.75em;
            font-weight: 500;
        }
        .sentiment-positive {
            background: #d4edda;
            color: #155724;
        }
        .sentiment-negative {
            background: #f8d7da;
            color: #721c24;
        }
        .sentiment-neutral {
            background: #e2e3e5;
            color: #383d41;
        }
        .price-change {
            min-width: 100px;
            text-align: right;
            font-weight: bold;
            margin-left: 15px;
        }
        .price-up {
            color: #28a745;
        }
        .price-down {
            color: #dc3545;
        }
        .price-neutral {
            color: #6c757d;
        }
        .correlation-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        .legend-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .legend-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        .legend-color {
            width: 20px;
            height: 4px;
            margin-right: 10px;
            border-radius: 2px;
        }
        .legend-positive { background: #28a745; }
        .legend-negative { background: #dc3545; }
        .legend-neutral { background: #6c757d; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 股价新闻关联分析</h1>
        <p>在股价趋势图上展示各个时间点发生的新闻，分析股价和新闻之间的关系</p>
    </div>

    <div class="analysis-container">
        <div class="section-title">🎯 股票选择</div>
        <div class="chart-controls">
            <div class="stock-selector">
                <button class="stock-btn active" onclick="loadStockAnalysis('AAPL')">AAPL - Apple</button>
                <button class="stock-btn" onclick="loadStockAnalysis('MSFT')">MSFT - Microsoft</button>
                <button class="stock-btn" onclick="loadStockAnalysis('GOOGL')">GOOGL - Google</button>
                <button class="stock-btn" onclick="loadStockAnalysis('TSLA')">TSLA - Tesla</button>
                <button class="stock-btn" onclick="loadStockAnalysis('AMZN')">AMZN - Amazon</button>
                <button class="stock-btn" onclick="loadStockAnalysis('META')">META - Meta</button>
            </div>
        </div>
    </div>

    <div class="analysis-container">
        <div class="section-title">📊 股价走势与新闻事件</div>
        <div class="legend-box">
            <div class="legend-title">📊 图表说明</div>
            <div class="legend-item">
                <div class="legend-color legend-positive"></div>
                <span>积极新闻 - 可能推动股价上涨</span>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-negative"></div>
                <span>消极新闻 - 可能导致股价下跌</span>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-neutral"></div>
                <span>中性新闻 - 对股价影响不明确</span>
            </div>
        </div>
        <canvas id="correlationChart" class="chart-canvas"></canvas>
        
        <div class="correlation-stats" id="correlationStats">
            <!-- 统计数据将在这里显示 -->
        </div>
    </div>

    <div class="analysis-container">
        <div class="section-title">📰 新闻时间线与股价变动</div>
        <div class="news-timeline" id="newsTimeline">
            <div class="timeline-title">📅 按时间顺序排列的新闻事件</div>
            <div style="text-align: center; color: #666; padding: 20px;">
                请选择股票查看新闻时间线...
            </div>
        </div>
    </div>

    <script>
        let currentChart = null;
        let currentStockData = null;
        let currentNewsData = null;
        let currentSymbol = 'AAPL';

        // 页面加载完成后自动加载AAPL数据
        window.addEventListener('load', function() {
            loadStockAnalysis('AAPL');
        });

        // 加载股票分析数据
        async function loadStockAnalysis(symbol) {
            currentSymbol = symbol;
            
            // 更新按钮状态
            document.querySelectorAll('.stock-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            try {
                const response = await fetch(`http://localhost:8080/api/stock/${symbol}`);
                const data = await response.json();
                
                if (response.ok) {
                    currentStockData = data.stock_data;
                    currentNewsData = data.news || [];
                    
                    createCorrelationChart();
                    createNewsTimeline();
                    updateCorrelationStats();
                } else {
                    console.error('获取股票数据失败:', data.error);
                }
            } catch (error) {
                console.error('网络错误:', error);
            }
        }

        // 创建关联分析图表
        function createCorrelationChart() {
            if (!currentStockData) return;

            const canvas = document.getElementById('correlationChart');
            const ctx = canvas.getContext('2d');

            if (currentChart) {
                currentChart.destroy();
            }

            const historicalData = currentStockData.historical_data || [];
            const labels = historicalData.map(item => item.date);
            const prices = historicalData.map(item => item.close);

            // 生成新闻标注
            const annotations = [];
            if (currentNewsData.length > 0) {
                currentNewsData.forEach((news, index) => {
                    const newsDate = new Date(news.published_time).toISOString().split('T')[0];
                    const dataPoint = historicalData.find(item => item.date === newsDate);
                    
                    if (dataPoint) {
                        let color = '#6c757d';
                        let emoji = '📰';
                        if (news.sentiment === 'positive') {
                            color = '#28a745';
                            emoji = '📈';
                        } else if (news.sentiment === 'negative') {
                            color = '#dc3545';
                            emoji = '📉';
                        }
                        
                        // 垂直线标注
                        annotations.push({
                            type: 'line',
                            mode: 'vertical',
                            scaleID: 'x',
                            value: newsDate,
                            borderColor: color,
                            borderWidth: 3,
                            borderDash: [8, 4],
                            label: {
                                enabled: true,
                                content: `${emoji} ${news.title.substring(0, 25)}...`,
                                position: 'top',
                                backgroundColor: color,
                                color: 'white',
                                font: { size: 11, weight: 'bold' },
                                padding: 6,
                                cornerRadius: 4,
                                rotation: 0
                            }
                        });
                        
                        // 在股价线上标注点
                        annotations.push({
                            type: 'point',
                            scaleID: 'x',
                            value: newsDate,
                            yValue: dataPoint.close,
                            backgroundColor: color,
                            borderColor: 'white',
                            borderWidth: 3,
                            radius: 8
                        });
                    }
                });
            }

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `${currentStockData.symbol} 收盘价`,
                        data: prices,
                        borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: currentStockData.price_change >= 0 ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.1,
                        pointRadius: 4,
                        pointHoverRadius: 8,
                        pointBackgroundColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        pointBorderColor: 'white',
                        pointBorderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentStockData.name} (${currentStockData.symbol}) - 股价与新闻事件关联分析`,
                            font: { size: 18, weight: 'bold' }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        annotation: {
                            annotations: annotations
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day',
                                displayFormats: {
                                    day: 'MM/dd'
                                }
                            },
                            title: {
                                display: true,
                                text: '日期',
                                font: { size: 14, weight: 'bold' }
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '股价 ($)',
                                font: { size: 14, weight: 'bold' }
                            },
                            beginAtZero: false
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // 创建新闻时间线
        function createNewsTimeline() {
            const timeline = document.getElementById('newsTimeline');
            
            if (!currentNewsData || currentNewsData.length === 0) {
                timeline.innerHTML = `
                    <div class="timeline-title">📅 新闻时间线</div>
                    <div style="text-align: center; color: #666; padding: 20px;">
                        暂无新闻数据
                    </div>
                `;
                return;
            }

            const historicalData = currentStockData.historical_data || [];
            
            // 按时间排序新闻
            const sortedNews = [...currentNewsData].sort((a, b) => 
                new Date(a.published_time) - new Date(b.published_time)
            );

            let timelineHTML = `<div class="timeline-title">📅 新闻时间线与股价变动</div>`;
            
            sortedNews.forEach((news, index) => {
                const newsDate = new Date(news.published_time).toISOString().split('T')[0];
                const dataPoint = historicalData.find(item => item.date === newsDate);
                
                const sentimentClass = news.sentiment || 'neutral';
                const sentimentText = {
                    'positive': '积极',
                    'negative': '消极',
                    'neutral': '中性'
                }[sentimentClass] || '中性';
                
                const sentimentEmoji = {
                    'positive': '📈',
                    'negative': '📉',
                    'neutral': '📰'
                }[sentimentClass] || '📰';

                let priceChangeHTML = '';
                if (dataPoint && index > 0) {
                    const prevDataPoint = historicalData.find(item => 
                        new Date(item.date) < new Date(newsDate)
                    );
                    if (prevDataPoint) {
                        const priceChange = ((dataPoint.close - prevDataPoint.close) / prevDataPoint.close * 100);
                        const changeClass = priceChange > 0 ? 'price-up' : priceChange < 0 ? 'price-down' : 'price-neutral';
                        const changeSymbol = priceChange > 0 ? '+' : '';
                        priceChangeHTML = `
                            <div class="price-change ${changeClass}">
                                ${changeSymbol}${priceChange.toFixed(2)}%
                                <div style="font-size: 0.8em;">$${dataPoint.close.toFixed(2)}</div>
                            </div>
                        `;
                    }
                }

                timelineHTML += `
                    <div class="news-item ${sentimentClass}">
                        <div class="news-date">
                            ${new Date(news.published_time).toLocaleDateString('zh-CN', {
                                month: 'short',
                                day: 'numeric'
                            })}
                        </div>
                        <div class="news-content">
                            <div class="news-title">
                                ${sentimentEmoji} ${news.title}
                            </div>
                            <div class="news-summary">
                                ${news.summary || '暂无摘要'}
                            </div>
                            <div class="news-meta">
                                <span>${news.source} • ${formatTime(news.published_time)}</span>
                                <span class="sentiment-badge sentiment-${sentimentClass}">
                                    ${sentimentText}
                                </span>
                            </div>
                        </div>
                        ${priceChangeHTML}
                    </div>
                `;
            });

            timeline.innerHTML = timelineHTML;
        }

        // 更新关联统计
        function updateCorrelationStats() {
            const statsContainer = document.getElementById('correlationStats');
            
            if (!currentNewsData || currentNewsData.length === 0) {
                statsContainer.innerHTML = '';
                return;
            }

            const totalNews = currentNewsData.length;
            const positiveNews = currentNewsData.filter(news => news.sentiment === 'positive').length;
            const negativeNews = currentNewsData.filter(news => news.sentiment === 'negative').length;
            const neutralNews = totalNews - positiveNews - negativeNews;

            const historicalData = currentStockData.historical_data || [];
            const priceChange = currentStockData.price_change || 0;
            const priceChangePercent = currentStockData.price_change_percent || 0;

            statsContainer.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${totalNews}</div>
                    <div class="stat-label">新闻总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #28a745;">${positiveNews}</div>
                    <div class="stat-label">积极新闻</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #dc3545;">${negativeNews}</div>
                    <div class="stat-label">消极新闻</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #6c757d;">${neutralNews}</div>
                    <div class="stat-label">中性新闻</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: ${priceChangePercent >= 0 ? '#28a745' : '#dc3545'};">
                        ${priceChangePercent >= 0 ? '+' : ''}${priceChangePercent.toFixed(2)}%
                    </div>
                    <div class="stat-label">期间涨跌幅</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${historicalData.length}</div>
                    <div class="stat-label">交易日数</div>
                </div>
            `;
        }

        // 格式化时间
        function formatTime(timeString) {
            const date = new Date(timeString);
            const now = new Date();
            const diffHours = Math.floor((now - date) / (1000 * 60 * 60));
            
            if (diffHours < 1) {
                return '刚刚';
            } else if (diffHours < 24) {
                return `${diffHours}小时前`;
            } else {
                const diffDays = Math.floor(diffHours / 24);
                if (diffDays < 7) {
                    return `${diffDays}天前`;
                } else {
                    return date.toLocaleDateString('zh-CN');
                }
            }
        }
    </script>
</body>
</html>
