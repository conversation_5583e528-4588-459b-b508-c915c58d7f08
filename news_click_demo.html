<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻点击详情演示</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@3"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .chart-with-news {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
        }
        .chart-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .chart-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .chart-btn {
            padding: 8px 16px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }
        .chart-btn:hover {
            background: #5a6268;
        }
        .chart-btn.active {
            background: #dc3545;
        }
        .chart-canvas {
            height: 500px;
            width: 100%;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .stock-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .stock-btn {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background 0.3s ease;
        }
        .stock-btn:hover {
            background: #218838;
        }
        .stock-btn.active {
            background: #dc3545;
        }
        .news-detail-sidebar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            height: fit-content;
            max-height: 600px;
            overflow-y: auto;
        }
        .news-detail-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .news-item-detail {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }
        .news-item-detail.positive {
            border-left-color: #28a745;
            background: #f8fff9;
        }
        .news-item-detail.negative {
            border-left-color: #dc3545;
            background: #fff8f8;
        }
        .news-item-detail.neutral {
            border-left-color: #6c757d;
            background: #f8f9fa;
        }
        .news-detail-headline {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        .news-detail-summary {
            color: #666;
            line-height: 1.5;
            margin-bottom: 10px;
        }
        .news-detail-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
            color: #999;
            margin-bottom: 10px;
        }
        .news-sentiment-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }
        .sentiment-positive {
            background: #d4edda;
            color: #155724;
        }
        .sentiment-negative {
            background: #f8d7da;
            color: #721c24;
        }
        .sentiment-neutral {
            background: #e2e3e5;
            color: #383d41;
        }
        .news-placeholder {
            text-align: center;
            color: #666;
            padding: 40px 20px;
        }
        .feature-highlight {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        .feature-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }
        .instruction-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .instruction-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 新闻点击详情演示</h1>
        <p>点击趋势图上的新闻标注点，在右边查看具体新闻详情</p>
    </div>

    <div class="demo-section">
        <div class="section-title">🎯 功能特色</div>
        <div class="feature-highlight">
            <div class="feature-title">🎯 精准点击</div>
            <div>在趋势图上点击新闻标注的圆点，即可在右侧详情栏查看完整的新闻内容。</div>
        </div>
        <div class="feature-highlight">
            <div class="feature-title">📰 详细信息</div>
            <div>显示新闻标题、摘要、发布时间、来源、情绪分析等完整信息。</div>
        </div>
        <div class="feature-highlight">
            <div class="feature-title">🔗 原文链接</div>
            <div>提供新闻原文链接，可以跳转到原始新闻页面查看完整内容。</div>
        </div>
        <div class="instruction-box">
            <div class="instruction-title">💡 使用说明</div>
            <div>
                1. 选择要查看的股票<br>
                2. 点击"显示新闻标注"按钮<br>
                3. 在图表上找到新闻标注点（彩色圆点）<br>
                4. 点击圆点查看新闻详情<br>
                5. 右侧会显示完整的新闻信息
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="section-title">🎯 股票选择</div>
        <div class="stock-selector">
            <button class="stock-btn active" onclick="loadStockChart('AAPL')">AAPL - Apple</button>
            <button class="stock-btn" onclick="loadStockChart('MSFT')">MSFT - Microsoft</button>
            <button class="stock-btn" onclick="loadStockChart('GOOGL')">GOOGL - Google</button>
            <button class="stock-btn" onclick="loadStockChart('TSLA')">TSLA - Tesla</button>
            <button class="stock-btn" onclick="loadStockChart('AMZN')">AMZN - Amazon</button>
            <button class="stock-btn" onclick="loadStockChart('META')">META - Meta</button>
        </div>
    </div>

    <div class="demo-section">
        <div class="section-title">📰 新闻点击演示</div>
        <div class="chart-controls">
            <div class="chart-actions">
                <button class="chart-btn active" onclick="toggleNewsMarkers()" id="newsBtn">显示新闻标注</button>
                <button class="chart-btn" onclick="showPriceChart()">价格走势图</button>
                <button class="chart-btn" onclick="showCombinedChart()">组合图表</button>
            </div>
        </div>
        
        <div class="chart-with-news">
            <div class="chart-main-area">
                <canvas id="mainChart" class="chart-canvas"></canvas>
            </div>
            
            <div class="news-detail-sidebar">
                <div class="news-detail-title">📰 新闻详情</div>
                <div id="newsDetailContent">
                    <div class="news-placeholder">
                        <div style="font-size: 2em; margin-bottom: 10px;">🎯</div>
                        <div>点击图表上的新闻标注点查看详情</div>
                        <div style="margin-top: 10px; font-size: 0.9em; color: #999;">
                            新闻标注点显示为彩色圆点
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentChart = null;
        let currentStockData = null;
        let currentNewsData = null;
        let currentSymbol = 'AAPL';
        let showNewsMarkers = true;
        let currentChartType = 'combined';

        // 页面加载完成后自动加载AAPL数据
        window.addEventListener('load', function() {
            loadStockChart('AAPL');
        });

        // 加载股票图表
        async function loadStockChart(symbol) {
            currentSymbol = symbol;
            
            // 更新按钮状态
            document.querySelectorAll('.stock-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            try {
                const response = await fetch(`http://localhost:8080/api/stock/${symbol}`);
                const data = await response.json();
                
                if (response.ok) {
                    currentStockData = data.stock_data;
                    currentNewsData = data.news || [];
                    createChart();
                } else {
                    console.error('获取股票数据失败:', data.error);
                }
            } catch (error) {
                console.error('网络错误:', error);
            }
        }

        // 切换新闻标注显示
        function toggleNewsMarkers() {
            showNewsMarkers = !showNewsMarkers;
            const btn = document.getElementById('newsBtn');
            
            if (showNewsMarkers) {
                btn.textContent = '隐藏新闻标注';
                btn.classList.add('active');
            } else {
                btn.textContent = '显示新闻标注';
                btn.classList.remove('active');
            }
            
            createChart();
        }

        // 显示价格走势图
        function showPriceChart() {
            currentChartType = 'price';
            createChart();
        }

        // 显示组合图表
        function showCombinedChart() {
            currentChartType = 'combined';
            createChart();
        }

        // 创建图表
        function createChart() {
            if (!currentStockData) return;
            
            switch(currentChartType) {
                case 'price':
                    createPriceChart();
                    break;
                case 'combined':
                default:
                    createCombinedChart();
                    break;
            }
        }

        // 处理新闻标注
        function getNewsAnnotations() {
            if (!showNewsMarkers || !currentNewsData || currentNewsData.length === 0) {
                return [];
            }
            
            const annotations = [];
            const historicalData = currentStockData.historical_data || [];
            
            currentNewsData.forEach((news, index) => {
                const newsDate = new Date(news.published_time).toISOString().split('T')[0];
                
                // 查找对应的历史数据点
                const dataPoint = historicalData.find(item => item.date === newsDate);
                
                if (dataPoint) {
                    // 根据新闻情绪确定颜色和图标
                    let color = '#6c757d';
                    let emoji = '📰';
                    if (news.sentiment === 'positive') {
                        color = '#28a745';
                        emoji = '📈';
                    } else if (news.sentiment === 'negative') {
                        color = '#dc3545';
                        emoji = '📉';
                    }
                    
                    // 添加垂直线标注
                    annotations.push({
                        type: 'line',
                        mode: 'vertical',
                        scaleID: 'x',
                        value: newsDate,
                        borderColor: color,
                        borderWidth: 2,
                        borderDash: [5, 5],
                        label: {
                            enabled: true,
                            content: `${emoji} ${news.title.substring(0, 15)}...`,
                            position: 'top',
                            backgroundColor: color,
                            color: 'white',
                            font: { size: 10, weight: 'bold' },
                            padding: 4,
                            cornerRadius: 4
                        }
                    });
                    
                    // 添加可点击的圆点标注
                    annotations.push({
                        type: 'point',
                        scaleID: 'x',
                        value: newsDate,
                        yValue: dataPoint.close,
                        backgroundColor: color,
                        borderColor: 'white',
                        borderWidth: 3,
                        radius: 10,
                        pointStyle: 'circle',
                        label: { enabled: false },
                        newsIndex: index,
                        clickable: true
                    });
                }
            });
            
            return annotations;
        }

        // 处理图表点击事件
        function handleChartClick(event, elements, chart) {
            if (!showNewsMarkers || !currentNewsData) return;
            
            const canvasPosition = Chart.helpers.getRelativePosition(event, chart);
            const annotations = getNewsAnnotations();
            const clickThreshold = 20;
            
            annotations.forEach((annotation) => {
                if (annotation.type === 'point' && annotation.clickable) {
                    const annotationX = chart.scales.x.getPixelForValue(annotation.value);
                    const annotationY = chart.scales.y.getPixelForValue(annotation.yValue);
                    
                    const distance = Math.sqrt(
                        Math.pow(canvasPosition.x - annotationX, 2) + 
                        Math.pow(canvasPosition.y - annotationY, 2)
                    );
                    
                    if (distance <= clickThreshold) {
                        showNewsDetail(annotation.newsIndex);
                        return;
                    }
                }
            });
        }

        // 显示新闻详情
        function showNewsDetail(newsIndex) {
            const content = document.getElementById('newsDetailContent');
            
            if (!currentNewsData || newsIndex >= currentNewsData.length) {
                return;
            }
            
            const news = currentNewsData[newsIndex];
            const sentimentClass = news.sentiment || 'neutral';
            const sentimentText = {
                'positive': '积极',
                'negative': '消极',
                'neutral': '中性'
            }[sentimentClass] || '中性';
            
            const sentimentEmoji = {
                'positive': '📈',
                'negative': '📉',
                'neutral': '📰'
            }[sentimentClass] || '📰';
            
            content.innerHTML = `
                <div class="news-item-detail ${sentimentClass}">
                    <div class="news-detail-headline">
                        ${sentimentEmoji} ${news.title}
                    </div>
                    <div class="news-detail-summary">
                        ${news.summary || '暂无摘要'}
                    </div>
                    <div class="news-detail-meta">
                        <span>${news.source} • ${formatTime(news.published_time)}</span>
                        <span class="news-sentiment-badge sentiment-${sentimentClass}">
                            ${sentimentText}
                        </span>
                    </div>
                    ${news.url ? `<div style="margin-top: 10px;">
                        <a href="${news.url}" target="_blank" style="color: #007bff; text-decoration: none;">
                            🔗 查看原文
                        </a>
                    </div>` : ''}
                </div>
            `;
        }

        // 格式化时间
        function formatTime(timeString) {
            const date = new Date(timeString);
            const now = new Date();
            const diffHours = Math.floor((now - date) / (1000 * 60 * 60));
            
            if (diffHours < 1) {
                return '刚刚';
            } else if (diffHours < 24) {
                return `${diffHours}小时前`;
            } else {
                const diffDays = Math.floor(diffHours / 24);
                if (diffDays < 7) {
                    return `${diffDays}天前`;
                } else {
                    return date.toLocaleDateString('zh-CN');
                }
            }
        }

        // 创建价格走势图
        function createPriceChart() {
            const chartCanvas = document.getElementById('mainChart');
            const ctx = chartCanvas.getContext('2d');
            
            if (currentChart) {
                currentChart.destroy();
            }
            
            const historicalData = currentStockData.historical_data || [];
            const labels = historicalData.map(item => item.date);
            const prices = historicalData.map(item => item.close);
            
            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `${currentStockData.symbol} 收盘价`,
                        data: prices,
                        borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: currentStockData.price_change >= 0 ? 'rgba(40, 167, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.1,
                        pointRadius: 3,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentStockData.name} (${currentStockData.symbol}) - 价格走势与新闻点击`,
                            font: { size: 16, weight: 'bold' }
                        },
                        annotation: {
                            annotations: getNewsAnnotations()
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: { unit: 'day', displayFormats: { day: 'MM/dd' } },
                            title: { display: true, text: '日期' }
                        },
                        y: {
                            title: { display: true, text: '价格 ($)' }
                        }
                    },
                    onClick: function(event, elements) {
                        handleChartClick(event, elements, this);
                    }
                }
            });
        }

        // 创建组合图表
        function createCombinedChart() {
            const chartCanvas = document.getElementById('mainChart');
            const ctx = chartCanvas.getContext('2d');
            
            if (currentChart) {
                currentChart.destroy();
            }
            
            const historicalData = currentStockData.historical_data || [];
            const labels = historicalData.map(item => item.date);
            
            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '收盘价',
                        data: historicalData.map(item => item.close),
                        type: 'line',
                        borderColor: currentStockData.price_change >= 0 ? '#28a745' : '#dc3545',
                        backgroundColor: 'transparent',
                        borderWidth: 3,
                        pointRadius: 3,
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y'
                    }, {
                        label: '成交量',
                        data: historicalData.map(item => item.volume),
                        type: 'bar',
                        backgroundColor: historicalData.map(item => 
                            item.close >= item.open ? 'rgba(40, 167, 69, 0.3)' : 'rgba(220, 53, 69, 0.3)'
                        ),
                        borderColor: historicalData.map(item => 
                            item.close >= item.open ? '#28a745' : '#dc3545'
                        ),
                        borderWidth: 1,
                        barThickness: 6,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${currentStockData.name} (${currentStockData.symbol}) - 组合图表与新闻点击`,
                            font: { size: 16, weight: 'bold' }
                        },
                        annotation: {
                            annotations: getNewsAnnotations()
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: { unit: 'day', displayFormats: { day: 'MM/dd' } },
                            title: { display: true, text: '日期' }
                        },
                        y: {
                            type: 'linear',
                            position: 'left',
                            title: { display: true, text: '价格 ($)' }
                        },
                        y1: {
                            type: 'linear',
                            position: 'right',
                            title: { display: true, text: '成交量' },
                            beginAtZero: true,
                            grid: { drawOnChartArea: false },
                            ticks: {
                                callback: function(value) {
                                    if (value >= 1000000000) return (value/1000000000).toFixed(1) + 'B';
                                    if (value >= 1000000) return (value/1000000).toFixed(1) + 'M';
                                    if (value >= 1000) return (value/1000).toFixed(1) + 'K';
                                    return value.toString();
                                }
                            }
                        }
                    },
                    onClick: function(event, elements) {
                        handleChartClick(event, elements, this);
                    }
                }
            });
        }
    </script>
</body>
</html>
