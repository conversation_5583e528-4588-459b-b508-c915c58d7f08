<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .image-test {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .image-item {
            width: 200px;
            height: 150px;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }
        .image-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2em;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .loading { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🖼️ 图片加载测试</h1>
    
    <div class="test-section">
        <h2>Picsum 图片测试</h2>
        <div class="image-test">
            <div class="image-item">
                <img src="https://picsum.photos/400/200?random=1" alt="测试图片1" onload="showStatus(this, 'success')" onerror="showStatus(this, 'error')">
                <div class="status loading">加载中...</div>
            </div>
            <div class="image-item">
                <img src="https://picsum.photos/400/200?random=2" alt="测试图片2" onload="showStatus(this, 'success')" onerror="showStatus(this, 'error')">
                <div class="status loading">加载中...</div>
            </div>
            <div class="image-item">
                <img src="https://picsum.photos/400/200?random=3" alt="测试图片3" onload="showStatus(this, 'success')" onerror="showStatus(this, 'error')">
                <div class="status loading">加载中...</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>错误图片测试</h2>
        <div class="image-test">
            <div class="image-item">
                <img src="https://invalid-url.com/image1.jpg" alt="错误图片1" onload="showStatus(this, 'success')" onerror="showErrorImage(this)">
                <div class="status loading">加载中...</div>
            </div>
            <div class="image-item">
                <img src="https://invalid-url.com/image2.jpg" alt="错误图片2" onload="showStatus(this, 'success')" onerror="showErrorImage(this)">
                <div class="status loading">加载中...</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>占位符测试</h2>
        <div class="image-test">
            <div class="image-item">
                <div class="placeholder">🚗</div>
                <div class="status success">周边游占位符</div>
            </div>
            <div class="image-item">
                <div class="placeholder">🏔️</div>
                <div class="status success">国内游占位符</div>
            </div>
            <div class="image-item">
                <div class="placeholder">🌍</div>
                <div class="status success">出境游占位符</div>
            </div>
        </div>
    </div>

    <script>
        function showStatus(img, type) {
            const statusDiv = img.nextElementSibling;
            statusDiv.className = `status ${type}`;
            if (type === 'success') {
                statusDiv.textContent = '✅ 加载成功';
            } else if (type === 'error') {
                statusDiv.textContent = '❌ 加载失败';
            }
        }

        function showErrorImage(img) {
            showStatus(img, 'error');
            img.style.display = 'none';
            
            // 创建占位符
            const placeholder = document.createElement('div');
            placeholder.className = 'placeholder';
            placeholder.innerHTML = '🖼️';
            img.parentElement.insertBefore(placeholder, img);
        }

        // 页面加载完成后的统计
        window.addEventListener('load', function() {
            setTimeout(() => {
                const images = document.querySelectorAll('img');
                let loaded = 0;
                let failed = 0;
                
                images.forEach(img => {
                    if (img.complete) {
                        if (img.naturalWidth > 0) {
                            loaded++;
                        } else {
                            failed++;
                        }
                    }
                });
                
                console.log(`图片加载统计: 成功 ${loaded} 张, 失败 ${failed} 张`);
            }, 3000);
        });
    </script>
</body>
</html>
