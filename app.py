from flask import Flask, render_template, request, jsonify
import requests
from datetime import datetime
import google.generativeai as genai

app = Flask(__name__)

class WeatherApp:
    def __init__(self):
        # API密钥配置
        self.api_key = "547bca00ca205ddd4f903f8890d8b8e3"
        self.base_url = "http://api.openweathermap.org/data/2.5/weather"
        self.gemini_api_key = "AIzaSyBqRUjcDrplOUpJBNc3PtbmFDii3p8lR4M"
        self.setup_gemini()
    
    def setup_gemini(self):
        """配置Gemini API"""
        try:
            if self.gemini_api_key and self.gemini_api_key != "YOUR_GEMINI_API_KEY_HERE":
                genai.configure(api_key=self.gemini_api_key)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                self.use_gemini = True
            else:
                self.use_gemini = False
        except Exception as e:
            print(f"Gemini配置失败: {e}")
            self.use_gemini = False
    
    def get_weather(self, city_name):
        """获取指定城市的天气信息"""
        try:
            params = {
                'q': city_name,
                'appid': self.api_key,
                'units': 'metric',
                'lang': 'zh_cn'
            }
            
            response = requests.get(self.base_url, params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            print(f"获取天气失败: {e}")
            return None
    
    def get_weather_icon(self, description, temp):
        """根据天气状况和温度返回对应的图标"""
        desc_lower = description.lower()
        
        # 雨天图标
        if any(word in desc_lower for word in ['雨', 'rain', '阵雨', 'shower']):
            return '🌧️'
        # 雪天图标
        elif any(word in desc_lower for word in ['雪', 'snow']):
            return '❄️'
        # 雾霾图标
        elif any(word in desc_lower for word in ['雾', 'fog', '霾', 'haze']):
            return '🌫️'
        # 多云图标
        elif any(word in desc_lower for word in ['云', 'cloud', '阴']):
            if temp > 25:
                return '⛅'  # 温暖多云
            else:
                return '☁️'  # 凉爽多云
        # 晴天图标
        elif any(word in desc_lower for word in ['晴', 'clear', 'sunny']):
            if temp > 30:
                return '☀️'  # 炎热晴天
            elif temp > 20:
                return '🌤️'  # 温暖晴天
            else:
                return '🌞'  # 凉爽晴天
        # 默认图标
        else:
            return '🌤️'
    
    def get_clothing_by_temperature(self, temp, feels_like):
        """根据温度获取基础服装建议的工具函数"""
        reference_temp = feels_like
        temp_diff = abs(temp - feels_like)
        
        if reference_temp >= 30:
            extra_note = " 注意温差" if temp_diff > 3 else ""
            return {
                "top": "轻薄透气的短袖T恤或衬衫",
                "bottom": "短裤或轻薄长裤",
                "shoes": "透气凉鞋或轻便运动鞋",
                "accessories": "遮阳帽、太阳镜",
                "reason": f"高温天气需要透气散热{extra_note}"
            }
        elif reference_temp >= 25:
            return {
                "top": "短袖衬衫或薄长袖",
                "bottom": "长裤或裙子",
                "shoes": "休闲鞋或运动鞋",
                "accessories": "轻薄外套备用",
                "reason": "温暖舒适的搭配"
            }
        elif reference_temp >= 20:
            return {
                "top": "长袖衬衫或薄毛衣",
                "bottom": "长裤",
                "shoes": "休闲鞋或靴子",
                "accessories": "轻薄外套",
                "reason": "微凉天气需要适度保暖"
            }
        elif reference_temp >= 15:
            return {
                "top": "毛衣或厚衬衫",
                "bottom": "长裤",
                "shoes": "靴子或厚底鞋",
                "accessories": "夹克或薄外套",
                "reason": "凉爽天气需要保暖"
            }
        elif reference_temp >= 10:
            return {
                "top": "厚毛衣或卫衣",
                "bottom": "厚长裤",
                "shoes": "保暖靴子",
                "accessories": "厚外套或夹克",
                "reason": "寒冷天气需要多层保暖"
            }
        elif reference_temp >= 5:
            return {
                "top": "厚毛衣加保暖内衣",
                "bottom": "厚长裤或保暖裤",
                "shoes": "保暖靴子",
                "accessories": "厚大衣、围巾",
                "reason": "严寒天气需要全面保暖"
            }
        else:
            return {
                "top": "厚羽绒服或棉衣",
                "bottom": "保暖裤或厚长裤",
                "shoes": "防滑保暖靴",
                "accessories": "手套、围巾、帽子",
                "reason": "极寒天气需要防寒装备"
            }

    def get_weather_specific_advice(self, description, wind_speed, humidity):
        """根据具体天气状况获取专门建议的工具函数"""
        advice = []
        
        desc_lower = description.lower()
        if any(word in desc_lower for word in ['雨', 'rain', '阵雨', 'shower']):
            advice.append("☔ 携带雨伞或穿防水外套")
        elif any(word in desc_lower for word in ['雪', 'snow']):
            advice.append("❄️ 穿防滑鞋，注意保暖防湿")
        elif any(word in desc_lower for word in ['雾', 'fog', '霾', 'haze']):
            advice.append("😷 佩戴口罩，注意交通安全")
        elif any(word in desc_lower for word in ['晴', 'clear', 'sunny']):
            advice.append("☀️ 做好防晒措施")
        
        if wind_speed > 10:
            advice.append("💨 穿防风外套，固定帽子")
        elif wind_speed > 5:
            advice.append("🌬️ 注意防风，避免宽松衣物")
        
        if humidity > 80:
            advice.append("💧 选择透气材质，避免闷热")
        elif humidity < 30:
            advice.append("🧴 注意保湿，多喝水")
        
        return advice

    def get_gemini_clothing_advice(self, weather_data):
        """使用Gemini AI生成智能穿衣建议"""
        try:
            if not self.use_gemini:
                return self._fallback_advice_simple(weather_data)
            
            city = weather_data['name']
            country = weather_data['sys']['country']
            temp = weather_data['main']['temp']
            feels_like = weather_data['main']['feels_like']
            humidity = weather_data['main']['humidity']
            description = weather_data['weather'][0]['description']
            wind_speed = weather_data['wind']['speed']
            
            temp_advice = self.get_clothing_by_temperature(temp, feels_like)
            weather_advice = self.get_weather_specific_advice(description, wind_speed, humidity)
            
            prompt = f"""
作为专业时尚顾问，请根据{city}, {country}的天气生成穿衣建议：

天气数据：
- 温度：{temp}°C，体感：{feels_like}°C
- 天气：{description}
- 湿度：{humidity}%，风速：{wind_speed}m/s

基础分析：
- 上衣：{temp_advice['top']}
- 下装：{temp_advice['bottom']}
- 鞋子：{temp_advice['shoes']}
- 配饰：{temp_advice['accessories']}

特殊提醒：{chr(10).join(weather_advice) if weather_advice else '无'}

请生成5条实用建议，格式：emoji + 建议 - 原因（10字内）
"""
            
            response = self.gemini_model.generate_content(prompt)
            
            if response and response.text:
                advice_lines = [line.strip() for line in response.text.strip().split('\n') if line.strip()]
                advice = []
                
                for line in advice_lines:
                    if line and (line.startswith(('1.', '2.', '3.', '4.', '5.')) or 
                               any(emoji in line for emoji in ['👕', '👔', '🧥', '👖', '👗', '👠', '👟', '🧢', '🧤', '🧣', '☂️', '🌂', '😷', '🕶️', '💧', '🌡️', '💨', '☀️', '❄️', '☔']) or
                               ('-' in line and len(line) > 10)):
                        clean_line = line
                        for i in range(1, 10):
                            clean_line = clean_line.replace(f"{i}. ", "").replace(f"{i}.", "")
                        clean_line = clean_line.strip()
                        if clean_line:
                            advice.append(clean_line)
                
                return advice[:5] if advice else self._fallback_advice_simple(weather_data)
            
            return self._fallback_advice_simple(weather_data)
                
        except Exception as e:
            print(f"Gemini调用失败: {e}")
            return self._fallback_advice_simple(weather_data)
    
    def _fallback_advice_simple(self, weather_data):
        """简单回退建议"""
        temp = weather_data['main']['temp']
        feels_like = weather_data['main']['feels_like']
        description = weather_data['weather'][0]['description']
        wind_speed = weather_data['wind']['speed']
        humidity = weather_data['main']['humidity']
        
        temp_advice = self.get_clothing_by_temperature(temp, feels_like)
        weather_advice = self.get_weather_specific_advice(description, wind_speed, humidity)
        
        advice = []
        advice.append(f"👕 {temp_advice['top']} - {temp_advice['reason']}")
        advice.append(f"👖 {temp_advice['bottom']} - 舒适搭配")
        advice.append(f"👟 {temp_advice['shoes']} - 适合天气")
        
        for special_advice in weather_advice[:2]:
            advice.append(special_advice)
        
        return advice[:5]

# 创建WeatherApp实例
weather_app = WeatherApp()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/location/<float:lat>/<float:lon>')
def get_location_info(lat, lon):
    """API端点：根据坐标获取城市信息"""
    try:
        # 使用OpenWeatherMap的反向地理编码API
        url = f"http://api.openweathermap.org/geo/1.0/reverse"
        params = {
            'lat': lat,
            'lon': lon,
            'limit': 1,
            'appid': weather_app.api_key
        }

        response = requests.get(url, params=params)

        if response.status_code == 200:
            data = response.json()
            if data:
                location = data[0]
                city_name = location.get('name', '')
                country = location.get('country', '')

                return jsonify({
                    'city': city_name,
                    'country': country,
                    'lat': lat,
                    'lon': lon
                })
            else:
                return jsonify({'error': '无法找到对应的城市'}), 404
        else:
            return jsonify({'error': '地理编码服务不可用'}), 503

    except Exception as e:
        print(f"地理位置解析失败: {e}")
        return jsonify({'error': '位置解析失败'}), 500

@app.route('/api/weather/coords/<float:lat>/<float:lon>')
def get_weather_by_coords(lat, lon):
    """API端点：根据坐标获取天气和建议"""
    try:
        # 使用坐标直接获取天气
        params = {
            'lat': lat,
            'lon': lon,
            'appid': weather_app.api_key,
            'units': 'metric',
            'lang': 'zh_cn'
        }

        response = requests.get(weather_app.base_url, params=params)

        if response.status_code == 200:
            weather_data = response.json()

            # 获取天气图标
            temp = weather_data['main']['temp']
            description = weather_data['weather'][0]['description']
            weather_icon = weather_app.get_weather_icon(description, temp)

            # 获取穿衣建议
            clothing_advice = weather_app.get_gemini_clothing_advice(weather_data)

            # 构建响应数据
            response_data = {
                'city': weather_data['name'],
                'country': weather_data['sys']['country'],
                'temperature': temp,
                'feels_like': weather_data['main']['feels_like'],
                'description': description,
                'humidity': weather_data['main']['humidity'],
                'pressure': weather_data['main']['pressure'],
                'wind_speed': weather_data['wind']['speed'],
                'weather_icon': weather_icon,
                'clothing_advice': clothing_advice,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return jsonify(response_data)
        else:
            return jsonify({'error': '无法获取天气信息'}), 404

    except Exception as e:
        print(f"坐标天气获取失败: {e}")
        return jsonify({'error': '天气获取失败'}), 500

@app.route('/api/weather/<city>')
def get_weather_api(city):
    """API端点：获取城市天气和建议"""
    weather_data = weather_app.get_weather(city)

    if not weather_data:
        return jsonify({'error': '无法获取天气信息'}), 404
    
    # 获取天气图标
    temp = weather_data['main']['temp']
    description = weather_data['weather'][0]['description']
    weather_icon = weather_app.get_weather_icon(description, temp)
    
    # 获取穿衣建议
    clothing_advice = weather_app.get_gemini_clothing_advice(weather_data)
    
    # 构建响应数据
    response_data = {
        'city': weather_data['name'],
        'country': weather_data['sys']['country'],
        'temperature': temp,
        'feels_like': weather_data['main']['feels_like'],
        'description': description,
        'humidity': weather_data['main']['humidity'],
        'pressure': weather_data['main']['pressure'],
        'wind_speed': weather_data['wind']['speed'],
        'weather_icon': weather_icon,
        'clothing_advice': clothing_advice,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    return jsonify(response_data)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
