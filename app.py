from flask import Flask, render_template, request, jsonify
import requests
from datetime import datetime
import google.generativeai as genai

app = Flask(__name__)

class WeatherApp:
    def __init__(self):
        # API密钥配置
        self.api_key = "547bca00ca205ddd4f903f8890d8b8e3"
        self.base_url = "http://api.openweathermap.org/data/2.5/weather"
        self.gemini_api_key = "AIzaSyBqRUjcDrplOUpJBNc3PtbmFDii3p8lR4M"
        self.setup_gemini()
    
    def setup_gemini(self):
        """配置Gemini API"""
        try:
            if self.gemini_api_key and self.gemini_api_key != "YOUR_GEMINI_API_KEY_HERE":
                genai.configure(api_key=self.gemini_api_key)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                self.use_gemini = True
            else:
                self.use_gemini = False
        except Exception as e:
            print(f"Gemini配置失败: {e}")
            self.use_gemini = False
    
    def get_weather(self, city_name):
        """获取指定城市的天气信息"""
        try:
            params = {
                'q': city_name,
                'appid': self.api_key,
                'units': 'metric',
                'lang': 'zh_cn'
            }
            
            response = requests.get(self.base_url, params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            print(f"获取天气失败: {e}")
            return None
    
    def get_weather_icon(self, description, temp):
        """根据天气状况和温度返回对应的图标"""
        desc_lower = description.lower()
        
        # 雨天图标
        if any(word in desc_lower for word in ['雨', 'rain', '阵雨', 'shower']):
            return '🌧️'
        # 雪天图标
        elif any(word in desc_lower for word in ['雪', 'snow']):
            return '❄️'
        # 雾霾图标
        elif any(word in desc_lower for word in ['雾', 'fog', '霾', 'haze']):
            return '🌫️'
        # 多云图标
        elif any(word in desc_lower for word in ['云', 'cloud', '阴']):
            if temp > 25:
                return '⛅'  # 温暖多云
            else:
                return '☁️'  # 凉爽多云
        # 晴天图标
        elif any(word in desc_lower for word in ['晴', 'clear', 'sunny']):
            if temp > 30:
                return '☀️'  # 炎热晴天
            elif temp > 20:
                return '🌤️'  # 温暖晴天
            else:
                return '🌞'  # 凉爽晴天
        # 默认图标
        else:
            return '🌤️'
    
    def get_clothing_by_temperature(self, temp, feels_like):
        """根据温度获取基础服装建议的工具函数"""
        reference_temp = feels_like
        temp_diff = abs(temp - feels_like)
        
        if reference_temp >= 30:
            extra_note = " 注意温差" if temp_diff > 3 else ""
            return {
                "top": "轻薄透气的短袖T恤或衬衫",
                "bottom": "短裤或轻薄长裤",
                "shoes": "透气凉鞋或轻便运动鞋",
                "accessories": "遮阳帽、太阳镜",
                "reason": f"高温天气需要透气散热{extra_note}"
            }
        elif reference_temp >= 25:
            return {
                "top": "短袖衬衫或薄长袖",
                "bottom": "长裤或裙子",
                "shoes": "休闲鞋或运动鞋",
                "accessories": "轻薄外套备用",
                "reason": "温暖舒适的搭配"
            }
        elif reference_temp >= 20:
            return {
                "top": "长袖衬衫或薄毛衣",
                "bottom": "长裤",
                "shoes": "休闲鞋或靴子",
                "accessories": "轻薄外套",
                "reason": "微凉天气需要适度保暖"
            }
        elif reference_temp >= 15:
            return {
                "top": "毛衣或厚衬衫",
                "bottom": "长裤",
                "shoes": "靴子或厚底鞋",
                "accessories": "夹克或薄外套",
                "reason": "凉爽天气需要保暖"
            }
        elif reference_temp >= 10:
            return {
                "top": "厚毛衣或卫衣",
                "bottom": "厚长裤",
                "shoes": "保暖靴子",
                "accessories": "厚外套或夹克",
                "reason": "寒冷天气需要多层保暖"
            }
        elif reference_temp >= 5:
            return {
                "top": "厚毛衣加保暖内衣",
                "bottom": "厚长裤或保暖裤",
                "shoes": "保暖靴子",
                "accessories": "厚大衣、围巾",
                "reason": "严寒天气需要全面保暖"
            }
        else:
            return {
                "top": "厚羽绒服或棉衣",
                "bottom": "保暖裤或厚长裤",
                "shoes": "防滑保暖靴",
                "accessories": "手套、围巾、帽子",
                "reason": "极寒天气需要防寒装备"
            }

    def get_weather_specific_advice(self, description, wind_speed, humidity):
        """根据具体天气状况获取专门建议的工具函数"""
        advice = []
        
        desc_lower = description.lower()
        if any(word in desc_lower for word in ['雨', 'rain', '阵雨', 'shower']):
            advice.append("☔ 携带雨伞或穿防水外套")
        elif any(word in desc_lower for word in ['雪', 'snow']):
            advice.append("❄️ 穿防滑鞋，注意保暖防湿")
        elif any(word in desc_lower for word in ['雾', 'fog', '霾', 'haze']):
            advice.append("😷 佩戴口罩，注意交通安全")
        elif any(word in desc_lower for word in ['晴', 'clear', 'sunny']):
            advice.append("☀️ 做好防晒措施")
        
        if wind_speed > 10:
            advice.append("💨 穿防风外套，固定帽子")
        elif wind_speed > 5:
            advice.append("🌬️ 注意防风，避免宽松衣物")
        
        if humidity > 80:
            advice.append("💧 选择透气材质，避免闷热")
        elif humidity < 30:
            advice.append("🧴 注意保湿，多喝水")
        
        return advice

    def get_gemini_clothing_advice(self, weather_data):
        """使用Gemini AI生成智能穿衣建议"""
        try:
            if not self.use_gemini:
                return self._fallback_advice_simple(weather_data)
            
            city = weather_data['name']
            country = weather_data['sys']['country']
            temp = weather_data['main']['temp']
            feels_like = weather_data['main']['feels_like']
            humidity = weather_data['main']['humidity']
            description = weather_data['weather'][0]['description']
            wind_speed = weather_data['wind']['speed']
            
            temp_advice = self.get_clothing_by_temperature(temp, feels_like)
            weather_advice = self.get_weather_specific_advice(description, wind_speed, humidity)
            
            prompt = f"""
作为专业时尚顾问，请根据{city}, {country}的天气生成穿衣建议：

天气数据：
- 温度：{temp}°C，体感：{feels_like}°C
- 天气：{description}
- 湿度：{humidity}%，风速：{wind_speed}m/s

基础分析：
- 上衣：{temp_advice['top']}
- 下装：{temp_advice['bottom']}
- 鞋子：{temp_advice['shoes']}
- 配饰：{temp_advice['accessories']}

特殊提醒：{chr(10).join(weather_advice) if weather_advice else '无'}

请生成5条实用建议，格式：emoji + 建议 - 原因（10字内）
"""
            
            response = self.gemini_model.generate_content(prompt)
            
            if response and response.text:
                advice_lines = [line.strip() for line in response.text.strip().split('\n') if line.strip()]
                advice = []
                
                for line in advice_lines:
                    if line and (line.startswith(('1.', '2.', '3.', '4.', '5.')) or 
                               any(emoji in line for emoji in ['👕', '👔', '🧥', '👖', '👗', '👠', '👟', '🧢', '🧤', '🧣', '☂️', '🌂', '😷', '🕶️', '💧', '🌡️', '💨', '☀️', '❄️', '☔']) or
                               ('-' in line and len(line) > 10)):
                        clean_line = line
                        for i in range(1, 10):
                            clean_line = clean_line.replace(f"{i}. ", "").replace(f"{i}.", "")
                        clean_line = clean_line.strip()
                        if clean_line:
                            advice.append(clean_line)
                
                return advice[:5] if advice else self._fallback_advice_simple(weather_data)
            
            return self._fallback_advice_simple(weather_data)
                
        except Exception as e:
            print(f"Gemini调用失败: {e}")
            return self._fallback_advice_simple(weather_data)
    
    def _fallback_advice_simple(self, weather_data):
        """简单回退建议"""
        temp = weather_data['main']['temp']
        feels_like = weather_data['main']['feels_like']
        description = weather_data['weather'][0]['description']
        wind_speed = weather_data['wind']['speed']
        humidity = weather_data['main']['humidity']

        temp_advice = self.get_clothing_by_temperature(temp, feels_like)
        weather_advice = self.get_weather_specific_advice(description, wind_speed, humidity)

        advice = []
        advice.append(f"👕 {temp_advice['top']} - {temp_advice['reason']}")
        advice.append(f"👖 {temp_advice['bottom']} - 舒适搭配")
        advice.append(f"👟 {temp_advice['shoes']} - 适合天气")

        for special_advice in weather_advice[:2]:
            advice.append(special_advice)

        return advice[:5]

    def process_city_name(self, city_name, country, state=None):
        """智能处理城市名称，提高匹配成功率"""
        # state参数预留给未来扩展使用
        _ = state

        # 城市名称映射表 - 将API返回的名称映射到下拉列表中的标准名称
        city_mapping = {
            # 中国城市映射
            'Beijing': 'Beijing',
            'Peking': 'Beijing',
            'Shanghai': 'Shanghai',
            'Guangzhou': 'Guangzhou',
            'Canton': 'Guangzhou',
            'Shenzhen': 'Shenzhen',
            'Hangzhou': 'Hangzhou',
            'Nanjing': 'Nanjing',
            'Nanking': 'Nanjing',
            'Chengdu': 'Chengdu',
            'Wuhan': 'Wuhan',
            'Xi\'an': 'Xi\'an',
            'Xian': 'Xi\'an',
            'Chongqing': 'Chongqing',

            # 国际城市映射
            'Tokyo': 'Tokyo',
            'Seoul': 'Seoul',
            'Singapore': 'Singapore',
            'Bangkok': 'Bangkok',
            'New York': 'New York',
            'New York City': 'New York',
            'NYC': 'New York',
            'London': 'London',
            'Paris': 'Paris',
            'Sydney': 'Sydney',
            'Dubai': 'Dubai',
            'Moscow': 'Moscow',
            'Moskva': 'Moscow'
        }

        # 首先尝试直接映射
        if city_name in city_mapping:
            return city_mapping[city_name]

        # 根据国家代码进行智能推断
        if country == 'CN':  # 中国
            # 中国主要城市的特殊处理
            city_lower = city_name.lower()
            if 'beijing' in city_lower or 'peking' in city_lower:
                return 'Beijing'
            elif 'shanghai' in city_lower:
                return 'Shanghai'
            elif 'guangzhou' in city_lower or 'canton' in city_lower:
                return 'Guangzhou'
            elif 'shenzhen' in city_lower:
                return 'Shenzhen'
            elif 'hangzhou' in city_lower:
                return 'Hangzhou'
            elif 'nanjing' in city_lower or 'nanking' in city_lower:
                return 'Nanjing'
            elif 'chengdu' in city_lower:
                return 'Chengdu'
            elif 'wuhan' in city_lower:
                return 'Wuhan'
            elif 'xian' in city_lower or 'xi\'an' in city_lower:
                return 'Xi\'an'
            elif 'chongqing' in city_lower:
                return 'Chongqing'

        elif country == 'US':  # 美国
            if 'new york' in city_name.lower() or city_name.lower() == 'nyc':
                return 'New York'

        elif country == 'JP':  # 日本
            if 'tokyo' in city_name.lower():
                return 'Tokyo'

        elif country == 'KR':  # 韩国
            if 'seoul' in city_name.lower():
                return 'Seoul'

        elif country == 'SG':  # 新加坡
            return 'Singapore'

        elif country == 'TH':  # 泰国
            if 'bangkok' in city_name.lower():
                return 'Bangkok'

        elif country == 'GB':  # 英国
            if 'london' in city_name.lower():
                return 'London'

        elif country == 'FR':  # 法国
            if 'paris' in city_name.lower():
                return 'Paris'

        elif country == 'AU':  # 澳大利亚
            if 'sydney' in city_name.lower():
                return 'Sydney'

        elif country == 'AE':  # 阿联酋
            if 'dubai' in city_name.lower():
                return 'Dubai'

        elif country == 'RU':  # 俄罗斯
            if 'moscow' in city_name.lower() or 'moskva' in city_name.lower():
                return 'Moscow'

        # 如果没有匹配到，返回原始城市名称
        return city_name

    def get_clothing_recommendations(self, weather_data):
        """根据天气状况生成衣物购买推荐链接"""
        temp = weather_data['main']['temp']
        feels_like = weather_data['main']['feels_like']
        description = weather_data['weather'][0]['description']
        wind_speed = weather_data['wind']['speed']
        humidity = weather_data['main']['humidity']

        # 预留变量供未来扩展使用
        _ = temp, humidity

        recommendations = []

        # 根据体感温度推荐基础衣物
        if feels_like >= 30:
            recommendations.extend([
                {
                    "category": "上衣",
                    "item": "透气短袖T恤",
                    "reason": "高温透气",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=透气短袖T恤",
                        "京东": "https://search.jd.com/Search?keyword=透气短袖T恤",
                        "天猫": "https://list.tmall.com/search_product.htm?q=透气短袖T恤"
                    }
                },
                {
                    "category": "防晒",
                    "item": "遮阳帽",
                    "reason": "防晒必备",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=遮阳帽",
                        "京东": "https://search.jd.com/Search?keyword=遮阳帽",
                        "天猫": "https://list.tmall.com/search_product.htm?q=遮阳帽"
                    }
                }
            ])
        elif feels_like >= 20:
            recommendations.extend([
                {
                    "category": "上衣",
                    "item": "长袖衬衫",
                    "reason": "舒适保暖",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=长袖衬衫",
                        "京东": "https://search.jd.com/Search?keyword=长袖衬衫",
                        "天猫": "https://list.tmall.com/search_product.htm?q=长袖衬衫"
                    }
                },
                {
                    "category": "外套",
                    "item": "薄外套",
                    "reason": "温差防护",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=薄外套",
                        "京东": "https://search.jd.com/Search?keyword=薄外套",
                        "天猫": "https://list.tmall.com/search_product.htm?q=薄外套"
                    }
                }
            ])
        elif feels_like >= 10:
            recommendations.extend([
                {
                    "category": "上衣",
                    "item": "毛衣",
                    "reason": "保暖舒适",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=毛衣",
                        "京东": "https://search.jd.com/Search?keyword=毛衣",
                        "天猫": "https://list.tmall.com/search_product.htm?q=毛衣"
                    }
                },
                {
                    "category": "外套",
                    "item": "厚外套",
                    "reason": "防寒保暖",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=厚外套",
                        "京东": "https://search.jd.com/Search?keyword=厚外套",
                        "天猫": "https://list.tmall.com/search_product.htm?q=厚外套"
                    }
                }
            ])
        else:
            recommendations.extend([
                {
                    "category": "外套",
                    "item": "羽绒服",
                    "reason": "极寒防护",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=羽绒服",
                        "京东": "https://search.jd.com/Search?keyword=羽绒服",
                        "天猫": "https://list.tmall.com/search_product.htm?q=羽绒服"
                    }
                },
                {
                    "category": "配饰",
                    "item": "保暖手套",
                    "reason": "手部保暖",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=保暖手套",
                        "京东": "https://search.jd.com/Search?keyword=保暖手套",
                        "天猫": "https://list.tmall.com/search_product.htm?q=保暖手套"
                    }
                }
            ])

        # 根据天气状况添加特殊推荐
        desc_lower = description.lower()
        if any(word in desc_lower for word in ['雨', 'rain', '阵雨', 'shower']):
            recommendations.append({
                "category": "雨具",
                "item": "雨伞",
                "reason": "雨天必备",
                "links": {
                    "淘宝": "https://s.taobao.com/search?q=雨伞",
                    "京东": "https://search.jd.com/Search?keyword=雨伞",
                    "天猫": "https://list.tmall.com/search_product.htm?q=雨伞"
                }
            })
        elif any(word in desc_lower for word in ['雪', 'snow']):
            recommendations.append({
                "category": "鞋子",
                "item": "防滑靴",
                "reason": "雪天防滑",
                "links": {
                    "淘宝": "https://s.taobao.com/search?q=防滑靴",
                    "京东": "https://search.jd.com/Search?keyword=防滑靴",
                    "天猫": "https://list.tmall.com/search_product.htm?q=防滑靴"
                }
            })
        elif any(word in desc_lower for word in ['晴', 'clear', 'sunny']) and feels_like > 25:
            recommendations.append({
                "category": "防晒",
                "item": "防晒霜",
                "reason": "紫外线防护",
                "links": {
                    "淘宝": "https://s.taobao.com/search?q=防晒霜",
                    "京东": "https://search.jd.com/Search?keyword=防晒霜",
                    "天猫": "https://list.tmall.com/search_product.htm?q=防晒霜"
                }
            })

        # 根据风速推荐
        if wind_speed > 10:
            recommendations.append({
                "category": "外套",
                "item": "防风衣",
                "reason": "大风防护",
                "links": {
                    "淘宝": "https://s.taobao.com/search?q=防风衣",
                    "京东": "https://search.jd.com/Search?keyword=防风衣",
                    "天猫": "https://list.tmall.com/search_product.htm?q=防风衣"
                }
            })

        return recommendations[:4]  # 最多返回4个推荐

    def get_travel_recommendations(self, city, month=None):
        """根据城市和月份获取旅行推荐"""
        import datetime
        if month is None:
            month = datetime.datetime.now().month

        # 旅行推荐数据库
        travel_data = {
            # 周边推荐（基于主要城市）
            "nearby": {
                "Beijing": [
                    {"name": "承德避暑山庄", "description": "清朝皇家园林，夏季避暑胜地", "icon": "🏯", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "image": "https://source.unsplash.com/400x300/?chengde,imperial,palace,garden", "distance": "230km", "travel_time": "3小时"},
                    {"name": "天津海河", "description": "现代都市风光，夜景迷人", "icon": "🌉", "color": "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)", "image": "https://source.unsplash.com/400x300/?tianjin,river,city,night", "distance": "120km", "travel_time": "1.5小时"},
                    {"name": "秦皇岛北戴河", "description": "海滨度假胜地，沙滩阳光", "icon": "🏖️", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "image": "https://source.unsplash.com/400x300/?beidaihe,beach,seaside,china", "distance": "280km", "travel_time": "3.5小时"}
                ],
                "Shanghai": [
                    {"name": "苏州园林", "description": "江南古典园林，诗情画意", "icon": "🏮", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://source.unsplash.com/400x300/?suzhou,garden,classical,chinese,pavilion", "distance": "100km", "travel_time": "1小时"},
                    {"name": "杭州西湖", "description": "人间天堂，湖光山色", "icon": "🌸", "color": "linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)", "image": "https://source.unsplash.com/400x300/?westlake,hangzhou,pagoda,lake,willow", "distance": "180km", "travel_time": "2小时"},
                    {"name": "南京夫子庙", "description": "六朝古都，历史文化", "icon": "🏛️", "color": "linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)", "image": "https://source.unsplash.com/400x300/?confucius,temple,nanjing,traditional,architecture", "distance": "300km", "travel_time": "3小时"}
                ],
                "Guangzhou": [
                    {"name": "深圳世界之窗", "description": "世界景观微缩景区", "icon": "🌍", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://picsum.photos/400/300?random=10", "distance": "120km", "travel_time": "1.5小时"},
                    {"name": "珠海长隆", "description": "海洋主题乐园", "icon": "🐋", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://picsum.photos/400/300?random=11", "distance": "150km", "travel_time": "2小时"},
                    {"name": "佛山祖庙", "description": "岭南古建筑群", "icon": "🏮", "color": "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)", "image": "https://picsum.photos/400/300?random=12", "distance": "50km", "travel_time": "1小时"}
                ],
                "Tokyo": [
                    {"name": "富士山", "description": "日本最高峰，神圣象征", "icon": "🗻", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://picsum.photos/400/300?random=13", "distance": "100km", "travel_time": "2小时"},
                    {"name": "镰仓大佛", "description": "古都镰仓，历史文化", "icon": "🧘", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://picsum.photos/400/300?random=14", "distance": "50km", "travel_time": "1小时"},
                    {"name": "箱根温泉", "description": "温泉胜地，自然风光", "icon": "♨️", "color": "linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)", "image": "https://picsum.photos/400/300?random=15", "distance": "80km", "travel_time": "1.5小时"}
                ],
                "Hangzhou": [
                    {"name": "千岛湖", "description": "湖光山色，度假胜地", "icon": "🏞️", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "image": "https://picsum.photos/400/300?random=16", "distance": "150km", "travel_time": "2小时"},
                    {"name": "乌镇水乡", "description": "江南水乡，古镇风情", "icon": "🛶", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://picsum.photos/400/300?random=17", "distance": "60km", "travel_time": "1小时"},
                    {"name": "莫干山", "description": "避暑胜地，民宿天堂", "icon": "🌲", "color": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "image": "https://picsum.photos/400/300?random=18", "distance": "70km", "travel_time": "1.5小时"}
                ],
                "Chongqing": [
                    {"name": "大足石刻", "description": "世界文化遗产，石雕艺术", "icon": "🗿", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "image": "https://source.unsplash.com/400x300/?dazu,stone,carving,buddha,sculpture", "distance": "80km", "travel_time": "1.5小时"},
                    {"name": "武隆天生三桥", "description": "自然奇观，喀斯特地貌", "icon": "🌉", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://source.unsplash.com/400x300/?wulong,natural,bridge,karst,canyon", "distance": "120km", "travel_time": "2小时"},
                    {"name": "合川钓鱼城", "description": "古战场遗址，历史文化", "icon": "🏰", "color": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "image": "https://source.unsplash.com/400x300/?ancient,fortress,castle,historical,china", "distance": "60km", "travel_time": "1小时"}
                ],
                "Chengdu": [
                    {"name": "都江堰", "description": "古代水利工程，世界遗产", "icon": "🌊", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "image": "https://source.unsplash.com/400x300/?dujiangyan,irrigation,ancient,water,engineering", "distance": "60km", "travel_time": "1小时"},
                    {"name": "青城山", "description": "道教名山，清幽秀美", "icon": "⛰️", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://source.unsplash.com/400x300/?qingcheng,mountain,taoist,temple,forest", "distance": "70km", "travel_time": "1.5小时"},
                    {"name": "峨眉山", "description": "佛教圣地，金顶云海", "icon": "🏔️", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://source.unsplash.com/400x300/?emei,mountain,buddhist,temple,golden,summit", "distance": "150km", "travel_time": "2.5小时"}
                ],
                "Xi'an": [
                    {"name": "华山", "description": "五岳之一，险峻奇峰", "icon": "⛰️", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "image": "https://picsum.photos/400/300?random=19", "distance": "120km", "travel_time": "2小时"},
                    {"name": "法门寺", "description": "佛教圣地，舍利宝塔", "icon": "🛕", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://picsum.photos/400/300?random=20", "distance": "120km", "travel_time": "2小时"},
                    {"name": "乾陵", "description": "唐代皇陵，武则天陵墓", "icon": "🏛️", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://picsum.photos/400/300?random=21", "distance": "80km", "travel_time": "1.5小时"}
                ],
                "Wuhan": [
                    {"name": "黄鹤楼", "description": "江南三大名楼，诗词文化", "icon": "🏯", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://picsum.photos/400/300?random=22", "distance": "市内", "travel_time": "30分钟"},
                    {"name": "木兰天池", "description": "山水风光，避暑胜地", "icon": "🏞️", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://picsum.photos/400/300?random=23", "distance": "60km", "travel_time": "1小时"},
                    {"name": "东湖樱花园", "description": "樱花盛开，湖光山色", "icon": "🌸", "color": "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)", "image": "https://picsum.photos/400/300?random=24", "distance": "15km", "travel_time": "30分钟"}
                ],
                "Nanjing": [
                    {"name": "中山陵", "description": "国父陵墓，庄严肃穆", "icon": "🏛️", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "distance": "15km", "travel_time": "30分钟"},
                    {"name": "扬州瘦西湖", "description": "园林艺术，诗画江南", "icon": "🌸", "color": "linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)", "distance": "100km", "travel_time": "1.5小时"},
                    {"name": "镇江金山寺", "description": "佛教名刹，长江风光", "icon": "🛕", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "distance": "80km", "travel_time": "1小时"}
                ],
                "Tianjin": [
                    {"name": "盘山", "description": "京东第一山，自然风光", "icon": "⛰️", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "distance": "90km", "travel_time": "1.5小时"},
                    {"name": "黄崖关长城", "description": "明代长城，险要关隘", "icon": "🏯", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "distance": "100km", "travel_time": "2小时"},
                    {"name": "独乐寺", "description": "千年古刹，辽代建筑", "icon": "🛕", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "distance": "80km", "travel_time": "1.5小时"}
                ],
                "Shenzhen": [
                    {"name": "大梅沙海滨", "description": "黄金海岸，海滨度假", "icon": "🏖️", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "distance": "30km", "travel_time": "45分钟"},
                    {"name": "梧桐山", "description": "深圳第一峰，登高望远", "icon": "🏔️", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "distance": "25km", "travel_time": "40分钟"},
                    {"name": "大鹏古城", "description": "明清古城，历史文化", "icon": "🏰", "color": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "distance": "60km", "travel_time": "1小时"}
                ]
            },

            # 国内推荐（按月份）
            "domestic": {
                1: [  # 1月
                    {"name": "哈尔滨冰雪大世界", "city": "哈尔滨", "description": "冰雪艺术的殿堂", "icon": "❄️", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "image": "https://source.unsplash.com/400x300/?harbin,ice,snow,festival,sculpture", "reason": "冬季冰雪节最佳时期"},
                    {"name": "海南三亚", "city": "三亚", "description": "热带海滨度假天堂", "icon": "🏝️", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "image": "https://source.unsplash.com/400x300/?sanya,tropical,beach,palm,hainan", "reason": "避寒度假的理想选择"},
                    {"name": "云南大理", "city": "大理", "description": "风花雪月的浪漫古城", "icon": "🌺", "color": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "image": "https://source.unsplash.com/400x300/?dali,yunnan,ancient,town,erhai", "reason": "四季如春，冬季温暖"}
                ],
                7: [  # 7月
                    {"name": "青海湖", "city": "西宁", "description": "高原明珠，油菜花海", "icon": "🌻", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://source.unsplash.com/400x300/?qinghai,lake,rapeseed,flower,plateau", "reason": "夏季油菜花盛开"},
                    {"name": "内蒙古草原", "city": "呼和浩特", "description": "辽阔草原，牧歌悠扬", "icon": "🐎", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://source.unsplash.com/400x300/?inner,mongolia,grassland,horse,prairie", "reason": "草原最美的季节"},
                    {"name": "新疆伊犁", "city": "伊宁", "description": "薰衣草花海，天山雪峰", "icon": "💜", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://source.unsplash.com/400x300/?xinjiang,lavender,field,tianshan,mountain", "reason": "薰衣草花期"}
                ]
            },

            # 国外推荐（按月份）
            "international": {
                1: [  # 1月
                    {"name": "日本北海道", "city": "札幌", "description": "雪国风情，温泉滑雪", "icon": "🎿", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "image": "https://source.unsplash.com/400x300/?hokkaido,snow,skiing,onsen,winter", "reason": "冬季雪景和温泉"},
                    {"name": "泰国清迈", "city": "清迈", "description": "古城文化，热带风情", "icon": "🛕", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://source.unsplash.com/400x300/?chiang,mai,temple,thailand,buddhist", "reason": "凉季气候宜人"},
                    {"name": "新西兰南岛", "city": "皇后镇", "description": "纯净自然，户外天堂", "icon": "🏔️", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://source.unsplash.com/400x300/?queenstown,new,zealand,mountain,lake", "reason": "南半球夏季"}
                ],
                7: [  # 7月
                    {"name": "法国普罗旺斯", "city": "阿维尼翁", "description": "薰衣草田，浪漫法式", "icon": "🌾", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://source.unsplash.com/400x300/?provence,lavender,field,france,purple", "reason": "薰衣草盛开季节"},
                    {"name": "挪威峡湾", "city": "卑尔根", "description": "壮美峡湾，午夜阳光", "icon": "⛵", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "image": "https://source.unsplash.com/400x300/?norway,fjord,bergen,waterfall,mountain", "reason": "白夜和峡湾最美时节"},
                    {"name": "土耳其卡帕多奇亚", "city": "格雷梅", "description": "奇特地貌，热气球之旅", "icon": "🎈", "color": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "image": "https://source.unsplash.com/400x300/?cappadocia,hot,air,balloon,turkey", "reason": "夏季天气稳定"}
                ]
            }
        }

        # 获取周边推荐（智能匹配城市）
        # 城市名称映射表（支持中英文匹配）
        city_mapping = {
            "重庆": "Chongqing",
            "chongqing": "Chongqing",
            "成都": "Chengdu",
            "chengdu": "Chengdu",
            "西安": "Xi'an",
            "xian": "Xi'an",
            "xi'an": "Xi'an",
            "武汉": "Wuhan",
            "wuhan": "Wuhan",
            "南京": "Nanjing",
            "nanjing": "Nanjing",
            "天津": "Tianjin",
            "tianjin": "Tianjin",
            "深圳": "Shenzhen",
            "shenzhen": "Shenzhen",
            "北京": "Beijing",
            "beijing": "Beijing",
            "上海": "Shanghai",
            "shanghai": "Shanghai",
            "广州": "Guangzhou",
            "guangzhou": "Guangzhou",
            "杭州": "Hangzhou",
            "hangzhou": "Hangzhou",
            "东京": "Tokyo",
            "tokyo": "Tokyo"
        }

        # 标准化城市名称
        city_key = city_mapping.get(city.lower(), city)

        nearby = travel_data["nearby"].get(city_key)
        if not nearby:
            # 尝试匹配相似城市名
            city_lower = city.lower()
            for key in travel_data["nearby"].keys():
                if key.lower() in city_lower or city_lower in key.lower():
                    nearby = travel_data["nearby"][key]
                    break

            # 如果还是没找到，使用默认推荐
            if not nearby:
                nearby = travel_data["nearby"]["Beijing"]

        # 获取国内推荐（根据月份，如果没有对应月份则使用7月）
        domestic = travel_data["domestic"].get(month, travel_data["domestic"][7])

        # 获取国外推荐（根据月份，如果没有对应月份则使用7月）
        international = travel_data["international"].get(month, travel_data["international"][7])

        # 为国内游和出境游添加机票价格信息
        # 标准化出发城市名称
        origin_city = self.normalize_city_name(city)

        for item in domestic:
            flight_info = self.get_flight_prices(origin_city, item["city"], is_international=False)
            item["flight_price"] = flight_info

        for item in international:
            flight_info = self.get_flight_prices(origin_city, item["city"], is_international=True)
            item["flight_price"] = flight_info

        return {
            "nearby": nearby,
            "domestic": domestic,
            "international": international,
            "month": month
        }

    def get_fallback_image_url(self, category, name):
        """获取备用图片URL"""
        # 使用更稳定的图片服务作为备用
        fallback_images = {
            # 周边景点备用图片
            "承德避暑山庄": "https://source.unsplash.com/400x200/?palace,garden",
            "天津海河": "https://source.unsplash.com/400x200/?river,city",
            "秦皇岛北戴河": "https://source.unsplash.com/400x200/?beach,seaside",
            "苏州园林": "https://source.unsplash.com/400x200/?garden,traditional",
            "杭州西湖": "https://source.unsplash.com/400x200/?lake,scenic",
            "南京夫子庙": "https://source.unsplash.com/400x200/?temple,traditional",

            # 国内景点备用图片
            "哈尔滨冰雪大世界": "https://source.unsplash.com/400x200/?ice,snow",
            "海南三亚": "https://source.unsplash.com/400x200/?tropical,beach",
            "云南大理": "https://source.unsplash.com/400x200/?mountain,ancient",
            "青海湖": "https://source.unsplash.com/400x200/?lake,plateau",
            "内蒙古草原": "https://source.unsplash.com/400x200/?grassland,prairie",
            "新疆伊犁": "https://source.unsplash.com/400x200/?lavender,mountain",

            # 国外景点备用图片
            "日本北海道": "https://source.unsplash.com/400x200/?japan,snow",
            "泰国清迈": "https://source.unsplash.com/400x200/?thailand,temple",
            "新西兰南岛": "https://source.unsplash.com/400x200/?newzealand,nature",
            "法国普罗旺斯": "https://source.unsplash.com/400x200/?provence,lavender",
            "挪威峡湾": "https://source.unsplash.com/400x200/?norway,fjord",
            "土耳其卡帕多奇亚": "https://source.unsplash.com/400x200/?cappadocia,balloon"
        }

        return fallback_images.get(name, f"https://source.unsplash.com/400x200/?{category},travel")

    def normalize_city_name(self, city_name):
        """标准化城市名称，用于机票价格查询"""
        # 城市名称映射表
        city_mapping = {
            # 英文到中文
            "Beijing": "北京",
            "Shanghai": "上海",
            "Guangzhou": "广州",
            "Shenzhen": "深圳",
            "Hangzhou": "杭州",
            "Chengdu": "成都",
            "Xi'an": "西安",
            "Wuhan": "武汉",
            "Nanjing": "南京",
            "Chongqing": "重庆",
            "Tianjin": "天津",
            "Qingdao": "青岛",
            "Dalian": "大连",
            "Xiamen": "厦门",
            "Kunming": "昆明",
            "Urumqi": "乌鲁木齐",
            "Lhasa": "拉萨",
            "Hohhot": "呼和浩特",
            "Taiyuan": "太原",
            "Shijiazhuang": "石家庄",

            # 国际城市保持英文
            "Tokyo": "Tokyo",
            "Osaka": "Osaka",
            "Seoul": "Seoul",
            "Bangkok": "Bangkok",
            "Singapore": "Singapore",
            "Kuala Lumpur": "Kuala Lumpur",
            "Manila": "Manila",
            "Jakarta": "Jakarta",
            "Ho Chi Minh City": "Ho Chi Minh City",
            "Phnom Penh": "Phnom Penh"
        }

        # 如果在映射表中，返回映射后的名称
        if city_name in city_mapping:
            return city_mapping[city_name]

        # 如果不在映射表中，返回原名称
        return city_name

    def get_flight_prices(self, origin_city, destination_city, is_international=False):
        """获取机票价格信息"""
        import random
        import datetime

        # 城市到机场代码的映射
        city_to_airport = {
            # 国内城市（中文）
            "北京": "PEK",
            "上海": "PVG",
            "广州": "CAN",
            "深圳": "SZX",
            "杭州": "HGH",
            "成都": "CTU",
            "西安": "XIY",
            "武汉": "WUH",
            "南京": "NKG",
            "重庆": "CKG",
            "天津": "TSN",
            "青岛": "TAO",
            "大连": "DLC",
            "厦门": "XMN",
            "昆明": "KMG",

            # 国内城市（英文）
            "Beijing": "PEK",
            "Shanghai": "PVG",
            "Guangzhou": "CAN",
            "Shenzhen": "SZX",
            "Hangzhou": "HGH",
            "Chengdu": "CTU",
            "Xi'an": "XIY",
            "Wuhan": "WUH",
            "Nanjing": "NKG",
            "Chongqing": "CKG",

            # 国内目的地
            "哈尔滨": "HRB",
            "三亚": "SYX",
            "大理": "DLU",
            "西宁": "XNN",
            "呼和浩特": "HET",
            "伊宁": "YIN",

            # 国际目的地
            "札幌": "CTS",
            "清迈": "CNX",
            "皇后镇": "ZQN",
            "阿维尼翁": "AVN",
            "卑尔根": "BGO",
            "格雷梅": "NAV",

            # 国际城市
            "Tokyo": "NRT",
            "Osaka": "KIX",
            "Seoul": "ICN",
            "Bangkok": "BKK",
            "Singapore": "SIN"
        }

        # 获取机场代码
        origin_code = city_to_airport.get(origin_city, "PEK")
        dest_code = city_to_airport.get(destination_city, "PVG")

        # 模拟机票价格数据（根据出发城市和目的地计算）
        def calculate_base_price(origin, destination, is_intl):
            # 国内航线基础价格（从主要城市出发）
            domestic_prices = {
                ("北京", "哈尔滨"): 800, ("上海", "哈尔滨"): 1200, ("广州", "哈尔滨"): 1800,
                ("北京", "三亚"): 1500, ("上海", "三亚"): 1300, ("广州", "三亚"): 800,
                ("北京", "大理"): 1400, ("上海", "大理"): 1200, ("广州", "大理"): 600,
                ("北京", "西宁"): 900, ("上海", "西宁"): 1100, ("广州", "西宁"): 1400,
                ("北京", "呼和浩特"): 500, ("上海", "呼和浩特"): 800, ("广州", "呼和浩特"): 1200,
                ("北京", "伊宁"): 1800, ("上海", "伊宁"): 2200, ("广州", "伊宁"): 2500
            }

            # 国际航线基础价格
            international_prices = {
                ("北京", "札幌"): 2800, ("上海", "札幌"): 2500, ("广州", "札幌"): 3200,
                ("北京", "清迈"): 2200, ("上海", "清迈"): 2000, ("广州", "清迈"): 1500,
                ("北京", "皇后镇"): 7500, ("上海", "皇后镇"): 7200, ("广州", "皇后镇"): 7800,
                ("北京", "阿维尼翁"): 5800, ("上海", "阿维尼翁"): 5500, ("广州", "阿维尼翁"): 6200,
                ("北京", "卑尔根"): 6500, ("上海", "卑尔根"): 6200, ("广州", "卑尔根"): 6800,
                ("北京", "格雷梅"): 4200, ("上海", "格雷梅"): 4000, ("广州", "格雷梅"): 4500
            }

            if is_intl:
                return international_prices.get((origin, destination), 5000)
            else:
                return domestic_prices.get((origin, destination), 1200)

        base_price = calculate_base_price(origin_city, destination_city, is_international)

        # 生成未来30天的价格数据
        today = datetime.date.today()
        price_data = []

        for i in range(30):
            date = today + datetime.timedelta(days=i)

            # 模拟价格波动（周末和节假日价格较高）
            price_multiplier = 1.0

            # 周末价格上涨
            if date.weekday() >= 5:  # 周六、周日
                price_multiplier += 0.2

            # 随机波动
            price_multiplier += random.uniform(-0.3, 0.4)
            price_multiplier = max(0.6, min(1.8, price_multiplier))  # 限制在0.6-1.8倍之间

            final_price = int(base_price * price_multiplier)

            price_data.append({
                "date": date.strftime("%Y-%m-%d"),
                "price": final_price,
                "day_of_week": date.strftime("%A"),
                "is_weekend": date.weekday() >= 5
            })

        # 找出最便宜的价格
        cheapest = min(price_data, key=lambda x: x["price"])

        # 计算平均价格
        avg_price = int(sum(p["price"] for p in price_data) / len(price_data))

        return {
            "origin": origin_city,
            "destination": destination_city,
            "origin_airport": origin_code,
            "destination_airport": dest_code,
            "cheapest_price": cheapest["price"],
            "cheapest_date": cheapest["date"],
            "average_price": avg_price,
            "currency": "CNY",
            "search_period": "30天",
            "price_trend": "stable" if abs(cheapest["price"] - avg_price) < avg_price * 0.2 else "volatile",
            "savings": max(0, avg_price - cheapest["price"]),
            "all_prices": price_data[:7]  # 返回前7天的价格供图表显示
        }

# 创建WeatherApp实例
weather_app = WeatherApp()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/location/<float:lat>/<float:lon>')
def get_location_info(lat, lon):
    """API端点：根据坐标获取城市信息"""
    try:
        # 使用OpenWeatherMap的反向地理编码API
        url = f"http://api.openweathermap.org/geo/1.0/reverse"
        params = {
            'lat': lat,
            'lon': lon,
            'limit': 1,
            'appid': weather_app.api_key
        }

        response = requests.get(url, params=params)

        if response.status_code == 200:
            data = response.json()
            if data:
                location = data[0]
                city_name = location.get('name', '')
                country = location.get('country', '')
                state = location.get('state', '')

                # 城市名称智能处理
                processed_city = weather_app.process_city_name(city_name, country, state)

                return jsonify({
                    'city': processed_city,
                    'original_city': city_name,
                    'country': country,
                    'state': state,
                    'lat': lat,
                    'lon': lon
                })
            else:
                return jsonify({'error': '无法找到对应的城市'}), 404
        else:
            return jsonify({'error': '地理编码服务不可用'}), 503

    except Exception as e:
        print(f"地理位置解析失败: {e}")
        return jsonify({'error': '位置解析失败'}), 500

@app.route('/api/weather/coords/<float:lat>/<float:lon>')
def get_weather_by_coords(lat, lon):
    """API端点：根据坐标获取天气和建议"""
    try:
        # 使用坐标直接获取天气
        params = {
            'lat': lat,
            'lon': lon,
            'appid': weather_app.api_key,
            'units': 'metric',
            'lang': 'zh_cn'
        }

        response = requests.get(weather_app.base_url, params=params)

        if response.status_code == 200:
            weather_data = response.json()

            # 获取天气图标
            temp = weather_data['main']['temp']
            description = weather_data['weather'][0]['description']
            weather_icon = weather_app.get_weather_icon(description, temp)

            # 获取穿衣建议
            clothing_advice = weather_app.get_gemini_clothing_advice(weather_data)

            # 获取衣物购买推荐
            clothing_recommendations = weather_app.get_clothing_recommendations(weather_data)

            # 获取旅行推荐（使用城市名称）
            travel_recommendations = weather_app.get_travel_recommendations(weather_data['name'])

            # 构建响应数据
            response_data = {
                'city': weather_data['name'],
                'country': weather_data['sys']['country'],
                'temperature': temp,
                'feels_like': weather_data['main']['feels_like'],
                'description': description,
                'humidity': weather_data['main']['humidity'],
                'pressure': weather_data['main']['pressure'],
                'wind_speed': weather_data['wind']['speed'],
                'weather_icon': weather_icon,
                'clothing_advice': clothing_advice,
                'clothing_recommendations': clothing_recommendations,
                'travel_recommendations': travel_recommendations,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return jsonify(response_data)
        else:
            return jsonify({'error': '无法获取天气信息'}), 404

    except Exception as e:
        print(f"坐标天气获取失败: {e}")
        return jsonify({'error': '天气获取失败'}), 500

@app.route('/api/weather/<city>')
def get_weather_api(city):
    """API端点：获取城市天气和建议"""
    weather_data = weather_app.get_weather(city)

    if not weather_data:
        return jsonify({'error': '无法获取天气信息'}), 404
    
    # 获取天气图标
    temp = weather_data['main']['temp']
    description = weather_data['weather'][0]['description']
    weather_icon = weather_app.get_weather_icon(description, temp)
    
    # 获取穿衣建议
    clothing_advice = weather_app.get_gemini_clothing_advice(weather_data)

    # 获取衣物购买推荐
    clothing_recommendations = weather_app.get_clothing_recommendations(weather_data)

    # 获取旅行推荐
    travel_recommendations = weather_app.get_travel_recommendations(city)

    # 构建响应数据
    response_data = {
        'city': weather_data['name'],
        'country': weather_data['sys']['country'],
        'temperature': temp,
        'feels_like': weather_data['main']['feels_like'],
        'description': description,
        'humidity': weather_data['main']['humidity'],
        'pressure': weather_data['main']['pressure'],
        'wind_speed': weather_data['wind']['speed'],
        'weather_icon': weather_icon,
        'clothing_advice': clothing_advice,
        'clothing_recommendations': clothing_recommendations,
        'travel_recommendations': travel_recommendations,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    return jsonify(response_data)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
