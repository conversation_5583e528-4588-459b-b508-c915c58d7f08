from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import requests
from datetime import datetime, timedelta
import json
import random
import yfinance as yf
import google.generativeai as genai

app = Flask(__name__)
CORS(app)  # 启用CORS支持

class StockApp:
    def __init__(self):
        # API密钥配置
        self.gemini_api_key = "AIzaSyBqRUjcDrplOUpJBNc3PtbmFDii3p8lR4M"
        self.news_api_key = "YOUR_NEWS_API_KEY"  # 可以使用NewsAPI或其他新闻API
        self.setup_gemini()

        # 热门美股列表
        self.popular_stocks = {
            'AAPL': {'name': 'Apple Inc.', 'sector': 'Technology'},
            'MSFT': {'name': 'Microsoft Corporation', 'sector': 'Technology'},
            'GOOGL': {'name': 'Alphabet Inc.', 'sector': 'Technology'},
            'AMZN': {'name': 'Amazon.com Inc.', 'sector': 'Consumer Discretionary'},
            'TSLA': {'name': 'Tesla Inc.', 'sector': 'Consumer Discretionary'},
            'META': {'name': 'Meta Platforms Inc.', 'sector': 'Technology'},
            'NVDA': {'name': 'NVIDIA Corporation', 'sector': 'Technology'},
            'NFLX': {'name': 'Netflix Inc.', 'sector': 'Communication Services'},
            'AMD': {'name': 'Advanced Micro Devices', 'sector': 'Technology'},
            'CRM': {'name': 'Salesforce Inc.', 'sector': 'Technology'},
            'ORCL': {'name': 'Oracle Corporation', 'sector': 'Technology'},
            'ADBE': {'name': 'Adobe Inc.', 'sector': 'Technology'},
            'PYPL': {'name': 'PayPal Holdings Inc.', 'sector': 'Financial Services'},
            'INTC': {'name': 'Intel Corporation', 'sector': 'Technology'},
            'CSCO': {'name': 'Cisco Systems Inc.', 'sector': 'Technology'},
            'PEP': {'name': 'PepsiCo Inc.', 'sector': 'Consumer Staples'},
            'KO': {'name': 'The Coca-Cola Company', 'sector': 'Consumer Staples'},
            'DIS': {'name': 'The Walt Disney Company', 'sector': 'Communication Services'},
            'BA': {'name': 'The Boeing Company', 'sector': 'Industrials'},
            'JPM': {'name': 'JPMorgan Chase & Co.', 'sector': 'Financial Services'}
        }

        # 扩展股票数据库，包含更多公司的别名和关键词
        self.stock_database = {
            # Technology
            'AAPL': {'name': 'Apple Inc.', 'aliases': ['apple', '苹果', 'iphone', 'mac'], 'sector': 'Technology'},
            'MSFT': {'name': 'Microsoft Corporation', 'aliases': ['microsoft', '微软', 'windows', 'office'], 'sector': 'Technology'},
            'GOOGL': {'name': 'Alphabet Inc.', 'aliases': ['google', '谷歌', 'alphabet', 'youtube'], 'sector': 'Technology'},
            'AMZN': {'name': 'Amazon.com Inc.', 'aliases': ['amazon', '亚马逊', 'aws'], 'sector': 'Consumer Discretionary'},
            'TSLA': {'name': 'Tesla Inc.', 'aliases': ['tesla', '特斯拉', 'elon', 'musk'], 'sector': 'Consumer Discretionary'},
            'META': {'name': 'Meta Platforms Inc.', 'aliases': ['meta', 'facebook', '脸书', 'instagram', 'whatsapp'], 'sector': 'Technology'},
            'NVDA': {'name': 'NVIDIA Corporation', 'aliases': ['nvidia', '英伟达', 'gpu', 'ai'], 'sector': 'Technology'},
            'NFLX': {'name': 'Netflix Inc.', 'aliases': ['netflix', '奈飞', 'streaming'], 'sector': 'Communication Services'},
            'AMD': {'name': 'Advanced Micro Devices', 'aliases': ['amd', '超微', 'ryzen'], 'sector': 'Technology'},
            'CRM': {'name': 'Salesforce Inc.', 'aliases': ['salesforce', 'crm'], 'sector': 'Technology'},
            'ORCL': {'name': 'Oracle Corporation', 'aliases': ['oracle', '甲骨文', 'database'], 'sector': 'Technology'},
            'ADBE': {'name': 'Adobe Inc.', 'aliases': ['adobe', 'photoshop', 'pdf'], 'sector': 'Technology'},
            'PYPL': {'name': 'PayPal Holdings Inc.', 'aliases': ['paypal', '贝宝', 'payment'], 'sector': 'Financial Services'},
            'INTC': {'name': 'Intel Corporation', 'aliases': ['intel', '英特尔', 'cpu'], 'sector': 'Technology'},
            'CSCO': {'name': 'Cisco Systems Inc.', 'aliases': ['cisco', '思科', 'network'], 'sector': 'Technology'},
            # Consumer
            'PEP': {'name': 'PepsiCo Inc.', 'aliases': ['pepsi', '百事', 'cola'], 'sector': 'Consumer Staples'},
            'KO': {'name': 'The Coca-Cola Company', 'aliases': ['coca cola', '可口可乐', 'coke'], 'sector': 'Consumer Staples'},
            'DIS': {'name': 'The Walt Disney Company', 'aliases': ['disney', '迪士尼', 'mickey'], 'sector': 'Communication Services'},
            # Industrial & Financial
            'BA': {'name': 'The Boeing Company', 'aliases': ['boeing', '波音', 'aircraft'], 'sector': 'Industrials'},
            'JPM': {'name': 'JPMorgan Chase & Co.', 'aliases': ['jpmorgan', 'chase', '摩根大通'], 'sector': 'Financial Services'},
            # Additional popular stocks
            'V': {'name': 'Visa Inc.', 'aliases': ['visa', '维萨', 'credit card'], 'sector': 'Financial Services'},
            'MA': {'name': 'Mastercard Inc.', 'aliases': ['mastercard', '万事达', 'payment'], 'sector': 'Financial Services'},
            'UNH': {'name': 'UnitedHealth Group Inc.', 'aliases': ['unitedhealth', 'healthcare'], 'sector': 'Healthcare'},
            'JNJ': {'name': 'Johnson & Johnson', 'aliases': ['johnson', 'jnj', '强生'], 'sector': 'Healthcare'},
            'WMT': {'name': 'Walmart Inc.', 'aliases': ['walmart', '沃尔玛', 'retail'], 'sector': 'Consumer Staples'},
            'PG': {'name': 'Procter & Gamble Co.', 'aliases': ['procter', 'gamble', '宝洁'], 'sector': 'Consumer Staples'},
            'HD': {'name': 'The Home Depot Inc.', 'aliases': ['home depot', '家得宝'], 'sector': 'Consumer Discretionary'},
            'BAC': {'name': 'Bank of America Corp.', 'aliases': ['bank of america', '美国银行'], 'sector': 'Financial Services'},
            'XOM': {'name': 'Exxon Mobil Corporation', 'aliases': ['exxon', 'mobil', '埃克森美孚'], 'sector': 'Energy'},
            'CVX': {'name': 'Chevron Corporation', 'aliases': ['chevron', '雪佛龙'], 'sector': 'Energy'}
        }
    
    def setup_gemini(self):
        """配置Gemini API"""
        try:
            if self.gemini_api_key and self.gemini_api_key != "YOUR_GEMINI_API_KEY_HERE":
                genai.configure(api_key=self.gemini_api_key)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                self.use_gemini = True
            else:
                self.use_gemini = False
        except Exception as e:
            print(f"Gemini配置失败: {e}")
            self.use_gemini = False

    def search_stocks(self, query):
        """智能股票搜索功能"""
        query = query.lower().strip()
        results = []

        # 直接匹配股票代码
        if query.upper() in self.stock_database:
            stock_info = self.stock_database[query.upper()]
            results.append({
                'symbol': query.upper(),
                'name': stock_info['name'],
                'sector': stock_info['sector'],
                'match_type': 'exact_symbol'
            })
            return results

        # 搜索匹配的股票
        for symbol, info in self.stock_database.items():
            # 检查公司名称匹配
            if query in info['name'].lower():
                results.append({
                    'symbol': symbol,
                    'name': info['name'],
                    'sector': info['sector'],
                    'match_type': 'company_name'
                })
                continue

            # 检查别名匹配
            for alias in info['aliases']:
                if query in alias.lower() or alias.lower() in query:
                    results.append({
                        'symbol': symbol,
                        'name': info['name'],
                        'sector': info['sector'],
                        'match_type': 'alias'
                    })
                    break

        # 去重并按匹配类型排序
        seen = set()
        unique_results = []
        for result in results:
            if result['symbol'] not in seen:
                seen.add(result['symbol'])
                unique_results.append(result)

        # 排序：精确匹配 > 公司名称 > 别名
        sort_order = {'exact_symbol': 0, 'company_name': 1, 'alias': 2}
        unique_results.sort(key=lambda x: sort_order.get(x['match_type'], 3))

        return unique_results[:10]  # 返回最多10个结果

    def get_stock_data(self, symbol, period='1mo'):
        """获取股票数据，支持不同时间段"""
        try:
            # 使用yfinance获取股票数据
            stock = yf.Ticker(symbol)

            # 获取基本信息
            info = stock.info

            # 根据period参数获取不同时间段的历史数据
            period_map = {
                '1d': '1d',      # 1天
                '1w': '5d',      # 1周
                '1mo': '1mo',    # 1个月
                '3mo': '3mo',    # 3个月
                '6mo': '6mo',    # 6个月
                '1y': '1y'       # 1年
            }

            yf_period = period_map.get(period, '1mo')
            hist = stock.history(period=yf_period)

            if hist.empty:
                return None

            # 获取最新价格
            current_price = hist['Close'].iloc[-1]

            # 计算期间涨跌（相对于期间开始）
            period_start_price = hist['Close'].iloc[0]
            period_change = current_price - period_start_price
            period_change_percent = (period_change / period_start_price) * 100

            # 计算日涨跌（相对于前一交易日）
            previous_close = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
            daily_change = current_price - previous_close
            daily_change_percent = (daily_change / previous_close) * 100

            # 获取期间高低点
            period_high = hist['High'].max()
            period_low = hist['Low'].min()

            # 计算技术指标
            volume = hist['Volume'].iloc[-1]
            avg_volume = hist['Volume'].mean()

            return {
                'symbol': symbol.upper(),
                'name': info.get('longName', self.stock_database.get(symbol.upper(), {}).get('name', symbol)),
                'sector': info.get('sector', self.stock_database.get(symbol.upper(), {}).get('sector', 'Unknown')),
                'current_price': round(current_price, 2),
                'previous_close': round(previous_close, 2),
                'daily_change': round(daily_change, 2),
                'daily_change_percent': round(daily_change_percent, 2),
                'period_change': round(period_change, 2),
                'period_change_percent': round(period_change_percent, 2),
                'period': period,
                'volume': int(volume),
                'avg_volume': int(avg_volume),
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'period_high': round(period_high, 2),
                'period_low': round(period_low, 2),
                'dividend_yield': info.get('dividendYield', 0),
                'beta': info.get('beta', 0),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'historical_data': self.format_historical_data(hist)
            }

        except Exception as e:
            print(f"获取股票数据失败 {symbol}: {e}")
            return None

    def format_historical_data(self, hist):
        """格式化历史数据用于图表显示"""
        try:
            data = []
            for date, row in hist.iterrows():
                data.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'open': round(row['Open'], 2),
                    'high': round(row['High'], 2),
                    'low': round(row['Low'], 2),
                    'close': round(row['Close'], 2),
                    'volume': int(row['Volume'])
                })
            return data[-30:]  # 返回最近30天数据
        except Exception as e:
            print(f"格式化历史数据失败: {e}")
            return []

    def get_stock_news(self, symbol, company_name):
        """获取股票相关新闻"""
        try:
            # 模拟新闻数据（实际应用中应该调用真实的新闻API）
            news_templates = [
                {
                    "title": f"{company_name}发布最新财报，业绩超预期",
                    "summary": f"{company_name}公布了最新季度财报，营收和利润均超出分析师预期，股价可能受到积极影响。",
                    "sentiment": "positive",
                    "impact": "high"
                },
                {
                    "title": f"{company_name}宣布新产品发布计划",
                    "summary": f"{company_name}计划在下个季度推出创新产品，市场对此反应积极，投资者信心增强。",
                    "sentiment": "positive",
                    "impact": "medium"
                },
                {
                    "title": f"分析师上调{company_name}目标价",
                    "summary": f"多家投资银行分析师上调了{company_name}的目标价，认为公司基本面强劲。",
                    "sentiment": "positive",
                    "impact": "medium"
                },
                {
                    "title": f"{company_name}面临监管审查",
                    "summary": f"监管机构对{company_name}的某些业务实践展开调查，可能对公司运营产生影响。",
                    "sentiment": "negative",
                    "impact": "medium"
                },
                {
                    "title": f"市场担忧{company_name}增长放缓",
                    "summary": f"由于宏观经济环境变化，投资者对{company_name}未来增长前景表示担忧。",
                    "sentiment": "negative",
                    "impact": "low"
                },
                {
                    "title": f"{company_name}CEO发表重要讲话",
                    "summary": f"{company_name}首席执行官在投资者会议上分享了公司战略规划和未来发展方向。",
                    "sentiment": "neutral",
                    "impact": "low"
                }
            ]

            # 获取股票历史数据用于生成关联新闻
            import random

            # 获取历史数据来分析股价变动
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period="30d")
                if hist.empty:
                    # 如果没有历史数据，使用模板新闻
                    selected_news = random.sample(news_templates, min(3, len(news_templates)))
                else:
                    # 分析股价变动，生成相关新闻
                    selected_news = []
                    hist_data = hist.reset_index()

                    # 找出显著变动日期
                    for i in range(1, min(len(hist_data), 15)):  # 最近15天
                        prev_close = hist_data.iloc[i-1]['Close']
                        curr_close = hist_data.iloc[i]['Close']
                        change_percent = ((curr_close - prev_close) / prev_close) * 100

                        if abs(change_percent) > 2 and len(selected_news) < 4:  # 显著变动
                            if change_percent > 0:
                                # 上涨，选择积极新闻
                                positive_news = [n for n in news_templates if n['sentiment'] == 'positive']
                                if positive_news:
                                    selected_news.append(random.choice(positive_news))
                            else:
                                # 下跌，选择消极新闻
                                negative_news = [n for n in news_templates if n['sentiment'] == 'negative']
                                if negative_news:
                                    selected_news.append(random.choice(negative_news))

                    # 如果没有显著变动，添加一些随机新闻
                    while len(selected_news) < 3:
                        remaining = [n for n in news_templates if n not in selected_news]
                        if remaining:
                            selected_news.append(random.choice(remaining))
                        else:
                            break
            except:
                # 如果获取历史数据失败，使用模板新闻
                selected_news = random.sample(news_templates, min(3, len(news_templates)))

            # 添加时间戳和其他信息
            news_list = []
            for i, news in enumerate(selected_news):
                # 设置新闻发布时间为1-10天前的随机时间
                days_ago = random.randint(1, 10)
                hours_ago = random.randint(0, 23)
                news_time = datetime.now() - timedelta(days=days_ago, hours=hours_ago)

                news_item = {
                    "id": f"{symbol}_{i}",
                    "title": news["title"],
                    "summary": news["summary"],
                    "sentiment": news["sentiment"],
                    "impact": news["impact"],
                    "source": random.choice(["Reuters", "Bloomberg", "CNBC", "MarketWatch", "Yahoo Finance"]),
                    "published_time": news_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "url": f"https://example.com/news/{symbol.lower()}_{i}",
                    "relevance_score": random.randint(75, 95)
                }
                news_list.append(news_item)

            return sorted(news_list, key=lambda x: x['published_time'], reverse=True)

        except Exception as e:
            print(f"获取新闻失败 {symbol}: {e}")
            return []

    def analyze_news_impact(self, stock_data, news_data):
        """分析新闻对股价的影响"""
        try:
            if not news_data or not stock_data.get('historical_data'):
                return {
                    'summary': '暂无足够数据进行影响分析',
                    'impact_events': [],
                    'overall_sentiment': 'neutral',
                    'correlation_score': 0
                }

            historical_data = stock_data['historical_data']
            impact_events = []

            # 分析每条新闻的影响
            for news in news_data:
                news_date = news['published_time'][:10]  # 获取日期部分

                # 找到新闻发布日期对应的股价数据
                news_day_data = None
                news_day_index = -1

                for i, day_data in enumerate(historical_data):
                    if day_data['date'] == news_date:
                        news_day_data = day_data
                        news_day_index = i
                        break

                if news_day_data and news_day_index < len(historical_data) - 1:
                    # 计算新闻发布后的股价变化
                    next_day_data = historical_data[news_day_index + 1]

                    # 计算当日和次日的价格变化
                    if news_day_index > 0:
                        prev_day_data = historical_data[news_day_index - 1]
                        same_day_change = ((news_day_data['close'] - prev_day_data['close']) / prev_day_data['close']) * 100
                    else:
                        same_day_change = 0

                    next_day_change = ((next_day_data['close'] - news_day_data['close']) / news_day_data['close']) * 100

                    # 计算3日累计影响
                    cumulative_change = 0
                    for j in range(1, min(4, len(historical_data) - news_day_index)):
                        if news_day_index + j < len(historical_data):
                            day_change = ((historical_data[news_day_index + j]['close'] -
                                         historical_data[news_day_index + j - 1]['close']) /
                                        historical_data[news_day_index + j - 1]['close']) * 100
                            cumulative_change += day_change

                    # 判断影响强度
                    impact_strength = 'low'
                    if abs(cumulative_change) > 3:
                        impact_strength = 'high'
                    elif abs(cumulative_change) > 1:
                        impact_strength = 'medium'

                    # 判断影响方向是否与新闻情绪一致
                    sentiment_match = False
                    if news['sentiment'] == 'positive' and cumulative_change > 0:
                        sentiment_match = True
                    elif news['sentiment'] == 'negative' and cumulative_change < 0:
                        sentiment_match = True
                    elif news['sentiment'] == 'neutral':
                        sentiment_match = True

                    impact_events.append({
                        'news_title': news['title'],
                        'news_date': news_date,
                        'news_sentiment': news['sentiment'],
                        'same_day_change': round(same_day_change, 2),
                        'next_day_change': round(next_day_change, 2),
                        'cumulative_3day_change': round(cumulative_change, 2),
                        'impact_strength': impact_strength,
                        'sentiment_match': sentiment_match,
                        'price_on_news_date': news_day_data['close']
                    })

            # 计算整体分析
            if impact_events:
                # 计算情绪分布
                positive_count = sum(1 for event in impact_events if event['news_sentiment'] == 'positive')
                negative_count = sum(1 for event in impact_events if event['news_sentiment'] == 'negative')
                neutral_count = len(impact_events) - positive_count - negative_count

                # 计算平均影响
                avg_positive_impact = sum(event['cumulative_3day_change'] for event in impact_events
                                        if event['news_sentiment'] == 'positive') / max(positive_count, 1)
                avg_negative_impact = sum(event['cumulative_3day_change'] for event in impact_events
                                        if event['news_sentiment'] == 'negative') / max(negative_count, 1)

                # 计算关联度得分
                sentiment_matches = sum(1 for event in impact_events if event['sentiment_match'])
                correlation_score = (sentiment_matches / len(impact_events)) * 100

                # 确定整体情绪
                if positive_count > negative_count:
                    overall_sentiment = 'positive'
                elif negative_count > positive_count:
                    overall_sentiment = 'negative'
                else:
                    overall_sentiment = 'neutral'

                # 生成分析摘要
                summary = f"分析了{len(impact_events)}个新闻事件的影响。"
                if correlation_score > 70:
                    summary += f"新闻情绪与股价变动的关联度较高({correlation_score:.0f}%)。"
                elif correlation_score > 40:
                    summary += f"新闻情绪与股价变动有一定关联({correlation_score:.0f}%)。"
                else:
                    summary += f"新闻情绪与股价变动的关联度较低({correlation_score:.0f}%)。"

                if avg_positive_impact > 1:
                    summary += f"积极新闻平均推动股价上涨{avg_positive_impact:.1f}%。"
                if avg_negative_impact < -1:
                    summary += f"消极新闻平均导致股价下跌{abs(avg_negative_impact):.1f}%。"

                return {
                    'summary': summary,
                    'impact_events': sorted(impact_events, key=lambda x: abs(x['cumulative_3day_change']), reverse=True),
                    'overall_sentiment': overall_sentiment,
                    'correlation_score': round(correlation_score, 1),
                    'sentiment_distribution': {
                        'positive': positive_count,
                        'negative': negative_count,
                        'neutral': neutral_count
                    },
                    'average_impacts': {
                        'positive': round(avg_positive_impact, 2),
                        'negative': round(avg_negative_impact, 2)
                    }
                }
            else:
                return {
                    'summary': '未找到可分析的新闻事件',
                    'impact_events': [],
                    'overall_sentiment': 'neutral',
                    'correlation_score': 0
                }

        except Exception as e:
            print(f"新闻影响分析失败: {e}")
            return {
                'summary': '分析过程中出现错误',
                'impact_events': [],
                'overall_sentiment': 'neutral',
                'correlation_score': 0
            }

    def get_market_sentiment(self, symbol, news_list):
        """分析市场情绪"""
        try:
            if not news_list:
                return {
                    "overall_sentiment": "neutral",
                    "sentiment_score": 50,
                    "confidence": 0,
                    "summary": "暂无足够新闻数据进行情绪分析"
                }

            # 计算情绪分数
            sentiment_scores = {
                "positive": 80,
                "neutral": 50,
                "negative": 20
            }

            impact_weights = {
                "high": 3,
                "medium": 2,
                "low": 1
            }

            total_score = 0
            total_weight = 0

            for news in news_list:
                score = sentiment_scores.get(news["sentiment"], 50)
                weight = impact_weights.get(news["impact"], 1)
                total_score += score * weight
                total_weight += weight

            avg_score = total_score / total_weight if total_weight > 0 else 50

            # 确定整体情绪
            if avg_score >= 65:
                overall_sentiment = "positive"
                sentiment_text = "积极"
            elif avg_score <= 35:
                overall_sentiment = "negative"
                sentiment_text = "消极"
            else:
                overall_sentiment = "neutral"
                sentiment_text = "中性"

            return {
                "overall_sentiment": overall_sentiment,
                "sentiment_score": round(avg_score, 1),
                "confidence": min(len(news_list) * 20, 100),
                "summary": f"基于{len(news_list)}条相关新闻，市场对{symbol}的整体情绪偏{sentiment_text}"
            }

        except Exception as e:
            print(f"分析市场情绪失败: {e}")
            return {
                "overall_sentiment": "neutral",
                "sentiment_score": 50,
                "confidence": 0,
                "summary": "情绪分析暂时不可用"
            }
    
    def get_stock_analysis(self, symbol):
        """获取股票综合分析"""
        try:
            # 获取股票基础数据
            stock_data = self.get_stock_data(symbol)
            if not stock_data:
                return None

            # 获取相关新闻
            news_list = self.get_stock_news(symbol, stock_data['name'])

            # 分析市场情绪
            sentiment_analysis = self.get_market_sentiment(symbol, news_list)

            # 技术分析
            technical_analysis = self.get_technical_analysis(stock_data)

            # 投资建议
            investment_recommendation = self.get_investment_recommendation(stock_data, sentiment_analysis, technical_analysis)

            return {
                'stock_data': stock_data,
                'news': news_list,
                'sentiment_analysis': sentiment_analysis,
                'technical_analysis': technical_analysis,
                'investment_recommendation': investment_recommendation,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            print(f"获取股票分析失败 {symbol}: {e}")
            return None

    def get_technical_analysis(self, stock_data):
        """技术分析"""
        try:
            current_price = stock_data['current_price']
            week_52_high = stock_data['week_52_high']
            week_52_low = stock_data['week_52_low']
            price_change_percent = stock_data['price_change_percent']
            volume = stock_data['volume']
            avg_volume = stock_data['avg_volume']

            # 计算价格位置
            price_range = week_52_high - week_52_low
            price_position = (current_price - week_52_low) / price_range * 100 if price_range > 0 else 50

            # 成交量分析
            volume_ratio = volume / avg_volume if avg_volume > 0 else 1

            # 技术指标
            indicators = []

            # 价格趋势
            if price_change_percent > 5:
                indicators.append({"name": "价格趋势", "value": "强势上涨", "signal": "bullish"})
            elif price_change_percent > 2:
                indicators.append({"name": "价格趋势", "value": "温和上涨", "signal": "bullish"})
            elif price_change_percent < -5:
                indicators.append({"name": "价格趋势", "value": "强势下跌", "signal": "bearish"})
            elif price_change_percent < -2:
                indicators.append({"name": "价格趋势", "value": "温和下跌", "signal": "bearish"})
            else:
                indicators.append({"name": "价格趋势", "value": "横盘整理", "signal": "neutral"})

            # 价格位置
            if price_position > 80:
                indicators.append({"name": "价格位置", "value": "接近52周高点", "signal": "bearish"})
            elif price_position > 60:
                indicators.append({"name": "价格位置", "value": "相对高位", "signal": "neutral"})
            elif price_position < 20:
                indicators.append({"name": "价格位置", "value": "接近52周低点", "signal": "bullish"})
            elif price_position < 40:
                indicators.append({"name": "价格位置", "value": "相对低位", "signal": "neutral"})
            else:
                indicators.append({"name": "价格位置", "value": "中等位置", "signal": "neutral"})

            # 成交量
            if volume_ratio > 2:
                indicators.append({"name": "成交量", "value": "异常放量", "signal": "bullish"})
            elif volume_ratio > 1.5:
                indicators.append({"name": "成交量", "value": "温和放量", "signal": "bullish"})
            elif volume_ratio < 0.5:
                indicators.append({"name": "成交量", "value": "成交萎缩", "signal": "bearish"})
            else:
                indicators.append({"name": "成交量", "value": "成交正常", "signal": "neutral"})

            # 计算综合评分
            bullish_count = sum(1 for ind in indicators if ind["signal"] == "bullish")
            bearish_count = sum(1 for ind in indicators if ind["signal"] == "bearish")

            if bullish_count > bearish_count:
                overall_signal = "bullish"
                signal_text = "看涨"
            elif bearish_count > bullish_count:
                overall_signal = "bearish"
                signal_text = "看跌"
            else:
                overall_signal = "neutral"
                signal_text = "中性"

            return {
                "indicators": indicators,
                "overall_signal": overall_signal,
                "signal_text": signal_text,
                "price_position": round(price_position, 1),
                "volume_ratio": round(volume_ratio, 2),
                "summary": f"技术面显示{signal_text}信号，当前价格位于52周区间的{round(price_position, 1)}%位置"
            }

        except Exception as e:
            print(f"技术分析失败: {e}")
            return {
                "indicators": [],
                "overall_signal": "neutral",
                "signal_text": "中性",
                "summary": "技术分析暂时不可用"
            }

    def get_investment_recommendation(self, stock_data, sentiment_analysis, technical_analysis):
        """投资建议"""
        try:
            # 基于多个因素给出投资建议
            score = 50  # 基础分数

            # 价格变化影响
            price_change = stock_data['price_change_percent']
            if price_change > 5:
                score += 15
            elif price_change > 2:
                score += 10
            elif price_change < -5:
                score -= 15
            elif price_change < -2:
                score -= 10

            # 情绪分析影响
            sentiment_score = sentiment_analysis['sentiment_score']
            score += (sentiment_score - 50) * 0.3

            # 技术分析影响
            if technical_analysis['overall_signal'] == 'bullish':
                score += 15
            elif technical_analysis['overall_signal'] == 'bearish':
                score -= 15

            # 估值影响（基于PE比率）
            pe_ratio = stock_data.get('pe_ratio', 0)
            if pe_ratio > 0:
                if pe_ratio < 15:
                    score += 10  # 低估值
                elif pe_ratio > 30:
                    score -= 10  # 高估值

            # 确定推荐等级
            if score >= 75:
                recommendation = "强烈买入"
                action = "buy_strong"
                color = "#28a745"
            elif score >= 60:
                recommendation = "买入"
                action = "buy"
                color = "#20c997"
            elif score >= 40:
                recommendation = "持有"
                action = "hold"
                color = "#ffc107"
            elif score >= 25:
                recommendation = "卖出"
                action = "sell"
                color = "#fd7e14"
            else:
                recommendation = "强烈卖出"
                action = "sell_strong"
                color = "#dc3545"

            # 风险评估
            beta = stock_data.get('beta', 1)
            if beta > 1.5:
                risk_level = "高风险"
            elif beta > 1.2:
                risk_level = "中高风险"
            elif beta < 0.8:
                risk_level = "低风险"
            else:
                risk_level = "中等风险"

            return {
                "recommendation": recommendation,
                "action": action,
                "score": round(score, 1),
                "color": color,
                "risk_level": risk_level,
                "target_price": round(stock_data['current_price'] * (1 + (score - 50) / 100), 2),
                "reasons": self.get_recommendation_reasons(stock_data, sentiment_analysis, technical_analysis, score),
                "time_horizon": "1-3个月"
            }

        except Exception as e:
            print(f"生成投资建议失败: {e}")
            return {
                "recommendation": "持有",
                "action": "hold",
                "score": 50,
                "color": "#ffc107",
                "risk_level": "中等风险",
                "reasons": ["数据分析中，请稍后查看"],
                "time_horizon": "1-3个月"
            }

    def get_recommendation_reasons(self, stock_data, sentiment_analysis, technical_analysis, score):
        """生成推荐理由"""
        reasons = []

        # 价格表现
        price_change = stock_data['price_change_percent']
        if abs(price_change) > 2:
            direction = "上涨" if price_change > 0 else "下跌"
            reasons.append(f"股价近期{direction}{abs(price_change):.1f}%，显示{direction}动能")

        # 市场情绪
        if sentiment_analysis['sentiment_score'] > 65:
            reasons.append("市场情绪积极，新闻面偏好")
        elif sentiment_analysis['sentiment_score'] < 35:
            reasons.append("市场情绪消极，需关注风险")

        # 技术面
        if technical_analysis['overall_signal'] == 'bullish':
            reasons.append("技术指标显示看涨信号")
        elif technical_analysis['overall_signal'] == 'bearish':
            reasons.append("技术指标显示看跌信号")

        # 估值
        pe_ratio = stock_data.get('pe_ratio', 0)
        if pe_ratio > 0:
            if pe_ratio < 15:
                reasons.append("估值相对较低，具有投资价值")
            elif pe_ratio > 30:
                reasons.append("估值偏高，需谨慎投资")

        # 成交量
        volume_ratio = technical_analysis.get('volume_ratio', 1)
        if volume_ratio > 1.5:
            reasons.append("成交量放大，市场关注度提升")

        return reasons[:4]  # 最多返回4个理由
    
    def get_weather_icon(self, description, temp):
        """根据天气状况和温度返回对应的图标"""
        desc_lower = description.lower()
        
        # 雨天图标
        if any(word in desc_lower for word in ['雨', 'rain', '阵雨', 'shower']):
            return '🌧️'
        # 雪天图标
        elif any(word in desc_lower for word in ['雪', 'snow']):
            return '❄️'
        # 雾霾图标
        elif any(word in desc_lower for word in ['雾', 'fog', '霾', 'haze']):
            return '🌫️'
        # 多云图标
        elif any(word in desc_lower for word in ['云', 'cloud', '阴']):
            if temp > 25:
                return '⛅'  # 温暖多云
            else:
                return '☁️'  # 凉爽多云
        # 晴天图标
        elif any(word in desc_lower for word in ['晴', 'clear', 'sunny']):
            if temp > 30:
                return '☀️'  # 炎热晴天
            elif temp > 20:
                return '🌤️'  # 温暖晴天
            else:
                return '🌞'  # 凉爽晴天
        # 默认图标
        else:
            return '🌤️'
    
    def get_clothing_by_temperature(self, temp, feels_like):
        """根据温度获取基础服装建议的工具函数"""
        reference_temp = feels_like
        temp_diff = abs(temp - feels_like)
        
        if reference_temp >= 30:
            extra_note = " 注意温差" if temp_diff > 3 else ""
            return {
                "top": "轻薄透气的短袖T恤或衬衫",
                "bottom": "短裤或轻薄长裤",
                "shoes": "透气凉鞋或轻便运动鞋",
                "accessories": "遮阳帽、太阳镜",
                "reason": f"高温天气需要透气散热{extra_note}"
            }
        elif reference_temp >= 25:
            return {
                "top": "短袖衬衫或薄长袖",
                "bottom": "长裤或裙子",
                "shoes": "休闲鞋或运动鞋",
                "accessories": "轻薄外套备用",
                "reason": "温暖舒适的搭配"
            }
        elif reference_temp >= 20:
            return {
                "top": "长袖衬衫或薄毛衣",
                "bottom": "长裤",
                "shoes": "休闲鞋或靴子",
                "accessories": "轻薄外套",
                "reason": "微凉天气需要适度保暖"
            }
        elif reference_temp >= 15:
            return {
                "top": "毛衣或厚衬衫",
                "bottom": "长裤",
                "shoes": "靴子或厚底鞋",
                "accessories": "夹克或薄外套",
                "reason": "凉爽天气需要保暖"
            }
        elif reference_temp >= 10:
            return {
                "top": "厚毛衣或卫衣",
                "bottom": "厚长裤",
                "shoes": "保暖靴子",
                "accessories": "厚外套或夹克",
                "reason": "寒冷天气需要多层保暖"
            }
        elif reference_temp >= 5:
            return {
                "top": "厚毛衣加保暖内衣",
                "bottom": "厚长裤或保暖裤",
                "shoes": "保暖靴子",
                "accessories": "厚大衣、围巾",
                "reason": "严寒天气需要全面保暖"
            }
        else:
            return {
                "top": "厚羽绒服或棉衣",
                "bottom": "保暖裤或厚长裤",
                "shoes": "防滑保暖靴",
                "accessories": "手套、围巾、帽子",
                "reason": "极寒天气需要防寒装备"
            }

    def get_weather_specific_advice(self, description, wind_speed, humidity):
        """根据具体天气状况获取专门建议的工具函数"""
        advice = []
        
        desc_lower = description.lower()
        if any(word in desc_lower for word in ['雨', 'rain', '阵雨', 'shower']):
            advice.append("☔ 携带雨伞或穿防水外套")
        elif any(word in desc_lower for word in ['雪', 'snow']):
            advice.append("❄️ 穿防滑鞋，注意保暖防湿")
        elif any(word in desc_lower for word in ['雾', 'fog', '霾', 'haze']):
            advice.append("😷 佩戴口罩，注意交通安全")
        elif any(word in desc_lower for word in ['晴', 'clear', 'sunny']):
            advice.append("☀️ 做好防晒措施")
        
        if wind_speed > 10:
            advice.append("💨 穿防风外套，固定帽子")
        elif wind_speed > 5:
            advice.append("🌬️ 注意防风，避免宽松衣物")
        
        if humidity > 80:
            advice.append("💧 选择透气材质，避免闷热")
        elif humidity < 30:
            advice.append("🧴 注意保湿，多喝水")
        
        return advice

    def get_3day_forecast(self, city):
        """获取未来3天天气预测"""
        try:
            import random
            import datetime

            # 基础天气数据
            weather_conditions = ["晴天", "多云", "阴天", "小雨", "中雨", "雷阵雨", "雪", "雾"]
            weather_icons = ["☀️", "⛅", "☁️", "🌦️", "🌧️", "⛈️", "❄️", "🌫️"]

            # 获取当前日期
            today = datetime.date.today()

            # 季节温度范围
            month = today.month
            if month in [12, 1, 2]:  # 冬季
                temp_range = (-5, 15)
            elif month in [3, 4, 5]:  # 春季
                temp_range = (10, 25)
            elif month in [6, 7, 8]:  # 夏季
                temp_range = (20, 35)
            else:  # 秋季
                temp_range = (5, 20)

            forecast = []

            for i in range(1, 4):  # 未来3天
                forecast_date = today + datetime.timedelta(days=i)

                # 模拟天气变化趋势
                condition_index = random.randint(0, len(weather_conditions) - 1)

                # 温度有一定的连续性和随机波动
                base_temp = random.randint(temp_range[0], temp_range[1])
                temp_variation = random.randint(-3, 3)
                temperature = max(temp_range[0], min(temp_range[1], base_temp + temp_variation))

                # 最高最低温度
                temp_diff = random.randint(5, 12)
                max_temp = temperature + temp_diff // 2
                min_temp = temperature - temp_diff // 2

                day_forecast = {
                    "date": forecast_date.strftime("%Y-%m-%d"),
                    "day_name": self.get_day_name(forecast_date.weekday()),
                    "month_day": forecast_date.strftime("%m月%d日"),
                    "condition": weather_conditions[condition_index],
                    "weather_icon": weather_icons[condition_index],
                    "temperature": temperature,
                    "max_temperature": max_temp,
                    "min_temperature": min_temp,
                    "humidity": random.randint(30, 90),
                    "wind_speed": round(random.uniform(0.5, 8.0), 2),
                    "pressure": random.randint(990, 1030),
                    "precipitation_chance": random.randint(0, 100) if condition_index >= 3 else random.randint(0, 30)
                }

                forecast.append(day_forecast)

            return forecast

        except Exception as e:
            print(f"获取3天天气预测时出错: {e}")
            return []

    def get_day_name(self, weekday):
        """获取星期名称"""
        day_names = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        return day_names[weekday]

    def get_daily_clothing_advice(self, day_weather):
        """为单日天气生成穿衣建议"""
        try:
            temp = day_weather['temperature']
            max_temp = day_weather['max_temperature']
            min_temp = day_weather['min_temperature']
            condition = day_weather['condition']
            humidity = day_weather['humidity']
            wind_speed = day_weather['wind_speed']

            # 基于平均温度的基础建议
            feels_like = temp  # 简化处理
            base_advice = self.get_clothing_by_temperature(temp, feels_like)

            # 根据温差调整建议
            temp_diff = max_temp - min_temp
            if temp_diff > 10:
                base_advice['accessories'] += "，建议分层穿衣"
                base_advice['reason'] += f"（温差{temp_diff}°C较大）"

            # 根据天气条件调整
            weather_tips = []
            if "雨" in condition:
                weather_tips.append("☔ 携带雨伞")
                base_advice['shoes'] = "防水鞋或雨靴"
            elif "雪" in condition:
                weather_tips.append("❄️ 注意防滑保暖")
                base_advice['shoes'] = "防滑靴"
            elif "晴" in condition:
                weather_tips.append("☀️ 注意防晒")
                if temp > 25:
                    base_advice['accessories'] += "，太阳镜"

            if wind_speed > 5:
                weather_tips.append("💨 注意防风")

            if humidity > 80:
                weather_tips.append("💧 选择透气材质")

            return {
                "clothing": base_advice,
                "weather_tips": weather_tips,
                "temperature_range": f"{min_temp}°C - {max_temp}°C",
                "comfort_level": self.get_comfort_level(temp, humidity, condition)
            }

        except Exception as e:
            print(f"生成每日穿衣建议时出错: {e}")
            return None

    def get_comfort_level(self, temp, humidity, condition):
        """评估舒适度等级"""
        comfort_score = 0

        # 温度舒适度
        if 18 <= temp <= 26:
            comfort_score += 40
        elif 15 <= temp <= 30:
            comfort_score += 30
        elif 10 <= temp <= 35:
            comfort_score += 20
        else:
            comfort_score += 10

        # 湿度舒适度
        if 40 <= humidity <= 60:
            comfort_score += 30
        elif 30 <= humidity <= 70:
            comfort_score += 20
        else:
            comfort_score += 10

        # 天气条件舒适度
        if condition in ["晴天", "多云"]:
            comfort_score += 30
        elif condition in ["阴天"]:
            comfort_score += 20
        elif condition in ["小雨", "雾"]:
            comfort_score += 10
        else:
            comfort_score += 5

        if comfort_score >= 80:
            return {"level": "excellent", "text": "非常舒适", "icon": "😊"}
        elif comfort_score >= 60:
            return {"level": "good", "text": "比较舒适", "icon": "🙂"}
        elif comfort_score >= 40:
            return {"level": "fair", "text": "一般", "icon": "😐"}
        else:
            return {"level": "poor", "text": "不太舒适", "icon": "😕"}

    def get_gemini_clothing_advice(self, weather_data):
        """使用Gemini AI生成智能穿衣建议"""
        try:
            if not self.use_gemini:
                return self._fallback_advice_simple(weather_data)
            
            city = weather_data['name']
            country = weather_data['sys']['country']
            temp = weather_data['main']['temp']
            feels_like = weather_data['main']['feels_like']
            humidity = weather_data['main']['humidity']
            description = weather_data['weather'][0]['description']
            wind_speed = weather_data['wind']['speed']
            
            temp_advice = self.get_clothing_by_temperature(temp, feels_like)
            weather_advice = self.get_weather_specific_advice(description, wind_speed, humidity)
            
            prompt = f"""
作为专业时尚顾问，请根据{city}, {country}的天气生成穿衣建议：

天气数据：
- 温度：{temp}°C，体感：{feels_like}°C
- 天气：{description}
- 湿度：{humidity}%，风速：{wind_speed}m/s

基础分析：
- 上衣：{temp_advice['top']}
- 下装：{temp_advice['bottom']}
- 鞋子：{temp_advice['shoes']}
- 配饰：{temp_advice['accessories']}

特殊提醒：{chr(10).join(weather_advice) if weather_advice else '无'}

请生成5条实用建议，格式：emoji + 建议 - 原因（10字内）
"""
            
            response = self.gemini_model.generate_content(prompt)
            
            if response and response.text:
                advice_lines = [line.strip() for line in response.text.strip().split('\n') if line.strip()]
                advice = []
                
                for line in advice_lines:
                    if line and (line.startswith(('1.', '2.', '3.', '4.', '5.')) or 
                               any(emoji in line for emoji in ['👕', '👔', '🧥', '👖', '👗', '👠', '👟', '🧢', '🧤', '🧣', '☂️', '🌂', '😷', '🕶️', '💧', '🌡️', '💨', '☀️', '❄️', '☔']) or
                               ('-' in line and len(line) > 10)):
                        clean_line = line
                        for i in range(1, 10):
                            clean_line = clean_line.replace(f"{i}. ", "").replace(f"{i}.", "")
                        clean_line = clean_line.strip()
                        if clean_line:
                            advice.append(clean_line)
                
                return advice[:5] if advice else self._fallback_advice_simple(weather_data)
            
            return self._fallback_advice_simple(weather_data)
                
        except Exception as e:
            print(f"Gemini调用失败: {e}")
            return self._fallback_advice_simple(weather_data)
    
    def _fallback_advice_simple(self, weather_data):
        """简单回退建议"""
        temp = weather_data['main']['temp']
        feels_like = weather_data['main']['feels_like']
        description = weather_data['weather'][0]['description']
        wind_speed = weather_data['wind']['speed']
        humidity = weather_data['main']['humidity']

        temp_advice = self.get_clothing_by_temperature(temp, feels_like)
        weather_advice = self.get_weather_specific_advice(description, wind_speed, humidity)

        advice = []
        advice.append(f"👕 {temp_advice['top']} - {temp_advice['reason']}")
        advice.append(f"👖 {temp_advice['bottom']} - 舒适搭配")
        advice.append(f"👟 {temp_advice['shoes']} - 适合天气")

        for special_advice in weather_advice[:2]:
            advice.append(special_advice)

        return advice[:5]

    def process_city_name(self, city_name, country, state=None):
        """智能处理城市名称，提高匹配成功率"""
        # state参数预留给未来扩展使用
        _ = state

        # 城市名称映射表 - 将API返回的名称映射到下拉列表中的标准名称
        city_mapping = {
            # 中国城市映射
            'Beijing': 'Beijing',
            'Peking': 'Beijing',
            'Shanghai': 'Shanghai',
            'Guangzhou': 'Guangzhou',
            'Canton': 'Guangzhou',
            'Shenzhen': 'Shenzhen',
            'Hangzhou': 'Hangzhou',
            'Nanjing': 'Nanjing',
            'Nanking': 'Nanjing',
            'Chengdu': 'Chengdu',
            'Wuhan': 'Wuhan',
            'Xi\'an': 'Xi\'an',
            'Xian': 'Xi\'an',
            'Chongqing': 'Chongqing',

            # 国际城市映射
            'Tokyo': 'Tokyo',
            'Seoul': 'Seoul',
            'Singapore': 'Singapore',
            'Bangkok': 'Bangkok',
            'New York': 'New York',
            'New York City': 'New York',
            'NYC': 'New York',
            'London': 'London',
            'Paris': 'Paris',
            'Sydney': 'Sydney',
            'Dubai': 'Dubai',
            'Moscow': 'Moscow',
            'Moskva': 'Moscow'
        }

        # 首先尝试直接映射
        if city_name in city_mapping:
            return city_mapping[city_name]

        # 根据国家代码进行智能推断
        if country == 'CN':  # 中国
            # 中国主要城市的特殊处理
            city_lower = city_name.lower()
            if 'beijing' in city_lower or 'peking' in city_lower:
                return 'Beijing'
            elif 'shanghai' in city_lower:
                return 'Shanghai'
            elif 'guangzhou' in city_lower or 'canton' in city_lower:
                return 'Guangzhou'
            elif 'shenzhen' in city_lower:
                return 'Shenzhen'
            elif 'hangzhou' in city_lower:
                return 'Hangzhou'
            elif 'nanjing' in city_lower or 'nanking' in city_lower:
                return 'Nanjing'
            elif 'chengdu' in city_lower:
                return 'Chengdu'
            elif 'wuhan' in city_lower:
                return 'Wuhan'
            elif 'xian' in city_lower or 'xi\'an' in city_lower:
                return 'Xi\'an'
            elif 'chongqing' in city_lower:
                return 'Chongqing'

        elif country == 'US':  # 美国
            if 'new york' in city_name.lower() or city_name.lower() == 'nyc':
                return 'New York'

        elif country == 'JP':  # 日本
            if 'tokyo' in city_name.lower():
                return 'Tokyo'

        elif country == 'KR':  # 韩国
            if 'seoul' in city_name.lower():
                return 'Seoul'

        elif country == 'SG':  # 新加坡
            return 'Singapore'

        elif country == 'TH':  # 泰国
            if 'bangkok' in city_name.lower():
                return 'Bangkok'

        elif country == 'GB':  # 英国
            if 'london' in city_name.lower():
                return 'London'

        elif country == 'FR':  # 法国
            if 'paris' in city_name.lower():
                return 'Paris'

        elif country == 'AU':  # 澳大利亚
            if 'sydney' in city_name.lower():
                return 'Sydney'

        elif country == 'AE':  # 阿联酋
            if 'dubai' in city_name.lower():
                return 'Dubai'

        elif country == 'RU':  # 俄罗斯
            if 'moscow' in city_name.lower() or 'moskva' in city_name.lower():
                return 'Moscow'

        # 如果没有匹配到，返回原始城市名称
        return city_name

    def get_clothing_recommendations(self, weather_data):
        """根据天气状况生成衣物购买推荐链接"""
        temp = weather_data['main']['temp']
        feels_like = weather_data['main']['feels_like']
        description = weather_data['weather'][0]['description']
        wind_speed = weather_data['wind']['speed']
        humidity = weather_data['main']['humidity']

        # 预留变量供未来扩展使用
        _ = temp, humidity

        recommendations = []

        # 根据体感温度推荐基础衣物
        if feels_like >= 30:
            recommendations.extend([
                {
                    "category": "上衣",
                    "item": "透气短袖T恤",
                    "reason": "高温透气",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=透气短袖T恤",
                        "京东": "https://search.jd.com/Search?keyword=透气短袖T恤",
                        "天猫": "https://list.tmall.com/search_product.htm?q=透气短袖T恤"
                    }
                },
                {
                    "category": "防晒",
                    "item": "遮阳帽",
                    "reason": "防晒必备",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=遮阳帽",
                        "京东": "https://search.jd.com/Search?keyword=遮阳帽",
                        "天猫": "https://list.tmall.com/search_product.htm?q=遮阳帽"
                    }
                }
            ])
        elif feels_like >= 20:
            recommendations.extend([
                {
                    "category": "上衣",
                    "item": "长袖衬衫",
                    "reason": "舒适保暖",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=长袖衬衫",
                        "京东": "https://search.jd.com/Search?keyword=长袖衬衫",
                        "天猫": "https://list.tmall.com/search_product.htm?q=长袖衬衫"
                    }
                },
                {
                    "category": "外套",
                    "item": "薄外套",
                    "reason": "温差防护",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=薄外套",
                        "京东": "https://search.jd.com/Search?keyword=薄外套",
                        "天猫": "https://list.tmall.com/search_product.htm?q=薄外套"
                    }
                }
            ])
        elif feels_like >= 10:
            recommendations.extend([
                {
                    "category": "上衣",
                    "item": "毛衣",
                    "reason": "保暖舒适",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=毛衣",
                        "京东": "https://search.jd.com/Search?keyword=毛衣",
                        "天猫": "https://list.tmall.com/search_product.htm?q=毛衣"
                    }
                },
                {
                    "category": "外套",
                    "item": "厚外套",
                    "reason": "防寒保暖",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=厚外套",
                        "京东": "https://search.jd.com/Search?keyword=厚外套",
                        "天猫": "https://list.tmall.com/search_product.htm?q=厚外套"
                    }
                }
            ])
        else:
            recommendations.extend([
                {
                    "category": "外套",
                    "item": "羽绒服",
                    "reason": "极寒防护",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=羽绒服",
                        "京东": "https://search.jd.com/Search?keyword=羽绒服",
                        "天猫": "https://list.tmall.com/search_product.htm?q=羽绒服"
                    }
                },
                {
                    "category": "配饰",
                    "item": "保暖手套",
                    "reason": "手部保暖",
                    "links": {
                        "淘宝": "https://s.taobao.com/search?q=保暖手套",
                        "京东": "https://search.jd.com/Search?keyword=保暖手套",
                        "天猫": "https://list.tmall.com/search_product.htm?q=保暖手套"
                    }
                }
            ])

        # 根据天气状况添加特殊推荐
        desc_lower = description.lower()
        if any(word in desc_lower for word in ['雨', 'rain', '阵雨', 'shower']):
            recommendations.append({
                "category": "雨具",
                "item": "雨伞",
                "reason": "雨天必备",
                "links": {
                    "淘宝": "https://s.taobao.com/search?q=雨伞",
                    "京东": "https://search.jd.com/Search?keyword=雨伞",
                    "天猫": "https://list.tmall.com/search_product.htm?q=雨伞"
                }
            })
        elif any(word in desc_lower for word in ['雪', 'snow']):
            recommendations.append({
                "category": "鞋子",
                "item": "防滑靴",
                "reason": "雪天防滑",
                "links": {
                    "淘宝": "https://s.taobao.com/search?q=防滑靴",
                    "京东": "https://search.jd.com/Search?keyword=防滑靴",
                    "天猫": "https://list.tmall.com/search_product.htm?q=防滑靴"
                }
            })
        elif any(word in desc_lower for word in ['晴', 'clear', 'sunny']) and feels_like > 25:
            recommendations.append({
                "category": "防晒",
                "item": "防晒霜",
                "reason": "紫外线防护",
                "links": {
                    "淘宝": "https://s.taobao.com/search?q=防晒霜",
                    "京东": "https://search.jd.com/Search?keyword=防晒霜",
                    "天猫": "https://list.tmall.com/search_product.htm?q=防晒霜"
                }
            })

        # 根据风速推荐
        if wind_speed > 10:
            recommendations.append({
                "category": "外套",
                "item": "防风衣",
                "reason": "大风防护",
                "links": {
                    "淘宝": "https://s.taobao.com/search?q=防风衣",
                    "京东": "https://search.jd.com/Search?keyword=防风衣",
                    "天猫": "https://list.tmall.com/search_product.htm?q=防风衣"
                }
            })

        return recommendations[:4]  # 最多返回4个推荐

    def get_travel_recommendations(self, city, month=None):
        """根据城市和月份获取旅行推荐"""
        import datetime
        if month is None:
            month = datetime.datetime.now().month

        # 旅行推荐数据库
        travel_data = {
            # 周边推荐（基于主要城市）
            "nearby": {
                "Beijing": [
                    {"name": "承德避暑山庄", "description": "清朝皇家园林，夏季避暑胜地", "icon": "🏯", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "image": "https://source.unsplash.com/400x300/?chengde,imperial,palace,garden", "distance": "230km", "travel_time": "3小时"},
                    {"name": "天津海河", "description": "现代都市风光，夜景迷人", "icon": "🌉", "color": "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)", "image": "https://source.unsplash.com/400x300/?tianjin,river,city,night", "distance": "120km", "travel_time": "1.5小时"},
                    {"name": "秦皇岛北戴河", "description": "海滨度假胜地，沙滩阳光", "icon": "🏖️", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "image": "https://source.unsplash.com/400x300/?beidaihe,beach,seaside,china", "distance": "280km", "travel_time": "3.5小时"}
                ],
                "Shanghai": [
                    {"name": "苏州园林", "description": "江南古典园林，诗情画意", "icon": "🏮", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://source.unsplash.com/400x300/?suzhou,garden,classical,chinese,pavilion", "distance": "100km", "travel_time": "1小时"},
                    {"name": "杭州西湖", "description": "人间天堂，湖光山色", "icon": "🌸", "color": "linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)", "image": "https://source.unsplash.com/400x300/?westlake,hangzhou,pagoda,lake,willow", "distance": "180km", "travel_time": "2小时"},
                    {"name": "南京夫子庙", "description": "六朝古都，历史文化", "icon": "🏛️", "color": "linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)", "image": "https://source.unsplash.com/400x300/?confucius,temple,nanjing,traditional,architecture", "distance": "300km", "travel_time": "3小时"}
                ],
                "Guangzhou": [
                    {"name": "深圳世界之窗", "description": "世界景观微缩景区", "icon": "🌍", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://picsum.photos/400/300?random=10", "distance": "120km", "travel_time": "1.5小时"},
                    {"name": "珠海长隆", "description": "海洋主题乐园", "icon": "🐋", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://picsum.photos/400/300?random=11", "distance": "150km", "travel_time": "2小时"},
                    {"name": "佛山祖庙", "description": "岭南古建筑群", "icon": "🏮", "color": "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)", "image": "https://picsum.photos/400/300?random=12", "distance": "50km", "travel_time": "1小时"}
                ],
                "Tokyo": [
                    {"name": "富士山", "description": "日本最高峰，神圣象征", "icon": "🗻", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://picsum.photos/400/300?random=13", "distance": "100km", "travel_time": "2小时"},
                    {"name": "镰仓大佛", "description": "古都镰仓，历史文化", "icon": "🧘", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://picsum.photos/400/300?random=14", "distance": "50km", "travel_time": "1小时"},
                    {"name": "箱根温泉", "description": "温泉胜地，自然风光", "icon": "♨️", "color": "linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)", "image": "https://picsum.photos/400/300?random=15", "distance": "80km", "travel_time": "1.5小时"}
                ],
                "Hangzhou": [
                    {"name": "千岛湖", "description": "湖光山色，度假胜地", "icon": "🏞️", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "image": "https://picsum.photos/400/300?random=16", "distance": "150km", "travel_time": "2小时"},
                    {"name": "乌镇水乡", "description": "江南水乡，古镇风情", "icon": "🛶", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://picsum.photos/400/300?random=17", "distance": "60km", "travel_time": "1小时"},
                    {"name": "莫干山", "description": "避暑胜地，民宿天堂", "icon": "🌲", "color": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "image": "https://picsum.photos/400/300?random=18", "distance": "70km", "travel_time": "1.5小时"}
                ],
                "Chongqing": [
                    {"name": "大足石刻", "description": "世界文化遗产，石雕艺术", "icon": "🗿", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "image": "https://source.unsplash.com/400x300/?dazu,stone,carving,buddha,sculpture", "distance": "80km", "travel_time": "1.5小时"},
                    {"name": "武隆天生三桥", "description": "自然奇观，喀斯特地貌", "icon": "🌉", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://source.unsplash.com/400x300/?wulong,natural,bridge,karst,canyon", "distance": "120km", "travel_time": "2小时"},
                    {"name": "合川钓鱼城", "description": "古战场遗址，历史文化", "icon": "🏰", "color": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "image": "https://source.unsplash.com/400x300/?ancient,fortress,castle,historical,china", "distance": "60km", "travel_time": "1小时"}
                ],
                "Chengdu": [
                    {"name": "都江堰", "description": "古代水利工程，世界遗产", "icon": "🌊", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "image": "https://source.unsplash.com/400x300/?dujiangyan,irrigation,ancient,water,engineering", "distance": "60km", "travel_time": "1小时"},
                    {"name": "青城山", "description": "道教名山，清幽秀美", "icon": "⛰️", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://source.unsplash.com/400x300/?qingcheng,mountain,taoist,temple,forest", "distance": "70km", "travel_time": "1.5小时"},
                    {"name": "峨眉山", "description": "佛教圣地，金顶云海", "icon": "🏔️", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://source.unsplash.com/400x300/?emei,mountain,buddhist,temple,golden,summit", "distance": "150km", "travel_time": "2.5小时"}
                ],
                "Xi'an": [
                    {"name": "华山", "description": "五岳之一，险峻奇峰", "icon": "⛰️", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "image": "https://picsum.photos/400/300?random=19", "distance": "120km", "travel_time": "2小时"},
                    {"name": "法门寺", "description": "佛教圣地，舍利宝塔", "icon": "🛕", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://picsum.photos/400/300?random=20", "distance": "120km", "travel_time": "2小时"},
                    {"name": "乾陵", "description": "唐代皇陵，武则天陵墓", "icon": "🏛️", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://picsum.photos/400/300?random=21", "distance": "80km", "travel_time": "1.5小时"}
                ],
                "Wuhan": [
                    {"name": "黄鹤楼", "description": "江南三大名楼，诗词文化", "icon": "🏯", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://picsum.photos/400/300?random=22", "distance": "市内", "travel_time": "30分钟"},
                    {"name": "木兰天池", "description": "山水风光，避暑胜地", "icon": "🏞️", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://picsum.photos/400/300?random=23", "distance": "60km", "travel_time": "1小时"},
                    {"name": "东湖樱花园", "description": "樱花盛开，湖光山色", "icon": "🌸", "color": "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)", "image": "https://picsum.photos/400/300?random=24", "distance": "15km", "travel_time": "30分钟"}
                ],
                "Nanjing": [
                    {"name": "中山陵", "description": "国父陵墓，庄严肃穆", "icon": "🏛️", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "distance": "15km", "travel_time": "30分钟"},
                    {"name": "扬州瘦西湖", "description": "园林艺术，诗画江南", "icon": "🌸", "color": "linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)", "distance": "100km", "travel_time": "1.5小时"},
                    {"name": "镇江金山寺", "description": "佛教名刹，长江风光", "icon": "🛕", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "distance": "80km", "travel_time": "1小时"}
                ],
                "Tianjin": [
                    {"name": "盘山", "description": "京东第一山，自然风光", "icon": "⛰️", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "distance": "90km", "travel_time": "1.5小时"},
                    {"name": "黄崖关长城", "description": "明代长城，险要关隘", "icon": "🏯", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "distance": "100km", "travel_time": "2小时"},
                    {"name": "独乐寺", "description": "千年古刹，辽代建筑", "icon": "🛕", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "distance": "80km", "travel_time": "1.5小时"}
                ],
                "Shenzhen": [
                    {"name": "大梅沙海滨", "description": "黄金海岸，海滨度假", "icon": "🏖️", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "distance": "30km", "travel_time": "45分钟"},
                    {"name": "梧桐山", "description": "深圳第一峰，登高望远", "icon": "🏔️", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "distance": "25km", "travel_time": "40分钟"},
                    {"name": "大鹏古城", "description": "明清古城，历史文化", "icon": "🏰", "color": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "distance": "60km", "travel_time": "1小时"}
                ]
            },

            # 国内推荐（按月份）
            "domestic": {
                1: [  # 1月
                    {"name": "哈尔滨冰雪大世界", "city": "哈尔滨", "description": "冰雪艺术的殿堂", "icon": "❄️", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "image": "https://source.unsplash.com/400x300/?harbin,ice,snow,festival,sculpture", "reason": "冬季冰雪节最佳时期"},
                    {"name": "海南三亚", "city": "三亚", "description": "热带海滨度假天堂", "icon": "🏝️", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "image": "https://source.unsplash.com/400x300/?sanya,tropical,beach,palm,hainan", "reason": "避寒度假的理想选择"},
                    {"name": "云南大理", "city": "大理", "description": "风花雪月的浪漫古城", "icon": "🌺", "color": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "image": "https://source.unsplash.com/400x300/?dali,yunnan,ancient,town,erhai", "reason": "四季如春，冬季温暖"}
                ],
                7: [  # 7月
                    {"name": "青海湖", "city": "西宁", "description": "高原明珠，油菜花海", "icon": "🌻", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://source.unsplash.com/400x300/?qinghai,lake,rapeseed,flower,plateau", "reason": "夏季油菜花盛开"},
                    {"name": "内蒙古草原", "city": "呼和浩特", "description": "辽阔草原，牧歌悠扬", "icon": "🐎", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://source.unsplash.com/400x300/?inner,mongolia,grassland,horse,prairie", "reason": "草原最美的季节"},
                    {"name": "新疆伊犁", "city": "伊宁", "description": "薰衣草花海，天山雪峰", "icon": "💜", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://source.unsplash.com/400x300/?xinjiang,lavender,field,tianshan,mountain", "reason": "薰衣草花期"}
                ]
            },

            # 国外推荐（按月份）
            "international": {
                1: [  # 1月
                    {"name": "日本北海道", "city": "札幌", "description": "雪国风情，温泉滑雪", "icon": "🎿", "color": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "image": "https://source.unsplash.com/400x300/?hokkaido,snow,skiing,onsen,winter", "reason": "冬季雪景和温泉"},
                    {"name": "泰国清迈", "city": "清迈", "description": "古城文化，热带风情", "icon": "🛕", "color": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "image": "https://source.unsplash.com/400x300/?chiang,mai,temple,thailand,buddhist", "reason": "凉季气候宜人"},
                    {"name": "新西兰南岛", "city": "皇后镇", "description": "纯净自然，户外天堂", "icon": "🏔️", "color": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "image": "https://source.unsplash.com/400x300/?queenstown,new,zealand,mountain,lake", "reason": "南半球夏季"}
                ],
                7: [  # 7月
                    {"name": "法国普罗旺斯", "city": "阿维尼翁", "description": "薰衣草田，浪漫法式", "icon": "🌾", "color": "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)", "image": "https://source.unsplash.com/400x300/?provence,lavender,field,france,purple", "reason": "薰衣草盛开季节"},
                    {"name": "挪威峡湾", "city": "卑尔根", "description": "壮美峡湾，午夜阳光", "icon": "⛵", "color": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "image": "https://source.unsplash.com/400x300/?norway,fjord,bergen,waterfall,mountain", "reason": "白夜和峡湾最美时节"},
                    {"name": "土耳其卡帕多奇亚", "city": "格雷梅", "description": "奇特地貌，热气球之旅", "icon": "🎈", "color": "linear-gradient(135deg, #fa709a 0%, #fee140 100%)", "image": "https://source.unsplash.com/400x300/?cappadocia,hot,air,balloon,turkey", "reason": "夏季天气稳定"}
                ]
            }
        }

        # 获取周边推荐（智能匹配城市）
        # 城市名称映射表（支持中英文匹配）
        city_mapping = {
            "重庆": "Chongqing",
            "chongqing": "Chongqing",
            "成都": "Chengdu",
            "chengdu": "Chengdu",
            "西安": "Xi'an",
            "xian": "Xi'an",
            "xi'an": "Xi'an",
            "武汉": "Wuhan",
            "wuhan": "Wuhan",
            "南京": "Nanjing",
            "nanjing": "Nanjing",
            "天津": "Tianjin",
            "tianjin": "Tianjin",
            "深圳": "Shenzhen",
            "shenzhen": "Shenzhen",
            "北京": "Beijing",
            "beijing": "Beijing",
            "上海": "Shanghai",
            "shanghai": "Shanghai",
            "广州": "Guangzhou",
            "guangzhou": "Guangzhou",
            "杭州": "Hangzhou",
            "hangzhou": "Hangzhou",
            "东京": "Tokyo",
            "tokyo": "Tokyo"
        }

        # 标准化城市名称
        city_key = city_mapping.get(city.lower(), city)

        nearby = travel_data["nearby"].get(city_key)
        if not nearby:
            # 尝试匹配相似城市名
            city_lower = city.lower()
            for key in travel_data["nearby"].keys():
                if key.lower() in city_lower or city_lower in key.lower():
                    nearby = travel_data["nearby"][key]
                    break

            # 如果还是没找到，使用默认推荐
            if not nearby:
                nearby = travel_data["nearby"]["Beijing"]

        # 获取国内推荐（根据月份，如果没有对应月份则使用7月）
        domestic = travel_data["domestic"].get(month, travel_data["domestic"][7])

        # 获取国外推荐（根据月份，如果没有对应月份则使用7月）
        international = travel_data["international"].get(month, travel_data["international"][7])

        # 为国内游和出境游添加机票价格信息
        # 标准化出发城市名称
        origin_city = self.normalize_city_name(city)

        for item in domestic:
            flight_info = self.get_flight_prices(origin_city, item["city"], is_international=False)
            item["flight_price"] = flight_info

        for item in international:
            flight_info = self.get_flight_prices(origin_city, item["city"], is_international=True)
            item["flight_price"] = flight_info

        return {
            "nearby": nearby,
            "domestic": domestic,
            "international": international,
            "month": month
        }

    def get_fallback_image_url(self, category, name):
        """获取备用图片URL"""
        # 使用更稳定的图片服务作为备用
        fallback_images = {
            # 周边景点备用图片
            "承德避暑山庄": "https://source.unsplash.com/400x200/?palace,garden",
            "天津海河": "https://source.unsplash.com/400x200/?river,city",
            "秦皇岛北戴河": "https://source.unsplash.com/400x200/?beach,seaside",
            "苏州园林": "https://source.unsplash.com/400x200/?garden,traditional",
            "杭州西湖": "https://source.unsplash.com/400x200/?lake,scenic",
            "南京夫子庙": "https://source.unsplash.com/400x200/?temple,traditional",

            # 国内景点备用图片
            "哈尔滨冰雪大世界": "https://source.unsplash.com/400x200/?ice,snow",
            "海南三亚": "https://source.unsplash.com/400x200/?tropical,beach",
            "云南大理": "https://source.unsplash.com/400x200/?mountain,ancient",
            "青海湖": "https://source.unsplash.com/400x200/?lake,plateau",
            "内蒙古草原": "https://source.unsplash.com/400x200/?grassland,prairie",
            "新疆伊犁": "https://source.unsplash.com/400x200/?lavender,mountain",

            # 国外景点备用图片
            "日本北海道": "https://source.unsplash.com/400x200/?japan,snow",
            "泰国清迈": "https://source.unsplash.com/400x200/?thailand,temple",
            "新西兰南岛": "https://source.unsplash.com/400x200/?newzealand,nature",
            "法国普罗旺斯": "https://source.unsplash.com/400x200/?provence,lavender",
            "挪威峡湾": "https://source.unsplash.com/400x200/?norway,fjord",
            "土耳其卡帕多奇亚": "https://source.unsplash.com/400x200/?cappadocia,balloon"
        }

        return fallback_images.get(name, f"https://source.unsplash.com/400x200/?{category},travel")

    def normalize_city_name(self, city_name):
        """标准化城市名称，用于机票价格查询"""
        # 城市名称映射表
        city_mapping = {
            # 英文到中文
            "Beijing": "北京",
            "Shanghai": "上海",
            "Guangzhou": "广州",
            "Shenzhen": "深圳",
            "Hangzhou": "杭州",
            "Chengdu": "成都",
            "Xi'an": "西安",
            "Wuhan": "武汉",
            "Nanjing": "南京",
            "Chongqing": "重庆",
            "Tianjin": "天津",
            "Qingdao": "青岛",
            "Dalian": "大连",
            "Xiamen": "厦门",
            "Kunming": "昆明",
            "Urumqi": "乌鲁木齐",
            "Lhasa": "拉萨",
            "Hohhot": "呼和浩特",
            "Taiyuan": "太原",
            "Shijiazhuang": "石家庄",

            # 国际城市保持英文
            "Tokyo": "Tokyo",
            "Osaka": "Osaka",
            "Seoul": "Seoul",
            "Bangkok": "Bangkok",
            "Singapore": "Singapore",
            "Kuala Lumpur": "Kuala Lumpur",
            "Manila": "Manila",
            "Jakarta": "Jakarta",
            "Ho Chi Minh City": "Ho Chi Minh City",
            "Phnom Penh": "Phnom Penh"
        }

        # 如果在映射表中，返回映射后的名称
        if city_name in city_mapping:
            return city_mapping[city_name]

        # 如果不在映射表中，返回原名称
        return city_name

    def get_flight_prices(self, origin_city, destination_city, is_international=False):
        """获取机票价格信息"""
        import random
        import datetime

        # 城市到机场代码的映射
        city_to_airport = {
            # 国内城市（中文）
            "北京": "PEK",
            "上海": "PVG",
            "广州": "CAN",
            "深圳": "SZX",
            "杭州": "HGH",
            "成都": "CTU",
            "西安": "XIY",
            "武汉": "WUH",
            "南京": "NKG",
            "重庆": "CKG",
            "天津": "TSN",
            "青岛": "TAO",
            "大连": "DLC",
            "厦门": "XMN",
            "昆明": "KMG",

            # 国内城市（英文）
            "Beijing": "PEK",
            "Shanghai": "PVG",
            "Guangzhou": "CAN",
            "Shenzhen": "SZX",
            "Hangzhou": "HGH",
            "Chengdu": "CTU",
            "Xi'an": "XIY",
            "Wuhan": "WUH",
            "Nanjing": "NKG",
            "Chongqing": "CKG",

            # 国内目的地
            "哈尔滨": "HRB",
            "三亚": "SYX",
            "大理": "DLU",
            "西宁": "XNN",
            "呼和浩特": "HET",
            "伊宁": "YIN",

            # 国际目的地
            "札幌": "CTS",
            "清迈": "CNX",
            "皇后镇": "ZQN",
            "阿维尼翁": "AVN",
            "卑尔根": "BGO",
            "格雷梅": "NAV",

            # 国际城市
            "Tokyo": "NRT",
            "Osaka": "KIX",
            "Seoul": "ICN",
            "Bangkok": "BKK",
            "Singapore": "SIN"
        }

        # 获取机场代码
        origin_code = city_to_airport.get(origin_city, "PEK")
        dest_code = city_to_airport.get(destination_city, "PVG")

        # 模拟机票价格数据（根据出发城市和目的地计算）
        def calculate_base_price(origin, destination, is_intl):
            # 国内航线基础价格（从主要城市出发）
            domestic_prices = {
                ("北京", "哈尔滨"): 800, ("上海", "哈尔滨"): 1200, ("广州", "哈尔滨"): 1800,
                ("北京", "三亚"): 1500, ("上海", "三亚"): 1300, ("广州", "三亚"): 800,
                ("北京", "大理"): 1400, ("上海", "大理"): 1200, ("广州", "大理"): 600,
                ("北京", "西宁"): 900, ("上海", "西宁"): 1100, ("广州", "西宁"): 1400,
                ("北京", "呼和浩特"): 500, ("上海", "呼和浩特"): 800, ("广州", "呼和浩特"): 1200,
                ("北京", "伊宁"): 1800, ("上海", "伊宁"): 2200, ("广州", "伊宁"): 2500
            }

            # 国际航线基础价格
            international_prices = {
                ("北京", "札幌"): 2800, ("上海", "札幌"): 2500, ("广州", "札幌"): 3200,
                ("北京", "清迈"): 2200, ("上海", "清迈"): 2000, ("广州", "清迈"): 1500,
                ("北京", "皇后镇"): 7500, ("上海", "皇后镇"): 7200, ("广州", "皇后镇"): 7800,
                ("北京", "阿维尼翁"): 5800, ("上海", "阿维尼翁"): 5500, ("广州", "阿维尼翁"): 6200,
                ("北京", "卑尔根"): 6500, ("上海", "卑尔根"): 6200, ("广州", "卑尔根"): 6800,
                ("北京", "格雷梅"): 4200, ("上海", "格雷梅"): 4000, ("广州", "格雷梅"): 4500
            }

            if is_intl:
                return international_prices.get((origin, destination), 5000)
            else:
                return domestic_prices.get((origin, destination), 1200)

        base_price = calculate_base_price(origin_city, destination_city, is_international)

        # 生成未来30天的价格数据
        today = datetime.date.today()
        price_data = []

        for i in range(30):
            date = today + datetime.timedelta(days=i)

            # 模拟价格波动（周末和节假日价格较高）
            price_multiplier = 1.0

            # 周末价格上涨
            if date.weekday() >= 5:  # 周六、周日
                price_multiplier += 0.2

            # 随机波动
            price_multiplier += random.uniform(-0.3, 0.4)
            price_multiplier = max(0.6, min(1.8, price_multiplier))  # 限制在0.6-1.8倍之间

            final_price = int(base_price * price_multiplier)

            price_data.append({
                "date": date.strftime("%Y-%m-%d"),
                "price": final_price,
                "day_of_week": date.strftime("%A"),
                "is_weekend": date.weekday() >= 5
            })

        # 找出最便宜的价格
        cheapest = min(price_data, key=lambda x: x["price"])

        # 计算平均价格
        avg_price = int(sum(p["price"] for p in price_data) / len(price_data))

        return {
            "origin": origin_city,
            "destination": destination_city,
            "origin_airport": origin_code,
            "destination_airport": dest_code,
            "cheapest_price": cheapest["price"],
            "cheapest_date": cheapest["date"],
            "average_price": avg_price,
            "currency": "CNY",
            "search_period": "30天",
            "price_trend": "stable" if abs(cheapest["price"] - avg_price) < avg_price * 0.2 else "volatile",
            "savings": max(0, avg_price - cheapest["price"]),
            "all_prices": price_data[:7]  # 返回前7天的价格供图表显示
        }

# 创建StockApp实例
stock_app = StockApp()

@app.route('/')
def index():
    return render_template('stock_index.html')

@app.route('/simple')
def simple_index():
    return render_template('stock_simple.html')

@app.route('/api/search/<query>')
def search_stocks_api(query):
    """股票搜索API"""
    try:
        results = stock_app.search_stocks(query)
        return jsonify({
            'success': True,
            'results': results
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stocks/popular')
def get_popular_stocks():
    """API端点：获取热门股票列表"""
    try:
        popular_list = []
        for symbol, info in stock_app.popular_stocks.items():
            # 获取简化的股票数据
            stock_data = stock_app.get_stock_data(symbol)
            if stock_data:
                popular_list.append({
                    'symbol': symbol,
                    'name': info['name'],
                    'sector': info['sector'],
                    'current_price': stock_data['current_price'],
                    'price_change': stock_data['price_change'],
                    'price_change_percent': stock_data['price_change_percent']
                })

        return jsonify({
            'popular_stocks': popular_list,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

    except Exception as e:
        print(f"获取热门股票失败: {e}")
        return jsonify({'error': '获取热门股票失败'}), 500

@app.route('/api/stock/<symbol>')
def get_stock_analysis_api(symbol):
    """API端点：获取股票详细分析（支持时间段参数）"""
    period = request.args.get('period', '1mo')
    try:
        # 获取股票数据
        stock_data = stock_app.get_stock_data(symbol.upper(), period)
        if not stock_data:
            return jsonify({'error': f'无法获取股票 {symbol} 的数据'}), 404

        # 获取新闻数据
        news_data = stock_app.get_stock_news(symbol.upper(), stock_data['name'])

        # 分析新闻对股价的影响
        news_impact_analysis = stock_app.analyze_news_impact(stock_data, news_data)

        # 获取完整分析（如果需要）
        analysis = stock_app.get_stock_analysis(symbol.upper())

        return jsonify({
            'success': True,
            'stock_data': stock_data,
            'news': news_data,
            'news_impact_analysis': news_impact_analysis,
            'analysis': analysis
        })

    except Exception as e:
        print(f"获取股票分析失败 {symbol}: {e}")
        return jsonify({'error': '股票分析失败'}), 500

@app.route('/api/stocks/search/<query>')
def search_stocks(query):
    """API端点：搜索股票"""
    try:
        query = query.upper().strip()
        results = []

        # 在热门股票中搜索
        for symbol, info in stock_app.popular_stocks.items():
            if (query in symbol or
                query.lower() in info['name'].lower() or
                query.lower() in info['sector'].lower()):

                # 获取基本股票数据
                stock_data = stock_app.get_stock_data(symbol)
                if stock_data:
                    results.append({
                        'symbol': symbol,
                        'name': info['name'],
                        'sector': info['sector'],
                        'current_price': stock_data['current_price'],
                        'price_change': stock_data['price_change'],
                        'price_change_percent': stock_data['price_change_percent'],
                        'match_type': 'symbol' if query in symbol else 'name'
                    })

        # 按匹配类型和相关性排序
        results.sort(key=lambda x: (x['match_type'] == 'name', x['symbol']))

        return jsonify({
            'query': query,
            'results': results[:10],  # 最多返回10个结果
            'total_found': len(results),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

    except Exception as e:
        print(f"股票搜索失败: {e}")
        return jsonify({'error': '股票搜索失败'}), 500

@app.route('/api/stocks/trending')
def get_trending_stocks():
    """API端点：获取趋势股票"""
    try:
        trending_stocks = []

        # 获取一些热门股票的数据
        trending_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX']

        for symbol in trending_symbols:
            stock_data = stock_app.get_stock_data(symbol)
            if stock_data:
                # 计算趋势分数（基于价格变化和成交量）
                price_change = stock_data['price_change_percent']
                volume_ratio = stock_data['volume'] / stock_data['avg_volume'] if stock_data['avg_volume'] > 0 else 1

                trend_score = abs(price_change) * 0.7 + (volume_ratio - 1) * 30

                trending_stocks.append({
                    'symbol': symbol,
                    'name': stock_data['name'],
                    'current_price': stock_data['current_price'],
                    'price_change': stock_data['price_change'],
                    'price_change_percent': stock_data['price_change_percent'],
                    'volume': stock_data['volume'],
                    'trend_score': round(trend_score, 2),
                    'sector': stock_data['sector']
                })

        # 按趋势分数排序
        trending_stocks.sort(key=lambda x: x['trend_score'], reverse=True)

        return jsonify({
            'trending_stocks': trending_stocks,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

    except Exception as e:
        print(f"获取趋势股票失败: {e}")
        return jsonify({'error': '获取趋势股票失败'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
