<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机票价格对比测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .city-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .city-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }
        .city-name {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .flight-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .route {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 8px;
        }
        .price-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .price {
            font-size: 1.2em;
            font-weight: bold;
            color: #28a745;
        }
        .savings {
            font-size: 0.9em;
            color: #6c757d;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>✈️ 机票价格对比测试</h1>
    <p>测试不同出发城市到相同目的地的机票价格差异</p>
    
    <div style="text-align: center; margin: 20px 0;">
        <button onclick="loadPriceComparison()">🔄 加载价格对比</button>
        <button onclick="clearResults()">🗑️ 清空结果</button>
    </div>
    
    <div id="comparisonResults" class="comparison-grid"></div>

    <script>
        async function loadPriceComparison() {
            const resultsContainer = document.getElementById('comparisonResults');
            resultsContainer.innerHTML = '<div class="loading">🔄 正在加载机票价格数据...</div>';
            
            const cities = ['Beijing', 'Shanghai', 'Guangzhou'];
            const results = [];
            
            try {
                // 并行获取所有城市的数据
                const promises = cities.map(city => 
                    fetch(`http://localhost:8080/api/weather/${city}`)
                        .then(response => response.json())
                        .then(data => ({ city, data }))
                );
                
                const responses = await Promise.all(promises);
                
                // 处理数据
                responses.forEach(({ city, data }) => {
                    if (data.travel_recommendations) {
                        const domestic = data.travel_recommendations.domestic || [];
                        const international = data.travel_recommendations.international || [];
                        
                        results.push({
                            city: city,
                            cityName: getCityName(city),
                            flights: [...domestic, ...international].filter(item => item.flight_price)
                        });
                    }
                });
                
                displayComparison(results);
                
            } catch (error) {
                resultsContainer.innerHTML = `<div class="loading">❌ 加载失败: ${error.message}</div>`;
            }
        }
        
        function getCityName(city) {
            const names = {
                'Beijing': '北京',
                'Shanghai': '上海',
                'Guangzhou': '广州'
            };
            return names[city] || city;
        }
        
        function displayComparison(results) {
            const container = document.getElementById('comparisonResults');
            
            if (results.length === 0) {
                container.innerHTML = '<div class="loading">❌ 没有获取到数据</div>';
                return;
            }
            
            let html = '';
            
            results.forEach(cityData => {
                html += `
                    <div class="city-card">
                        <div class="city-header">
                            <div class="city-name">${cityData.cityName} 出发</div>
                            <div style="color: #6c757d; font-size: 0.9em;">${cityData.flights.length} 条航线</div>
                        </div>
                `;
                
                cityData.flights.forEach(flight => {
                    const fp = flight.flight_price;
                    const savingsText = fp.savings > 0 ? `节省 ¥${fp.savings}` : '价格稳定';
                    
                    html += `
                        <div class="flight-item">
                            <div class="route">${fp.origin} → ${fp.destination}</div>
                            <div class="price-info">
                                <div class="price">¥${fp.cheapest_price}</div>
                                <div class="savings">${savingsText}</div>
                            </div>
                        </div>
                    `;
                });
                
                html += '</div>';
            });
            
            container.innerHTML = html;
            
            // 显示统计信息
            displayStatistics(results);
        }
        
        function displayStatistics(results) {
            console.log('=== 机票价格统计 ===');
            
            // 按目的地分组统计
            const destinationStats = {};
            
            results.forEach(cityData => {
                cityData.flights.forEach(flight => {
                    const dest = flight.flight_price.destination;
                    if (!destinationStats[dest]) {
                        destinationStats[dest] = [];
                    }
                    destinationStats[dest].push({
                        origin: flight.flight_price.origin,
                        price: flight.flight_price.cheapest_price
                    });
                });
            });
            
            // 显示价格对比
            Object.keys(destinationStats).forEach(dest => {
                const prices = destinationStats[dest];
                if (prices.length > 1) {
                    console.log(`\n目的地: ${dest}`);
                    prices.sort((a, b) => a.price - b.price);
                    prices.forEach((item, index) => {
                        const label = index === 0 ? '(最便宜)' : `(+¥${item.price - prices[0].price})`;
                        console.log(`  ${item.origin}: ¥${item.price} ${label}`);
                    });
                }
            });
        }
        
        function clearResults() {
            document.getElementById('comparisonResults').innerHTML = '';
        }
        
        // 页面加载完成后自动加载数据
        window.addEventListener('load', function() {
            setTimeout(loadPriceComparison, 1000);
        });
    </script>
</body>
</html>
